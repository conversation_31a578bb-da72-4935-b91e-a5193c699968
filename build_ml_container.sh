#!/bin/bash

# Build script for ML training container with CUDA 12.6
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="ml-training-cuda126"
CONTAINER_NAME="junkim100-ml-cuda126"

echo -e "${BLUE}🚀 Building ML Training Container with CUDA 12.6${NC}"
echo -e "${BLUE}================================================${NC}"

# Check if NVIDIA Docker is available
echo -e "${BLUE}Checking NVIDIA Docker support...${NC}"
if ! docker info | grep -q nvidia; then
    echo -e "${RED}❌ NVIDIA Docker support not found!${NC}"
    echo "Please install nvidia-container-toolkit"
    exit 1
fi

# Check GPU availability
echo -e "${BLUE}Checking GPU availability...${NC}"
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits
else
    echo -e "${YELLOW}⚠️  nvidia-smi not found. GPU support may not work.${NC}"
fi

# Build the Docker image
echo -e "${BLUE}Building Docker image: ${IMAGE_NAME}${NC}"
docker build -f Dockerfile.ml -t ${IMAGE_NAME} .

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Image built successfully!${NC}"
else
    echo -e "${RED}❌ Image build failed!${NC}"
    exit 1
fi

# Stop and remove existing container if it exists
if docker ps -q -f name=${CONTAINER_NAME} | grep -q .; then
    echo -e "${YELLOW}Stopping existing container...${NC}"
    docker stop ${CONTAINER_NAME}
fi

if docker ps -aq -f name=${CONTAINER_NAME} | grep -q .; then
    echo -e "${YELLOW}Removing existing container...${NC}"
    docker rm ${CONTAINER_NAME}
fi

# Run the container
echo -e "${BLUE}Starting container: ${CONTAINER_NAME}${NC}"
docker run -d \
    --name ${CONTAINER_NAME} \
    --gpus '"device=0,1,2,3"' \
    --shm-size=16g \
    --memory=64g \
    -p 8888:8888 \
    -p 6006:6006 \
    -p 7860:7860 \
    -v "$(pwd)":/workspace/project \
    -v ~/.cache/huggingface:/home/<USER>/.cache/huggingface \
    -v ~/.wandb:/home/<USER>/.wandb \
    -w /workspace/project \
    -e NVIDIA_VISIBLE_DEVICES=0,1,2,3 \
    -e CUDA_VISIBLE_DEVICES=0,1,2,3 \
    --restart unless-stopped \
    ${IMAGE_NAME} \
    sleep infinity

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Container started successfully!${NC}"
    echo -e "${BLUE}📋 Container Details:${NC}"
    echo -e "   Name: ${CONTAINER_NAME}"
    echo -e "   Image: ${IMAGE_NAME}"
    echo -e "   GPUs: 0,1,2,3"
    echo -e "   Ports: 8888 (Jupyter), 6006 (TensorBoard), 7860 (Gradio)"
    echo ""
    echo -e "${BLUE}🔧 To enter the container:${NC}"
    echo -e "   docker exec -it ${CONTAINER_NAME} bash"
    echo ""
    echo -e "${BLUE}📦 Package Versions:${NC}"
    docker exec ${CONTAINER_NAME} python -c "
import torch, transformers, deepspeed, numpy
print(f'   PyTorch: {torch.__version__}')
print(f'   Transformers: {transformers.__version__}')
print(f'   DeepSpeed: {deepspeed.__version__}')
print(f'   NumPy: {numpy.__version__}')
print(f'   CUDA Available: {torch.cuda.is_available()}')
print(f'   GPU Count: {torch.cuda.device_count()}')
"
else
    echo -e "${RED}❌ Container failed to start!${NC}"
    exit 1
fi

echo -e "${GREEN}🎉 Setup complete! Your ML environment is ready.${NC}"
