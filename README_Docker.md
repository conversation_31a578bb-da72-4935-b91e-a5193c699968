# ML Training Environment with CUDA 12.6

This Docker setup provides a complete machine learning training environment with CUDA 12.6 support and the specific package versions you requested.

## 📦 Package Versions

- **PyTorch**: 2.7.0 (with CUDA 12.6)
- **Transformers**: 4.52.3
- **DeepSpeed**: 0.16.9
- **NumPy**: 1.26.3

## 🚀 Quick Start

### Prerequisites

1. **NVIDIA Docker Support**: Install nvidia-container-toolkit
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
   curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
     sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
     sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list
   sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
   sudo systemctl restart docker
   ```

2. **Docker & Docker Compose**: Ensure you have Docker and Docker Compose installed

### Build and Run

```bash
# Method 1: Using the setup script (Recommended)
./docker_setup.sh build    # Build the image
./docker_setup.sh run      # Start the container
./docker_setup.sh exec     # Enter the container

# Method 2: Using Docker Compose
docker-compose up -d        # Start in background
docker-compose exec ml-training bash  # Enter container

# Method 3: Manual Docker commands
docker build -t ml-training:cuda12.6 .
docker run -d --name ml-training-env --gpus '"device=0,1,2,3"' \
  -v $(pwd):/workspace/translation_it \
  ml-training:cuda12.6 sleep infinity
docker exec -it ml-training-env bash
```

## 🛠️ Setup Script Commands

The `docker_setup.sh` script provides convenient commands:

```bash
./docker_setup.sh build    # Build Docker image
./docker_setup.sh run      # Start container with GPU support
./docker_setup.sh exec     # Enter running container
./docker_setup.sh stop     # Stop container
./docker_setup.sh logs     # View container logs
./docker_setup.sh status   # Check container/image status
./docker_setup.sh clean    # Remove container and image
```

## 🔧 Configuration

### GPU Access
- **Default**: Uses GPUs 0-3 (as requested)
- **Modify**: Edit `NVIDIA_VISIBLE_DEVICES` in docker-compose.yml or docker_setup.sh

### Environment Variables
Create a `.env` file for sensitive variables:
```bash
# .env file
WANDB_API_KEY=your_wandb_key_here
HF_TOKEN=your_huggingface_token_here
```

### Volume Mounts
- **Project files**: `./` → `/workspace/translation_it`
- **HuggingFace cache**: `~/.cache/huggingface` → `/home/<USER>/.cache/huggingface`
- **WandB cache**: `~/.wandb` → `/home/<USER>/.wandb`

## 🧪 Testing the Environment

Once inside the container, verify the setup:

```bash
# Check PyTorch and CUDA
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}')"

# Check other packages
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
python -c "import deepspeed; print(f'DeepSpeed: {deepspeed.__version__}')"
python -c "import numpy; print(f'NumPy: {numpy.__version__}')"

# Test GPU access
nvidia-smi
```

## 🏃‍♂️ Running Your Training

```bash
# Inside the container
cd /workspace/translation_it

# Run your training script
deepspeed --num_gpus=4 train.py

# Or test translation tokens
python test_translation_tokens.py
```

## 📊 Monitoring

### Jupyter Notebook
```bash
# Inside container
jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root
# Access at: http://localhost:8888
```

### TensorBoard
```bash
# Inside container
tensorboard --logdir=./logs --host=0.0.0.0 --port=6006
# Access at: http://localhost:6006
```

## 🐛 Troubleshooting

### GPU Not Detected
```bash
# Check NVIDIA Docker support
docker run --rm --gpus all nvidia/cuda:12.6-base-ubuntu22.04 nvidia-smi

# Check container GPU access
docker exec -it ml-training-env nvidia-smi
```

### Memory Issues
```bash
# Increase shared memory if needed
docker run --shm-size=32g ...  # Increase from 16g to 32g
```

### Permission Issues
```bash
# The container runs as 'mluser' (non-root)
# If you need root access:
docker exec -it --user root ml-training-env bash
```

## 🔄 Updates

To update packages:
```bash
# Rebuild image with new versions
./docker_setup.sh clean
# Edit Dockerfile with new versions
./docker_setup.sh build
```

## 📝 Notes

- **Memory**: Container limited to 64GB RAM (adjust in docker-compose.yml)
- **GPUs**: Configured for GPUs 0-3 (modify as needed)
- **Storage**: Project files are mounted, so changes persist
- **Cache**: HuggingFace and WandB caches are preserved between runs

## 🆘 Support

If you encounter issues:
1. Check `./docker_setup.sh status` for container status
2. View logs with `./docker_setup.sh logs`
3. Verify GPU support with `nvidia-smi`
4. Ensure Docker has GPU access with `docker run --rm --gpus all nvidia/cuda:12.6-base-ubuntu22.04 nvidia-smi`
