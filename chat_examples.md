# 🤖 Interactive Chat CLI - Usage Examples

## 📋 Basic Usage

```bash
# Chat with your trained translation model
python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66

# Chat with a HuggingFace model
python chat_cli.py microsoft/DialoGPT-medium

# Chat with custom system prompt
python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66 --system-prompt "You are a translation assistant. Always use <translation> tags for translations."

# Use 4-bit quantization for memory efficiency
python chat_cli.py meta-llama/Meta-Llama-3.1-8B --4bit

# Set custom max length
python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66 --max-length 4096
```

## 🎯 Testing Your Translation Model

```bash
# Start chat with translation-specific system prompt
python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66 \
  --system-prompt "You are a translation assistant. When asked to translate, use <translation> tags around your translation."
```

Then in the chat:
```
👤 You: Translate "Hello world" to Italian
🤖 Assistant: <translation><PERSON><PERSON><PERSON> mondo</translation>

👤 You: Translate "How are you?" to Italian  
🤖 Assistant: <translation>Come stai?</translation>
```

## 🔧 Interactive Commands

While chatting, you can use these commands:

- **`quit`**, **`exit`**, or **`q`** - Exit the chat
- **`reset`** - Clear conversation history
- **`history`** - Show full conversation history
- **`Ctrl+C`** - Force exit

## 📊 Model Types Supported

### ✅ Base Models
- Any HuggingFace model (e.g., `meta-llama/Meta-Llama-3.1-8B`)
- Local model directories with `config.json` and model files

### ✅ LoRA Adapters  
- Local LoRA adapter directories (auto-detects `adapter_config.json`)
- Automatically loads base model and applies adapter
- Handles extended vocabularies (like your translation tokens)

## 🚀 Advanced Features

### Memory Optimization
```bash
# Use 4-bit quantization for large models
python chat_cli.py meta-llama/Meta-Llama-3.1-70B --4bit
```

### Custom System Prompts
```bash
# Translation assistant
python chat_cli.py ./your-model --system-prompt "You are a professional translator. Always provide accurate translations."

# Code assistant  
python chat_cli.py ./your-model --system-prompt "You are a helpful coding assistant. Provide clear, well-commented code examples."

# Creative writing
python chat_cli.py ./your-model --system-prompt "You are a creative writing assistant. Help with storytelling and creative ideas."
```

## 🔍 Troubleshooting

### Model Loading Issues
```bash
# Check if path exists
ls -la ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66/

# Verify required files for LoRA
ls -la ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66/adapter_config.json
```

### Memory Issues
```bash
# Use quantization
python chat_cli.py ./your-model --4bit

# Reduce max length
python chat_cli.py ./your-model --max-length 1024
```

### CUDA Issues
```bash
# Check CUDA availability
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

## 📝 Example Session

```
🚀 Interactive Chat CLI
============================================================
🤖 Loading model from: ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66
💭 System prompt: You are a helpful assistant
📏 Max length: 2048
🔧 Detected LoRA adapter
📦 Base model: meta-llama/Meta-Llama-3.1-8B
🔧 Resizing embeddings: 128256 → 128258
🎯 LoRA adapter loaded
✅ Model loaded successfully!
💬 Type 'quit', 'exit', or 'q' to end the conversation
🔄 Type 'reset' to clear conversation history
📋 Type 'history' to see conversation history
============================================================

👤 You: Hello! Can you translate "Good morning" to Italian?

🤖 Assistant: <translation>Buongiorno</translation>

👤 You: What about "Thank you very much"?

🤖 Assistant: <translation>Grazie mille</translation>

👤 You: reset
🔄 Conversation history reset

👤 You: quit
👋 Goodbye!
```

## 🎯 Perfect for Testing Your Model!

This CLI is ideal for:
- ✅ Testing your trained translation model
- ✅ Verifying translation token usage
- ✅ Interactive model evaluation
- ✅ Quick model comparisons
- ✅ Debugging model behavior
