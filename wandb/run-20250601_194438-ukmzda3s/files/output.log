Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 119.67it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/Lima-X-Processed with max_length=9064
Process 0: Dataset loaded - Train: 1030, Val: 52
Dataset filtering results:
  Train: 1030 -> 1030 (100.0% retained)
  Val: 52 -> 52 (100.0% retained)
  Sequence lengths - Min: 77, Max: 9064, Mean: 1517.7
Process 0: Dataset prepared and synchronized
[2025-06-01 19:45:25,137] [INFO] [comm.py:669:init_distributed] cdb=None
🔧 ChunkedSequenceDataCollator initialized:
   Max sequence length: 9064
   Chunk size: 4096
   Overlap: 512
   Effective chunks per 9k sequence: 3

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:743: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training with sequence chunking on 4 GPUs...
   - Max sequence length: 9064 (chunked into 4k pieces)
   - Chunk size: 4096 tokens with 512 token overlap
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Base effective batch size: 32
   - Note: Actual batch size will be ~3x higher due to chunking
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Loading extension module cpu_adam...
Time to load cpu_adam op: 2.461778402328491 seconds
Parameter Offload: Total persistent parameters: 1314816 in 129 params
[2025-06-01 19:46:01,556] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
 51%|█████████████████████████████████████████████▍                                            | 100/198 [44:19<36:59, 22.65s/it]/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/utils/save_and_load.py:250: UserWarning: Setting `save_embedding_layers` to `True` as the embedding layer has been resized during finetuning.
{'loss': 4.3841, 'grad_norm': 15.115035057067871, 'learning_rate': 0.0, 'epoch': 0.03}
{'loss': 4.2218, 'grad_norm': 10.538762092590332, 'learning_rate': 6.989700043360187e-05, 'epoch': 0.16}
{'loss': 2.8634, 'grad_norm': 3.7469191551208496, 'learning_rate': 9.999999999999999e-05, 'epoch': 0.31}
{'loss': 1.8235, 'grad_norm': 1.1965335607528687, 'learning_rate': 0.0001, 'epoch': 0.47}
{'loss': 1.5067, 'grad_norm': 1.070713758468628, 'learning_rate': 0.0001, 'epoch': 0.62}
{'loss': 1.3786, 'grad_norm': 0.8797351717948914, 'learning_rate': 0.0001, 'epoch': 0.78}
[2025-06-01 19:57:56,284] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
[2025-06-01 19:58:50,197] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.2775, 'grad_norm': 0.6276625990867615, 'learning_rate': 0.0001, 'epoch': 0.93}
{'loss': 1.216, 'grad_norm': 0.4060195982456207, 'learning_rate': 0.0001, 'epoch': 1.06}
{'loss': 1.2042, 'grad_norm': 0.28143900632858276, 'learning_rate': 0.0001, 'epoch': 1.22}
{'loss': 1.1549, 'grad_norm': 0.23164102435112, 'learning_rate': 0.0001, 'epoch': 1.37}
{'loss': 1.1183, 'grad_norm': 0.22547908127307892, 'learning_rate': 0.0001, 'epoch': 1.53}
{'loss': 1.1296, 'grad_norm': 0.18721862137317657, 'learning_rate': 0.0001, 'epoch': 1.68}
{'loss': 1.0703, 'grad_norm': 0.16096094250679016, 'learning_rate': 0.0001, 'epoch': 1.84}
{'loss': 1.1074, 'grad_norm': 0.2067575305700302, 'learning_rate': 0.0001, 'epoch': 1.99}
{'loss': 1.0911, 'grad_norm': 0.18698015809059143, 'learning_rate': 0.0001, 'epoch': 2.12}
{'loss': 1.1054, 'grad_norm': 0.18081197142601013, 'learning_rate': 0.0001, 'epoch': 2.28}
{'loss': 1.0823, 'grad_norm': 0.18589383363723755, 'learning_rate': 0.0001, 'epoch': 2.43}
{'loss': 1.0436, 'grad_norm': 0.18841643631458282, 'learning_rate': 0.0001, 'epoch': 2.59}
{'loss': 1.0815, 'grad_norm': 0.1985272914171219, 'learning_rate': 0.0001, 'epoch': 2.74}
{'loss': 1.0284, 'grad_norm': 0.1803186982870102, 'learning_rate': 0.0001, 'epoch': 2.9}
{'loss': 1.0206, 'grad_norm': 0.3525048792362213, 'learning_rate': 0.0001, 'epoch': 3.03}
  warnings.warn(                                                                                                                 
{'eval_loss': 1.1047457456588745, 'eval_runtime': 4.401, 'eval_samples_per_second': 11.815, 'eval_steps_per_second': 0.454, 'epoch': 3.03}
100%|████████████████████████████████████████████████████████████████████████████████████████| 198/198 [1:28:52<00:00, 26.93s/it]
[2025-06-01 20:32:17,434] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.02, 'grad_norm': 0.18311189115047455, 'learning_rate': 0.0001, 'epoch': 3.19}
[2025-06-01 20:35:03,871] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 0.9866, 'grad_norm': 0.21433685719966888, 'learning_rate': 0.0001, 'epoch': 3.34}
[2025-06-01 20:35:55,357] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 0.9866, 'grad_norm': 0.264798641204834, 'learning_rate': 0.0001, 'epoch': 3.5}
{'loss': 1.0286, 'grad_norm': 0.2119109183549881, 'learning_rate': 0.0001, 'epoch': 3.65}
[2025-06-01 20:41:44,265] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.0112, 'grad_norm': 0.2208585888147354, 'learning_rate': 0.0001, 'epoch': 3.81}
[2025-06-01 20:43:00,648] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.0722, 'grad_norm': 0.28780484199523926, 'learning_rate': 0.0001, 'epoch': 3.96}
{'loss': 0.9487, 'grad_norm': 0.2877848744392395, 'learning_rate': 0.0001, 'epoch': 4.09}
{'loss': 0.9676, 'grad_norm': 0.26326391100883484, 'learning_rate': 0.0001, 'epoch': 4.25}
{'loss': 0.9806, 'grad_norm': 0.31173789501190186, 'learning_rate': 0.0001, 'epoch': 4.4}
{'loss': 0.9698, 'grad_norm': 0.2768949270248413, 'learning_rate': 0.0001, 'epoch': 4.56}
{'loss': 0.9624, 'grad_norm': 0.3014037311077118, 'learning_rate': 0.0001, 'epoch': 4.71}
{'loss': 0.9622, 'grad_norm': 0.29484841227531433, 'learning_rate': 0.0001, 'epoch': 4.87}
{'loss': 0.9815, 'grad_norm': 0.29753050208091736, 'learning_rate': 0.0001, 'epoch': 5.0}
{'loss': 0.9208, 'grad_norm': 0.3192203640937805, 'learning_rate': 0.0001, 'epoch': 5.16}
{'loss': 0.9068, 'grad_norm': 0.4117967188358307, 'learning_rate': 0.0001, 'epoch': 5.31}
{'loss': 0.9129, 'grad_norm': 0.4463805854320526, 'learning_rate': 0.0001, 'epoch': 5.47}
{'loss': 0.9159, 'grad_norm': 0.3515644669532776, 'learning_rate': 0.0001, 'epoch': 5.62}
{'loss': 0.9094, 'grad_norm': 0.3931083381175995, 'learning_rate': 0.0001, 'epoch': 5.78}
{'loss': 0.9026, 'grad_norm': 0.3744772672653198, 'learning_rate': 0.0001, 'epoch': 5.93}
{'train_runtime': 5330.5364, 'train_samples_per_second': 1.159, 'train_steps_per_second': 0.037, 'train_loss': 1.1967513392669986, 'init_mem_cpu_alloc_delta': 12288, 'init_mem_gpu_alloc_delta': 0, 'init_mem_cpu_peaked_delta': 0, 'init_mem_gpu_peaked_delta': 0, 'train_mem_cpu_alloc_delta': 2422431744, 'train_mem_gpu_alloc_delta': 53276672, 'train_mem_cpu_peaked_delta': 17252245504, 'train_mem_gpu_peaked_delta': 57769102848, 'before_init_mem_cpu': 6190936064, 'before_init_mem_gpu': 512, 'epoch': 6.0}

=== GPU Memory Usage After Training Complete ===
GPU 0: 0.05GB/79.14GB (0.1%) - Free: 79.09GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
✅ DeepSpeed training completed in 1.49 hours!
