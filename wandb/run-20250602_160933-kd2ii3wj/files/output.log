Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 130.07it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing multilingual dataset junkim100/multilingual_instruction_tuning_lima_bactrian with max_length=9064
Process 0: Supported languages: ['de', 'es', 'fr', 'it']
Process 0: Dataset loaded - Train: 54437, Val: 6805, Test: 6805
Tokenizing dataset: 100%|███████████████████████████████████████████████████████████████████████████| 6805/6805 [00:09<00:00, 690.99 examples/s]
Filter: 100%|██████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:02<00:00, 2741.30 examples/s]
Dataset filtering results:
  Train: 54437 -> 54220 (99.6% retained)
  Val: 6805 -> 6780 (99.6% retained)
  Sequence lengths - Min: 51, Max: 9020, Mean: 403.6
  ✅ Sample 0: Complete format with preserved ending
  ✅ Sample 1: Complete format with preserved ending
  ✅ Sample 2: Complete format with preserved ending
  ✅ Sample 3: Complete format with preserved ending
  ✅ Sample 4: Complete format with preserved ending
  ✅ Sample 5: Complete format with preserved ending
  ✅ Sample 6: Complete format with preserved ending
  ✅ Sample 7: Complete format with preserved ending
  ✅ Sample 8: Complete format with preserved ending
  ✅ Sample 9: Complete format with preserved ending
  📊 Validation sample (first 10): 10/10 complete sequences

📊 Training Steps Calculation:
   Dataset size: 54,220
   Epochs: 2
   Effective batch size: 32
   Base steps per epoch: 1,694
   Base total steps: 3,388
   Average sequence length: 1551 tokens
   Chunking multiplier: 1.0x
   Estimated total steps: 3,388

🔄 Checkpoint & Evaluation Schedule:
   Save every: 100 steps
   Evaluate every: 100 steps
   Log every: 20 steps
   Expected checkpoints: ~33
   Expected evaluations: ~33
   Keep last: 10 checkpoints
   Save frequency: every 3.0% of training
   Log frequency: every 0.6% of training
Process 0: Dataset prepared and synchronized
[2025-06-02 16:10:57,992] [INFO] [comm.py:669:init_distributed] cdb=None
🔧 ChunkedSequenceDataCollator initialized:
   Max sequence length: 9064
   Chunk size: 4096
   Overlap: 512
   Effective chunks per 9k sequence: 3
   🎯 FIXED: Will cycle through ALL chunks to ensure complete training

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:1119: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training with sequence chunking on 4 GPUs...
   - Max sequence length: 9064 (chunked into 4k pieces)
   - Chunk size: 4096 tokens with 512 token overlap
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Base effective batch size: 32
   - Note: Actual batch size will be ~3x higher due to chunking
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Detected CUDA files, patching ldflags
Emitting ninja build file /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126/cpu_adam/build.ninja...
/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/utils/cpp_extension.py:2356: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation.
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Building extension module cpu_adam...
Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)
Loading extension module cpu_adam...
Time to load cpu_adam op: 2.*************** seconds
Parameter Offload: Total persistent parameters: 1314816 in 129 params
[2025-06-02 16:11:45,216] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
  3%|██▉                                                                                                  | 100/3390 [48:08<22:01:21, 24.10s/it]/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/utils/save_and_load.py:250: UserWarning: Setting `save_embedding_layers` to `True` as the embedding layer has been resized during finetuning.
{'loss': 5.7487, 'grad_norm': 13.907805442810059, 'learning_rate': 0.0, 'epoch': 0.0}
{'loss': 3.8995, 'grad_norm': 2.254296064376831, 'learning_rate': 5.833040977508817e-05, 'epoch': 0.01}
{'loss': 1.7948, 'grad_norm': 0.9450002312660217, 'learning_rate': 7.182679576172462e-05, 'epoch': 0.02}
{'loss': 1.4338, 'grad_norm': 0.7378047108650208, 'learning_rate': 7.972167545916544e-05, 'epoch': 0.04}
{'loss': 1.3365, 'grad_norm': 0.7283475399017334, 'learning_rate': 8.532318174836105e-05, 'epoch': 0.05}
{'loss': 1.3234, 'grad_norm': 0.6210907101631165, 'learning_rate': 8.966804757690343e-05, 'epoch': 0.06}
  warnings.warn(                                                                                                                                
{'eval_loss': 1.49502432346344, 'eval_runtime': 313.9198, 'eval_samples_per_second': 21.598, 'eval_steps_per_second': 0.675, 'epoch': 0.06}
  3%|███▏                                                                                                 | 107/3390 [51:39<33:14:18, 36.45s/it]
