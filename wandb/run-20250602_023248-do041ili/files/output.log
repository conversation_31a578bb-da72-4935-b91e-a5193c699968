Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 132.72it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing multilingual dataset junkim100/multilingual_instruction_tuning_lima_bactrian with max_length=9064
Process 0: Supported languages: ['de', 'es', 'fr', 'it']
Process 0: Dataset loaded - Train: 54437, Val: 6805, Test: 6805
Formatting dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [00:01<00:00, 35277.89 examples/s]
Formatting dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:00<00:00, 35687.52 examples/s]
Tokenizing dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [00:36<00:00, 1494.09 examples/s]
Tokenizing dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:04<00:00, 1531.04 examples/s]
Tokenizing dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:04<00:00, 1563.63 examples/s]
Filter: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [00:18<00:00, 2991.04 examples/s]
Filter: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:02<00:00, 2961.38 examples/s]
Filter: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:02<00:00, 3038.63 examples/s]
Dataset filtering results:
  Train: 54437 -> 54220 (99.6% retained)
  Val: 6805 -> 6780 (99.6% retained)
  Sequence lengths - Min: 51, Max: 9064, Mean: 403.6
Process 0: Dataset prepared and synchronized
[2025-06-02 02:35:03,482] [INFO] [comm.py:669:init_distributed] cdb=None
🔧 ChunkedSequenceDataCollator initialized:
   Max sequence length: 9064
   Chunk size: 4096
   Overlap: 512
   Effective chunks per 9k sequence: 3

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:780: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training with sequence chunking on 4 GPUs...
   - Max sequence length: 9064 (chunked into 4k pieces)
   - Chunk size: 4096 tokens with 512 token overlap
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Base effective batch size: 32
   - Note: Actual batch size will be ~3x higher due to chunking
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Loading extension module cpu_adam...
Time to load cpu_adam op: 2.4118902683258057 seconds
Parameter Offload: Total persistent parameters: 1314816 in 129 params
[2025-06-02 02:35:37,235] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
  3%|██▉                                                                                                  | 100/3390 [37:19<18:05:36, 19.80s/it]/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/utils/save_and_load.py:250: UserWarning: Setting `save_embedding_layers` to `True` as the embedding layer has been resized during finetuning.
{'loss': 5.7477, 'grad_norm': 12.449291229248047, 'learning_rate': 0.0, 'epoch': 0.0}
{'loss': 5.4059, 'grad_norm': 10.512336730957031, 'learning_rate': 3.133763780181526e-05, 'epoch': 0.0}
{'loss': 4.4219, 'grad_norm': 9.367112159729004, 'learning_rate': 4.4834023788451716e-05, 'epoch': 0.01}
{'loss': 3.4167, 'grad_norm': 4.600208759307861, 'learning_rate': 5.2728903485892547e-05, 'epoch': 0.01}
{'loss': 2.6622, 'grad_norm': 2.3552050590515137, 'learning_rate': 5.833040977508817e-05, 'epoch': 0.01}
{'loss': 2.0824, 'grad_norm': 1.5560933351516724, 'learning_rate': 6.267527560363053e-05, 'epoch': 0.01}
{'loss': 1.8801, 'grad_norm': 1.1214321851730347, 'learning_rate': 6.622528947252899e-05, 'epoch': 0.02}
{'loss': 1.618, 'grad_norm': 1.2868268489837646, 'learning_rate': 6.922678343138837e-05, 'epoch': 0.02}
{'loss': 1.6211, 'grad_norm': 0.9000183343887329, 'learning_rate': 7.182679576172462e-05, 'epoch': 0.02}
{'loss': 1.5029, 'grad_norm': 1.33198881149292, 'learning_rate': 7.412016916996981e-05, 'epoch': 0.03}
{'loss': 1.4657, 'grad_norm': 0.9966739416122437, 'learning_rate': 7.617166159026699e-05, 'epoch': 0.03}
{'loss': 1.4152, 'grad_norm': 0.8287239670753479, 'learning_rate': 7.802746222131873e-05, 'epoch': 0.03}
{'loss': 1.3517, 'grad_norm': 0.6737128496170044, 'learning_rate': 7.972167545916544e-05, 'epoch': 0.04}
{'loss': 1.3888, 'grad_norm': 0.7575621604919434, 'learning_rate': 8.128020055812762e-05, 'epoch': 0.04}
{'loss': 1.3403, 'grad_norm': 0.6198861598968506, 'learning_rate': 8.272316941802484e-05, 'epoch': 0.04}
{'loss': 1.2877, 'grad_norm': 0.7704681754112244, 'learning_rate': 8.40665412877078e-05, 'epoch': 0.04}
{'loss': 1.3217, 'grad_norm': 0.753148078918457, 'learning_rate': 8.532318174836105e-05, 'epoch': 0.05}
{'loss': 1.3163, 'grad_norm': 0.6373308897018433, 'learning_rate': 8.650361401336356e-05, 'epoch': 0.05}
{'loss': 1.2969, 'grad_norm': 0.6265484094619751, 'learning_rate': 8.761655515660627e-05, 'epoch': 0.05}
{'loss': 1.3712, 'grad_norm': 0.6926285624504089, 'learning_rate': 8.86693071665027e-05, 'epoch': 0.06}
{'loss': 1.3011, 'grad_norm': 0.6044808030128479, 'learning_rate': 8.966804757690343e-05, 'epoch': 0.06}
  warnings.warn(                                                                                                                                
{'eval_loss': 1.4924620389938354, 'eval_runtime': 246.2907, 'eval_samples_per_second': 27.528, 'eval_steps_per_second': 0.861, 'epoch': 0.06}
                                                                                                                                                
{'loss': 1.241, 'grad_norm': 0.5741841793060303, 'learning_rate': 9.061804911546565e-05, 'epoch': 0.06}
{'loss': 1.2734, 'grad_norm': 0.6286064982414246, 'learning_rate': 9.152384820795519e-05, 'epoch': 0.06}
{'loss': 1.3588, 'grad_norm': 0.6774728298187256, 'learning_rate': 9.23893759952249e-05, 'epoch': 0.07}
{'loss': 1.2822, 'grad_norm': 0.5251493453979492, 'learning_rate': 9.321806144580188e-05, 'epoch': 0.07}
{'loss': 1.274, 'grad_norm': 0.7161235213279724, 'learning_rate': 9.40129134054458e-05, 'epoch': 0.07}
{'loss': 1.2974, 'grad_norm': 0.6714425086975098, 'learning_rate': 9.477658654476408e-05, 'epoch': 0.08}
{'loss': 1.234, 'grad_norm': 0.6113528609275818, 'learning_rate': 9.55114348540471e-05, 'epoch': 0.08}
{'loss': 1.2742, 'grad_norm': 0.5702859163284302, 'learning_rate': 9.621955540466126e-05, 'epoch': 0.08}
{'loss': 1.2137, 'grad_norm': 0.5634028911590576, 'learning_rate': 9.690282442780122e-05, 'epoch': 0.09}
{'loss': 1.2929, 'grad_norm': 0.6437089443206787, 'learning_rate': 9.756292727434425e-05, 'epoch': 0.09}
{'loss': 1.2541, 'grad_norm': 0.5431512594223022, 'learning_rate': 9.82013834603667e-05, 'epoch': 0.09}
{'loss': 1.228, 'grad_norm': 0.6479554772377014, 'learning_rate': 9.881956773499751e-05, 'epoch': 0.09}
{'loss': 1.2546, 'grad_norm': 0.6105641722679138, 'learning_rate': 9.941872790539601e-05, 'epoch': 0.1}
{'loss': 1.2734, 'grad_norm': 0.6162766218185425, 'learning_rate': 0.0001, 'epoch': 0.1}
{'loss': 1.2618, 'grad_norm': 0.6421405076980591, 'learning_rate': 0.0001, 'epoch': 0.1}
{'loss': 1.2152, 'grad_norm': 0.6490446925163269, 'learning_rate': 0.0001, 'epoch': 0.11}
{'loss': 1.2847, 'grad_norm': 0.7280729413032532, 'learning_rate': 0.0001, 'epoch': 0.11}
{'loss': 1.239, 'grad_norm': 0.5215629935264587, 'learning_rate': 0.0001, 'epoch': 0.11}
{'loss': 1.2427, 'grad_norm': 0.5083969831466675, 'learning_rate': 0.0001, 'epoch': 0.12}
{'loss': 1.2721, 'grad_norm': 0.5302383899688721, 'learning_rate': 0.0001, 'epoch': 0.12}
                                                                                                                                                
{'eval_loss': 1.4297910928726196, 'eval_runtime': 250.6542, 'eval_samples_per_second': 27.049, 'eval_steps_per_second': 0.846, 'epoch': 0.12}
{'loss': 1.2549, 'grad_norm': 0.5137877464294434, 'learning_rate': 0.0001, 'epoch': 0.12}
{'loss': 1.3315, 'grad_norm': 0.6379396319389343, 'learning_rate': 0.0001, 'epoch': 0.12}
{'loss': 1.255, 'grad_norm': 0.5948516726493835, 'learning_rate': 0.0001, 'epoch': 0.13}
{'loss': 1.2639, 'grad_norm': 0.5186414122581482, 'learning_rate': 0.0001, 'epoch': 0.13}
{'loss': 1.2426, 'grad_norm': 0.5251172780990601, 'learning_rate': 0.0001, 'epoch': 0.13}
{'loss': 1.2152, 'grad_norm': 0.48480507731437683, 'learning_rate': 0.0001, 'epoch': 0.14}
{'loss': 1.261, 'grad_norm': 0.47951027750968933, 'learning_rate': 0.0001, 'epoch': 0.14}
{'loss': 1.2475, 'grad_norm': 0.5970630645751953, 'learning_rate': 0.0001, 'epoch': 0.14}
{'loss': 1.2518, 'grad_norm': 0.5175691246986389, 'learning_rate': 0.0001, 'epoch': 0.14}
{'loss': 1.2629, 'grad_norm': 0.6099365949630737, 'learning_rate': 0.0001, 'epoch': 0.15}
{'loss': 1.1788, 'grad_norm': 0.44505590200424194, 'learning_rate': 0.0001, 'epoch': 0.15}
{'loss': 1.2755, 'grad_norm': 0.5684008598327637, 'learning_rate': 0.0001, 'epoch': 0.15}
{'loss': 1.2402, 'grad_norm': 0.5378715395927429, 'learning_rate': 0.0001, 'epoch': 0.16}
{'loss': 1.2311, 'grad_norm': 0.4710020124912262, 'learning_rate': 0.0001, 'epoch': 0.16}
{'loss': 1.2385, 'grad_norm': 0.4516514539718628, 'learning_rate': 0.0001, 'epoch': 0.16}
{'loss': 1.2273, 'grad_norm': 0.46083661913871765, 'learning_rate': 0.0001, 'epoch': 0.17}
{'loss': 1.2456, 'grad_norm': 0.585292398929596, 'learning_rate': 0.0001, 'epoch': 0.17}
{'loss': 1.1616, 'grad_norm': 0.4513900876045227, 'learning_rate': 0.0001, 'epoch': 0.17}
{'loss': 1.23, 'grad_norm': 0.47135844826698303, 'learning_rate': 0.0001, 'epoch': 0.17}
{'loss': 1.2186, 'grad_norm': 0.5187608003616333, 'learning_rate': 0.0001, 'epoch': 0.18}
{'eval_loss': 1.4048868417739868, 'eval_runtime': 248.3007, 'eval_samples_per_second': 27.306, 'eval_steps_per_second': 0.854, 'epoch': 0.18}
{'loss': 1.1953, 'grad_norm': 0.5357810258865356, 'learning_rate': 0.0001, 'epoch': 0.18}
{'loss': 1.1933, 'grad_norm': 0.5217350125312805, 'learning_rate': 0.0001, 'epoch': 0.18}
{'loss': 1.1594, 'grad_norm': 0.4613063335418701, 'learning_rate': 0.0001, 'epoch': 0.19}
{'loss': 1.223, 'grad_norm': 0.4805125296115875, 'learning_rate': 0.0001, 'epoch': 0.19}
{'loss': 1.1926, 'grad_norm': 0.5022006630897522, 'learning_rate': 0.0001, 'epoch': 0.19}
{'loss': 1.2153, 'grad_norm': 0.4623624086380005, 'learning_rate': 0.0001, 'epoch': 0.19}
{'loss': 1.2157, 'grad_norm': 0.44568437337875366, 'learning_rate': 0.0001, 'epoch': 0.2}
{'loss': 1.195, 'grad_norm': 0.5664547681808472, 'learning_rate': 0.0001, 'epoch': 0.2}
{'loss': 1.2118, 'grad_norm': 0.4419609010219574, 'learning_rate': 0.0001, 'epoch': 0.2}
{'loss': 1.2231, 'grad_norm': 0.47520682215690613, 'learning_rate': 0.0001, 'epoch': 0.21}
{'loss': 1.1941, 'grad_norm': 0.5029681324958801, 'learning_rate': 0.0001, 'epoch': 0.21}
{'loss': 1.221, 'grad_norm': 0.5756416320800781, 'learning_rate': 0.0001, 'epoch': 0.21}
{'loss': 1.2048, 'grad_norm': 0.40774229168891907, 'learning_rate': 0.0001, 'epoch': 0.22}
{'loss': 1.2308, 'grad_norm': 0.5055027604103088, 'learning_rate': 0.0001, 'epoch': 0.22}
{'loss': 1.2006, 'grad_norm': 0.4239030182361603, 'learning_rate': 0.0001, 'epoch': 0.22}
{'loss': 1.1934, 'grad_norm': 0.3832748234272003, 'learning_rate': 0.0001, 'epoch': 0.22}
{'loss': 1.2679, 'grad_norm': 0.468159556388855, 'learning_rate': 0.0001, 'epoch': 0.23}
{'loss': 1.213, 'grad_norm': 0.5038846135139465, 'learning_rate': 0.0001, 'epoch': 0.23}
{'loss': 1.2091, 'grad_norm': 0.3974873721599579, 'learning_rate': 0.0001, 'epoch': 0.23}
[2025-06-02 05:02:43,264] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.2665, 'grad_norm': 0.4505026638507843, 'learning_rate': 0.0001, 'epoch': 0.24}
{'eval_loss': 1.3894466161727905, 'eval_runtime': 249.2843, 'eval_samples_per_second': 27.198, 'eval_steps_per_second': 0.85, 'epoch': 0.24}
{'loss': 1.2102, 'grad_norm': 0.4718388020992279, 'learning_rate': 0.0001, 'epoch': 0.24}
{'loss': 1.2079, 'grad_norm': 0.38462600111961365, 'learning_rate': 0.0001, 'epoch': 0.24}
{'loss': 1.201, 'grad_norm': 0.4475425183773041, 'learning_rate': 0.0001, 'epoch': 0.24}
{'loss': 1.2378, 'grad_norm': 0.4842807650566101, 'learning_rate': 0.0001, 'epoch': 0.25}
{'loss': 1.2478, 'grad_norm': 0.40634164214134216, 'learning_rate': 0.0001, 'epoch': 0.25}
{'loss': 1.2138, 'grad_norm': 0.42216214537620544, 'learning_rate': 0.0001, 'epoch': 0.25}
{'loss': 1.1823, 'grad_norm': 0.39252954721450806, 'learning_rate': 0.0001, 'epoch': 0.26}
{'loss': 1.1766, 'grad_norm': 0.3978445529937744, 'learning_rate': 0.0001, 'epoch': 0.26}
{'loss': 1.227, 'grad_norm': 0.3954154849052429, 'learning_rate': 0.0001, 'epoch': 0.26}
{'loss': 1.1863, 'grad_norm': 0.5098206400871277, 'learning_rate': 0.0001, 'epoch': 0.27}
{'loss': 1.1808, 'grad_norm': 0.3866317570209503, 'learning_rate': 0.0001, 'epoch': 0.27}
{'loss': 1.2234, 'grad_norm': 0.42024534940719604, 'learning_rate': 0.0001, 'epoch': 0.27}
{'loss': 1.2343, 'grad_norm': 0.49251362681388855, 'learning_rate': 0.0001, 'epoch': 0.27}
{'loss': 1.1916, 'grad_norm': 0.38760024309158325, 'learning_rate': 0.0001, 'epoch': 0.28}
{'loss': 1.2364, 'grad_norm': 0.393055260181427, 'learning_rate': 0.0001, 'epoch': 0.28}
{'loss': 1.1791, 'grad_norm': 0.3966905176639557, 'learning_rate': 0.0001, 'epoch': 0.28}
{'loss': 1.2097, 'grad_norm': 0.4205932319164276, 'learning_rate': 0.0001, 'epoch': 0.29}
{'loss': 1.1786, 'grad_norm': 0.40774476528167725, 'learning_rate': 0.0001, 'epoch': 0.29}
{'loss': 1.2093, 'grad_norm': 0.4860667288303375, 'learning_rate': 0.0001, 'epoch': 0.29}
{'loss': 1.1644, 'grad_norm': 0.38234424591064453, 'learning_rate': 0.0001, 'epoch': 0.3}
{'eval_loss': 1.3818562030792236, 'eval_runtime': 246.1763, 'eval_samples_per_second': 27.541, 'eval_steps_per_second': 0.861, 'epoch': 0.3}
{'loss': 1.2156, 'grad_norm': 0.4049389660358429, 'learning_rate': 0.0001, 'epoch': 0.3}
{'loss': 1.2037, 'grad_norm': 0.3692502975463867, 'learning_rate': 0.0001, 'epoch': 0.3}
{'loss': 1.2138, 'grad_norm': 0.44128918647766113, 'learning_rate': 0.0001, 'epoch': 0.3}
{'loss': 1.2531, 'grad_norm': 0.4006088674068451, 'learning_rate': 0.0001, 'epoch': 0.31}
{'loss': 1.1661, 'grad_norm': 0.2942383885383606, 'learning_rate': 0.0001, 'epoch': 0.31}
{'loss': 1.2258, 'grad_norm': 0.40336400270462036, 'learning_rate': 0.0001, 'epoch': 0.31}
{'loss': 1.2138, 'grad_norm': 0.4106636643409729, 'learning_rate': 0.0001, 'epoch': 0.32}
{'loss': 1.2023, 'grad_norm': 0.4566124379634857, 'learning_rate': 0.0001, 'epoch': 0.32}
{'loss': 1.18, 'grad_norm': 0.43914926052093506, 'learning_rate': 0.0001, 'epoch': 0.32}
{'loss': 1.2313, 'grad_norm': 0.42770352959632874, 'learning_rate': 0.0001, 'epoch': 0.32}
{'loss': 1.2465, 'grad_norm': 0.41804003715515137, 'learning_rate': 0.0001, 'epoch': 0.33}
{'loss': 1.1721, 'grad_norm': 0.42947250604629517, 'learning_rate': 0.0001, 'epoch': 0.33}
{'loss': 1.1603, 'grad_norm': 0.4126758277416229, 'learning_rate': 0.0001, 'epoch': 0.33}
{'loss': 1.1716, 'grad_norm': 0.3431655466556549, 'learning_rate': 0.0001, 'epoch': 0.34}
{'loss': 1.1917, 'grad_norm': 0.36367183923721313, 'learning_rate': 0.0001, 'epoch': 0.34}
{'loss': 1.1811, 'grad_norm': 0.37171483039855957, 'learning_rate': 0.0001, 'epoch': 0.34}
{'loss': 1.2025, 'grad_norm': 0.40305355191230774, 'learning_rate': 0.0001, 'epoch': 0.35}
{'loss': 1.2184, 'grad_norm': 0.6737216114997864, 'learning_rate': 0.0001, 'epoch': 0.35}
{'loss': 1.1775, 'grad_norm': 0.3790748417377472, 'learning_rate': 0.0001, 'epoch': 0.35}
{'loss': 1.206, 'grad_norm': 0.3921985626220703, 'learning_rate': 0.0001, 'epoch': 0.35}
{'eval_loss': 1.3737961053848267, 'eval_runtime': 247.3884, 'eval_samples_per_second': 27.406, 'eval_steps_per_second': 0.857, 'epoch': 0.35}
{'loss': 1.211, 'grad_norm': 0.35289466381073, 'learning_rate': 0.0001, 'epoch': 0.36}
{'loss': 1.1978, 'grad_norm': 0.33914104104042053, 'learning_rate': 0.0001, 'epoch': 0.36}
{'loss': 1.1504, 'grad_norm': 0.37348440289497375, 'learning_rate': 0.0001, 'epoch': 0.36}
{'loss': 1.2192, 'grad_norm': 0.3958370089530945, 'learning_rate': 0.0001, 'epoch': 0.37}
{'loss': 1.2206, 'grad_norm': 0.39797595143318176, 'learning_rate': 0.0001, 'epoch': 0.37}
{'loss': 1.1687, 'grad_norm': 0.43255582451820374, 'learning_rate': 0.0001, 'epoch': 0.37}
{'loss': 1.2094, 'grad_norm': 0.4527657628059387, 'learning_rate': 0.0001, 'epoch': 0.37}
{'loss': 1.1974, 'grad_norm': 0.34012940526008606, 'learning_rate': 0.0001, 'epoch': 0.38}
{'loss': 1.2065, 'grad_norm': 0.36931538581848145, 'learning_rate': 0.0001, 'epoch': 0.38}
{'loss': 1.1745, 'grad_norm': 0.3569357097148895, 'learning_rate': 0.0001, 'epoch': 0.38}
{'loss': 1.1673, 'grad_norm': 0.34146326780319214, 'learning_rate': 0.0001, 'epoch': 0.39}
{'loss': 1.1861, 'grad_norm': 0.35260283946990967, 'learning_rate': 0.0001, 'epoch': 0.39}
{'loss': 1.1988, 'grad_norm': 0.4133412837982178, 'learning_rate': 0.0001, 'epoch': 0.39}
{'loss': 1.2176, 'grad_norm': 0.3880932629108429, 'learning_rate': 0.0001, 'epoch': 0.4}
{'loss': 1.1912, 'grad_norm': 0.33228799700737, 'learning_rate': 0.0001, 'epoch': 0.4}
{'loss': 1.2024, 'grad_norm': 0.3206666111946106, 'learning_rate': 0.0001, 'epoch': 0.4}
{'loss': 1.1899, 'grad_norm': 0.3273152709007263, 'learning_rate': 0.0001, 'epoch': 0.4}
{'loss': 1.1743, 'grad_norm': 0.40319663286209106, 'learning_rate': 0.0001, 'epoch': 0.41}
{'loss': 1.1828, 'grad_norm': 0.3892875909805298, 'learning_rate': 0.0001, 'epoch': 0.41}
{'loss': 1.2053, 'grad_norm': 0.39169618487358093, 'learning_rate': 0.0001, 'epoch': 0.41}
{'eval_loss': 1.3690263032913208, 'eval_runtime': 246.9137, 'eval_samples_per_second': 27.459, 'eval_steps_per_second': 0.859, 'epoch': 0.41}
{'loss': 1.2106, 'grad_norm': 0.4526151716709137, 'learning_rate': 0.0001, 'epoch': 0.42}
{'loss': 1.163, 'grad_norm': 0.3414866328239441, 'learning_rate': 0.0001, 'epoch': 0.42}
{'loss': 1.1912, 'grad_norm': 0.33304673433303833, 'learning_rate': 0.0001, 'epoch': 0.42}
{'loss': 1.1921, 'grad_norm': 0.42594268918037415, 'learning_rate': 0.0001, 'epoch': 0.42}
{'loss': 1.2233, 'grad_norm': 0.33425864577293396, 'learning_rate': 0.0001, 'epoch': 0.43}
{'loss': 1.1964, 'grad_norm': 0.3625440299510956, 'learning_rate': 0.0001, 'epoch': 0.43}
{'loss': 1.1828, 'grad_norm': 0.3822436034679413, 'learning_rate': 0.0001, 'epoch': 0.43}
{'loss': 1.1799, 'grad_norm': 0.3039187788963318, 'learning_rate': 0.0001, 'epoch': 0.44}
{'loss': 1.1424, 'grad_norm': 0.37662774324417114, 'learning_rate': 0.0001, 'epoch': 0.44}
{'loss': 1.1697, 'grad_norm': 0.2947549819946289, 'learning_rate': 0.0001, 'epoch': 0.44}
{'loss': 1.1676, 'grad_norm': 0.38249680399894714, 'learning_rate': 0.0001, 'epoch': 0.45}
{'loss': 1.1899, 'grad_norm': 0.4027719795703888, 'learning_rate': 0.0001, 'epoch': 0.45}
{'loss': 1.1422, 'grad_norm': 0.3985890746116638, 'learning_rate': 0.0001, 'epoch': 0.45}
{'loss': 1.2159, 'grad_norm': 0.36851444840431213, 'learning_rate': 0.0001, 'epoch': 0.45}
{'loss': 1.1967, 'grad_norm': 0.3305245339870453, 'learning_rate': 0.0001, 'epoch': 0.46}
{'loss': 1.1573, 'grad_norm': 0.3373122215270996, 'learning_rate': 0.0001, 'epoch': 0.46}
{'loss': 1.2034, 'grad_norm': 0.40627261996269226, 'learning_rate': 0.0001, 'epoch': 0.46}
{'loss': 1.2213, 'grad_norm': 0.4473290741443634, 'learning_rate': 0.0001, 'epoch': 0.47}
{'loss': 1.1793, 'grad_norm': 0.39021629095077515, 'learning_rate': 0.0001, 'epoch': 0.47}
{'loss': 1.2166, 'grad_norm': 0.3755252957344055, 'learning_rate': 0.0001, 'epoch': 0.47}
{'eval_loss': 1.366101861000061, 'eval_runtime': 246.4874, 'eval_samples_per_second': 27.506, 'eval_steps_per_second': 0.86, 'epoch': 0.47}
{'loss': 1.1429, 'grad_norm': 0.3602404296398163, 'learning_rate': 0.0001, 'epoch': 0.48}
{'loss': 1.1833, 'grad_norm': 0.35179710388183594, 'learning_rate': 0.0001, 'epoch': 0.48}
{'loss': 1.1892, 'grad_norm': 0.3609860837459564, 'learning_rate': 0.0001, 'epoch': 0.48}
{'loss': 1.1814, 'grad_norm': 0.40376830101013184, 'learning_rate': 0.0001, 'epoch': 0.48}
{'loss': 1.1775, 'grad_norm': 0.3921487033367157, 'learning_rate': 0.0001, 'epoch': 0.49}
{'loss': 1.1903, 'grad_norm': 0.3765113055706024, 'learning_rate': 0.0001, 'epoch': 0.49}
{'loss': 1.1597, 'grad_norm': 0.3921235203742981, 'learning_rate': 0.0001, 'epoch': 0.49}
{'loss': 1.2156, 'grad_norm': 0.34277284145355225, 'learning_rate': 0.0001, 'epoch': 0.5}
{'loss': 1.2437, 'grad_norm': 0.36174461245536804, 'learning_rate': 0.0001, 'epoch': 0.5}
{'loss': 1.2092, 'grad_norm': 0.4500541687011719, 'learning_rate': 0.0001, 'epoch': 0.5}
{'loss': 1.2351, 'grad_norm': 0.3848503530025482, 'learning_rate': 0.0001, 'epoch': 0.5}
{'loss': 1.2352, 'grad_norm': 0.38285553455352783, 'learning_rate': 0.0001, 'epoch': 0.51}
{'loss': 1.1481, 'grad_norm': 0.3328910768032074, 'learning_rate': 0.0001, 'epoch': 0.51}
{'loss': 1.192, 'grad_norm': 0.33165690302848816, 'learning_rate': 0.0001, 'epoch': 0.51}
{'loss': 1.1854, 'grad_norm': 0.37010708451271057, 'learning_rate': 0.0001, 'epoch': 0.52}
{'loss': 1.2189, 'grad_norm': 0.35477691888809204, 'learning_rate': 0.0001, 'epoch': 0.52}
{'loss': 1.2665, 'grad_norm': 0.3677918016910553, 'learning_rate': 0.0001, 'epoch': 0.52}
{'loss': 1.199, 'grad_norm': 0.392135888338089, 'learning_rate': 0.0001, 'epoch': 0.53}
{'loss': 1.1592, 'grad_norm': 0.33336618542671204, 'learning_rate': 0.0001, 'epoch': 0.53}
{'loss': 1.2137, 'grad_norm': 0.31889161467552185, 'learning_rate': 0.0001, 'epoch': 0.53}
{'eval_loss': 1.3621083498001099, 'eval_runtime': 247.4751, 'eval_samples_per_second': 27.397, 'eval_steps_per_second': 0.857, 'epoch': 0.53}
{'loss': 1.2304, 'grad_norm': 0.27766871452331543, 'learning_rate': 0.0001, 'epoch': 0.53}
{'loss': 1.1632, 'grad_norm': 0.33129802346229553, 'learning_rate': 0.0001, 'epoch': 0.54}
{'loss': 1.1903, 'grad_norm': 0.37082311511039734, 'learning_rate': 0.0001, 'epoch': 0.54}
{'loss': 1.2044, 'grad_norm': 0.40135613083839417, 'learning_rate': 0.0001, 'epoch': 0.54}
{'loss': 1.1882, 'grad_norm': 0.36218884587287903, 'learning_rate': 0.0001, 'epoch': 0.55}
{'loss': 1.1786, 'grad_norm': 0.36676305532455444, 'learning_rate': 0.0001, 'epoch': 0.55}
{'loss': 1.2475, 'grad_norm': 0.4113537669181824, 'learning_rate': 0.0001, 'epoch': 0.55}
{'loss': 1.1733, 'grad_norm': 0.35803937911987305, 'learning_rate': 0.0001, 'epoch': 0.55}
{'loss': 1.204, 'grad_norm': 0.4070746600627899, 'learning_rate': 0.0001, 'epoch': 0.56}
{'loss': 1.2017, 'grad_norm': 0.4393693208694458, 'learning_rate': 0.0001, 'epoch': 0.56}
{'loss': 1.2381, 'grad_norm': 0.3669402301311493, 'learning_rate': 0.0001, 'epoch': 0.56}
{'loss': 1.2058, 'grad_norm': 0.37681281566619873, 'learning_rate': 0.0001, 'epoch': 0.57}
{'loss': 1.1947, 'grad_norm': 0.3346240818500519, 'learning_rate': 0.0001, 'epoch': 0.57}
{'loss': 1.2058, 'grad_norm': 0.3743688762187958, 'learning_rate': 0.0001, 'epoch': 0.57}
{'loss': 1.1743, 'grad_norm': 0.3186356723308563, 'learning_rate': 0.0001, 'epoch': 0.58}
{'loss': 1.1639, 'grad_norm': 0.3614408075809479, 'learning_rate': 0.0001, 'epoch': 0.58}
{'loss': 1.1383, 'grad_norm': 0.27605751156806946, 'learning_rate': 0.0001, 'epoch': 0.58}
{'loss': 1.1958, 'grad_norm': 0.3820742964744568, 'learning_rate': 0.0001, 'epoch': 0.58}
{'loss': 1.145, 'grad_norm': 0.3408990204334259, 'learning_rate': 0.0001, 'epoch': 0.59}
{'loss': 1.2051, 'grad_norm': 0.33466634154319763, 'learning_rate': 0.0001, 'epoch': 0.59}
{'eval_loss': 1.3581589460372925, 'eval_runtime': 247.6481, 'eval_samples_per_second': 27.378, 'eval_steps_per_second': 0.856, 'epoch': 0.59}
{'loss': 1.1286, 'grad_norm': 0.29110196232795715, 'learning_rate': 0.0001, 'epoch': 0.59}
{'loss': 1.1957, 'grad_norm': 0.39917507767677307, 'learning_rate': 0.0001, 'epoch': 0.6}
{'loss': 1.2235, 'grad_norm': 0.46603864431381226, 'learning_rate': 0.0001, 'epoch': 0.6}
{'loss': 1.1961, 'grad_norm': 0.37322765588760376, 'learning_rate': 0.0001, 'epoch': 0.6}
{'loss': 1.2031, 'grad_norm': 0.36014920473098755, 'learning_rate': 0.0001, 'epoch': 0.6}
{'loss': 1.1871, 'grad_norm': 0.36356082558631897, 'learning_rate': 0.0001, 'epoch': 0.61}
{'loss': 1.1842, 'grad_norm': 0.35309362411499023, 'learning_rate': 0.0001, 'epoch': 0.61}
{'loss': 1.2012, 'grad_norm': 0.37153396010398865, 'learning_rate': 0.0001, 'epoch': 0.61}
{'loss': 1.2355, 'grad_norm': 0.4099673926830292, 'learning_rate': 0.0001, 'epoch': 0.62}
{'loss': 1.2069, 'grad_norm': 0.41647520661354065, 'learning_rate': 0.0001, 'epoch': 0.62}
{'loss': 1.1836, 'grad_norm': 0.28108876943588257, 'learning_rate': 0.0001, 'epoch': 0.62}
{'loss': 1.1613, 'grad_norm': 0.3371139466762543, 'learning_rate': 0.0001, 'epoch': 0.63}
{'loss': 1.1463, 'grad_norm': 0.38452470302581787, 'learning_rate': 0.0001, 'epoch': 0.63}
[2025-06-02 09:18:59,549] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.1721, 'grad_norm': 0.40959247946739197, 'learning_rate': 0.0001, 'epoch': 0.63}
{'loss': 1.1507, 'grad_norm': 0.31261909008026123, 'learning_rate': 0.0001, 'epoch': 0.63}
{'loss': 1.1355, 'grad_norm': 0.3806215822696686, 'learning_rate': 0.0001, 'epoch': 0.64}
{'loss': 1.1687, 'grad_norm': 0.3014231324195862, 'learning_rate': 0.0001, 'epoch': 0.64}
{'loss': 1.2029, 'grad_norm': 0.35170215368270874, 'learning_rate': 0.0001, 'epoch': 0.64}
{'loss': 1.1798, 'grad_norm': 0.3542157709598541, 'learning_rate': 0.0001, 'epoch': 0.65}
{'loss': 1.1725, 'grad_norm': 0.3632979989051819, 'learning_rate': 0.0001, 'epoch': 0.65}
{'eval_loss': 1.3547154664993286, 'eval_runtime': 248.1431, 'eval_samples_per_second': 27.323, 'eval_steps_per_second': 0.854, 'epoch': 0.65}
{'loss': 1.1479, 'grad_norm': 0.3374481499195099, 'learning_rate': 0.0001, 'epoch': 0.65}
{'loss': 1.1752, 'grad_norm': 0.34452196955680847, 'learning_rate': 0.0001, 'epoch': 0.66}
{'loss': 1.1502, 'grad_norm': 0.3467633128166199, 'learning_rate': 0.0001, 'epoch': 0.66}
{'loss': 1.1449, 'grad_norm': 0.3299087584018707, 'learning_rate': 0.0001, 'epoch': 0.66}
[2025-06-02 09:42:27,124] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.1769, 'grad_norm': 0.3783397376537323, 'learning_rate': 0.0001, 'epoch': 0.66}
{'loss': 1.1904, 'grad_norm': 0.3245580792427063, 'learning_rate': 0.0001, 'epoch': 0.67}
{'loss': 1.1979, 'grad_norm': 0.3698376715183258, 'learning_rate': 0.0001, 'epoch': 0.67}
{'loss': 1.2051, 'grad_norm': 0.3766252398490906, 'learning_rate': 0.0001, 'epoch': 0.67}
{'loss': 1.1357, 'grad_norm': 0.34350407123565674, 'learning_rate': 0.0001, 'epoch': 0.68}
{'loss': 1.2092, 'grad_norm': 0.38537052273750305, 'learning_rate': 0.0001, 'epoch': 0.68}
{'loss': 1.2057, 'grad_norm': 0.3605525493621826, 'learning_rate': 0.0001, 'epoch': 0.68}
{'loss': 1.1926, 'grad_norm': 0.31560054421424866, 'learning_rate': 0.0001, 'epoch': 0.68}
{'loss': 1.2337, 'grad_norm': 0.34913086891174316, 'learning_rate': 0.0001, 'epoch': 0.69}
{'loss': 1.2076, 'grad_norm': 0.34916892647743225, 'learning_rate': 0.0001, 'epoch': 0.69}
{'loss': 1.1513, 'grad_norm': 0.35321515798568726, 'learning_rate': 0.0001, 'epoch': 0.69}
{'loss': 1.1692, 'grad_norm': 0.3224838376045227, 'learning_rate': 0.0001, 'epoch': 0.7}
{'loss': 1.2017, 'grad_norm': 0.32030728459358215, 'learning_rate': 0.0001, 'epoch': 0.7}
{'loss': 1.1755, 'grad_norm': 0.3288993537425995, 'learning_rate': 0.0001, 'epoch': 0.7}
{'loss': 1.1533, 'grad_norm': 0.3415464162826538, 'learning_rate': 0.0001, 'epoch': 0.71}
{'loss': 1.17, 'grad_norm': 0.337858110666275, 'learning_rate': 0.0001, 'epoch': 0.71}
{'eval_loss': 1.3537287712097168, 'eval_runtime': 247.6889, 'eval_samples_per_second': 27.373, 'eval_steps_per_second': 0.856, 'epoch': 0.71}
{'loss': 1.1984, 'grad_norm': 0.37323233485221863, 'learning_rate': 0.0001, 'epoch': 0.71}
{'loss': 1.1828, 'grad_norm': 0.37474894523620605, 'learning_rate': 0.0001, 'epoch': 0.71}
{'loss': 1.1669, 'grad_norm': 0.28909632563591003, 'learning_rate': 0.0001, 'epoch': 0.72}
{'loss': 1.1545, 'grad_norm': 0.369081050157547, 'learning_rate': 0.0001, 'epoch': 0.72}
{'loss': 1.1607, 'grad_norm': 0.3468446135520935, 'learning_rate': 0.0001, 'epoch': 0.72}
{'loss': 1.1644, 'grad_norm': 0.36472147703170776, 'learning_rate': 0.0001, 'epoch': 0.73}
{'loss': 1.2037, 'grad_norm': 0.37396541237831116, 'learning_rate': 0.0001, 'epoch': 0.73}
{'loss': 1.1631, 'grad_norm': 0.29168689250946045, 'learning_rate': 0.0001, 'epoch': 0.73}
{'loss': 1.1341, 'grad_norm': 0.27392587065696716, 'learning_rate': 0.0001, 'epoch': 0.73}
[2025-06-02 10:28:36,134] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.2066, 'grad_norm': 0.3431527614593506, 'learning_rate': 0.0001, 'epoch': 0.74}
{'loss': 1.1022, 'grad_norm': 0.30904772877693176, 'learning_rate': 0.0001, 'epoch': 0.74}
{'loss': 1.1328, 'grad_norm': 0.3317582905292511, 'learning_rate': 0.0001, 'epoch': 0.74}
{'loss': 1.1804, 'grad_norm': 0.3039371371269226, 'learning_rate': 0.0001, 'epoch': 0.75}
{'loss': 1.1261, 'grad_norm': 0.3079144358634949, 'learning_rate': 0.0001, 'epoch': 0.75}
{'loss': 1.1768, 'grad_norm': 0.3599222004413605, 'learning_rate': 0.0001, 'epoch': 0.75}
{'loss': 1.1666, 'grad_norm': 0.31710904836654663, 'learning_rate': 0.0001, 'epoch': 0.76}
{'loss': 1.1456, 'grad_norm': 0.327048659324646, 'learning_rate': 0.0001, 'epoch': 0.76}
{'loss': 1.1516, 'grad_norm': 0.3233148753643036, 'learning_rate': 0.0001, 'epoch': 0.76}
{'loss': 1.1785, 'grad_norm': 0.31001752614974976, 'learning_rate': 0.0001, 'epoch': 0.76}
{'loss': 1.172, 'grad_norm': 0.3062288761138916, 'learning_rate': 0.0001, 'epoch': 0.77}
{'eval_loss': 1.3487768173217773, 'eval_runtime': 247.4551, 'eval_samples_per_second': 27.399, 'eval_steps_per_second': 0.857, 'epoch': 0.77}
{'loss': 1.1683, 'grad_norm': 0.3496793508529663, 'learning_rate': 0.0001, 'epoch': 0.77}
{'loss': 1.1949, 'grad_norm': 0.3297085165977478, 'learning_rate': 0.0001, 'epoch': 0.77}
{'loss': 1.1865, 'grad_norm': 0.35025104880332947, 'learning_rate': 0.0001, 'epoch': 0.78}
{'loss': 1.1404, 'grad_norm': 0.36191660165786743, 'learning_rate': 0.0001, 'epoch': 0.78}
{'loss': 1.1829, 'grad_norm': 0.3237777650356293, 'learning_rate': 0.0001, 'epoch': 0.78}
{'loss': 1.1168, 'grad_norm': 0.29511624574661255, 'learning_rate': 0.0001, 'epoch': 0.78}
{'loss': 1.1806, 'grad_norm': 0.33152714371681213, 'learning_rate': 0.0001, 'epoch': 0.79}
{'loss': 1.202, 'grad_norm': 0.39324647188186646, 'learning_rate': 0.0001, 'epoch': 0.79}
{'loss': 1.1871, 'grad_norm': 0.2905988097190857, 'learning_rate': 0.0001, 'epoch': 0.79}
{'loss': 1.1572, 'grad_norm': 0.39540359377861023, 'learning_rate': 0.0001, 'epoch': 0.8}
{'loss': 1.2477, 'grad_norm': 0.2932383418083191, 'learning_rate': 0.0001, 'epoch': 0.8}
{'loss': 1.168, 'grad_norm': 0.2975446581840515, 'learning_rate': 0.0001, 'epoch': 0.8}
{'loss': 1.142, 'grad_norm': 0.33861738443374634, 'learning_rate': 0.0001, 'epoch': 0.81}
{'loss': 1.1618, 'grad_norm': 0.3614901900291443, 'learning_rate': 0.0001, 'epoch': 0.81}
{'loss': 1.1751, 'grad_norm': 0.27153632044792175, 'learning_rate': 0.0001, 'epoch': 0.81}
{'loss': 1.1611, 'grad_norm': 0.30300816893577576, 'learning_rate': 0.0001, 'epoch': 0.81}
{'loss': 1.188, 'grad_norm': 0.31228122115135193, 'learning_rate': 0.0001, 'epoch': 0.82}
{'loss': 1.1972, 'grad_norm': 0.3612673878669739, 'learning_rate': 0.0001, 'epoch': 0.82}
{'loss': 1.1499, 'grad_norm': 0.3396519720554352, 'learning_rate': 0.0001, 'epoch': 0.82}
{'loss': 1.176, 'grad_norm': 0.3056199848651886, 'learning_rate': 0.0001, 'epoch': 0.83}
{'eval_loss': 1.3480890989303589, 'eval_runtime': 246.7397, 'eval_samples_per_second': 27.478, 'eval_steps_per_second': 0.859, 'epoch': 0.83}
{'loss': 1.1992, 'grad_norm': 0.3566998839378357, 'learning_rate': 0.0001, 'epoch': 0.83}
{'loss': 1.157, 'grad_norm': 0.39576825499534607, 'learning_rate': 0.0001, 'epoch': 0.83}
{'loss': 1.1689, 'grad_norm': 0.36689692735671997, 'learning_rate': 0.0001, 'epoch': 0.84}
{'loss': 1.1621, 'grad_norm': 0.3026866316795349, 'learning_rate': 0.0001, 'epoch': 0.84}
{'loss': 1.2227, 'grad_norm': 0.3225614130496979, 'learning_rate': 0.0001, 'epoch': 0.84}
{'loss': 1.1737, 'grad_norm': 0.29500293731689453, 'learning_rate': 0.0001, 'epoch': 0.84}
{'loss': 1.1713, 'grad_norm': 0.2833113968372345, 'learning_rate': 0.0001, 'epoch': 0.85}
{'loss': 1.1553, 'grad_norm': 0.32218798995018005, 'learning_rate': 0.0001, 'epoch': 0.85}
{'loss': 1.1748, 'grad_norm': 0.32544612884521484, 'learning_rate': 0.0001, 'epoch': 0.85}
{'loss': 1.1312, 'grad_norm': 0.3194770812988281, 'learning_rate': 0.0001, 'epoch': 0.86}
{'loss': 1.1717, 'grad_norm': 0.29077011346817017, 'learning_rate': 0.0001, 'epoch': 0.86}
{'loss': 1.126, 'grad_norm': 0.4108298718929291, 'learning_rate': 0.0001, 'epoch': 0.86}
{'loss': 1.1641, 'grad_norm': 0.3263062536716461, 'learning_rate': 0.0001, 'epoch': 0.86}
{'loss': 1.1273, 'grad_norm': 0.321237713098526, 'learning_rate': 0.0001, 'epoch': 0.87}
{'loss': 1.1269, 'grad_norm': 0.3150706887245178, 'learning_rate': 0.0001, 'epoch': 0.87}
{'loss': 1.1442, 'grad_norm': 0.31895971298217773, 'learning_rate': 0.0001, 'epoch': 0.87}
{'loss': 1.1877, 'grad_norm': 0.3172248303890228, 'learning_rate': 0.0001, 'epoch': 0.88}
{'loss': 1.1694, 'grad_norm': 0.27037733793258667, 'learning_rate': 0.0001, 'epoch': 0.88}
{'loss': 1.1688, 'grad_norm': 0.3313964307308197, 'learning_rate': 0.0001, 'epoch': 0.88}
{'loss': 1.169, 'grad_norm': 0.30192407965660095, 'learning_rate': 0.0001, 'epoch': 0.89}
{'eval_loss': 1.346632719039917, 'eval_runtime': 247.3078, 'eval_samples_per_second': 27.415, 'eval_steps_per_second': 0.857, 'epoch': 0.89}
{'loss': 1.1616, 'grad_norm': 0.3626715838909149, 'learning_rate': 0.0001, 'epoch': 0.89}
{'loss': 1.2257, 'grad_norm': 0.3132845461368561, 'learning_rate': 0.0001, 'epoch': 0.89}
{'loss': 1.195, 'grad_norm': 0.3729867935180664, 'learning_rate': 0.0001, 'epoch': 0.89}
{'loss': 1.1903, 'grad_norm': 0.8032135367393494, 'learning_rate': 0.0001, 'epoch': 0.9}
{'loss': 1.1823, 'grad_norm': 0.28687193989753723, 'learning_rate': 0.0001, 'epoch': 0.9}
{'loss': 1.1859, 'grad_norm': 0.30333882570266724, 'learning_rate': 0.0001, 'epoch': 0.9}
{'loss': 1.1147, 'grad_norm': 0.29158881306648254, 'learning_rate': 0.0001, 'epoch': 0.91}
{'loss': 1.141, 'grad_norm': 0.31466299295425415, 'learning_rate': 0.0001, 'epoch': 0.91}
{'loss': 1.1875, 'grad_norm': 0.3682771623134613, 'learning_rate': 0.0001, 'epoch': 0.91}
{'loss': 1.1938, 'grad_norm': 0.34508758783340454, 'learning_rate': 0.0001, 'epoch': 0.91}
{'loss': 1.1748, 'grad_norm': 0.31624600291252136, 'learning_rate': 0.0001, 'epoch': 0.92}
{'loss': 1.155, 'grad_norm': 0.35846298933029175, 'learning_rate': 0.0001, 'epoch': 0.92}
{'loss': 1.093, 'grad_norm': 0.2583761215209961, 'learning_rate': 0.0001, 'epoch': 0.92}
{'loss': 1.1635, 'grad_norm': 0.32738620042800903, 'learning_rate': 0.0001, 'epoch': 0.93}
{'loss': 1.1665, 'grad_norm': 0.3147200644016266, 'learning_rate': 0.0001, 'epoch': 0.93}
{'loss': 1.14, 'grad_norm': 0.30471929907798767, 'learning_rate': 0.0001, 'epoch': 0.93}
{'loss': 1.1625, 'grad_norm': 0.3011219799518585, 'learning_rate': 0.0001, 'epoch': 0.94}
{'loss': 1.1681, 'grad_norm': 0.288665235042572, 'learning_rate': 0.0001, 'epoch': 0.94}
{'loss': 1.1574, 'grad_norm': 0.27973607182502747, 'learning_rate': 0.0001, 'epoch': 0.94}
{'loss': 1.1965, 'grad_norm': 0.3426041901111603, 'learning_rate': 0.0001, 'epoch': 0.94}
{'eval_loss': 1.3439240455627441, 'eval_runtime': 246.3907, 'eval_samples_per_second': 27.517, 'eval_steps_per_second': 0.86, 'epoch': 0.94}
{'loss': 1.1343, 'grad_norm': 0.2948189973831177, 'learning_rate': 0.0001, 'epoch': 0.95}
{'loss': 1.1804, 'grad_norm': 0.3621533513069153, 'learning_rate': 0.0001, 'epoch': 0.95}
{'loss': 1.1919, 'grad_norm': 0.3160797953605652, 'learning_rate': 0.0001, 'epoch': 0.95}
{'loss': 1.1445, 'grad_norm': 0.311749130487442, 'learning_rate': 0.0001, 'epoch': 0.96}
{'loss': 1.214, 'grad_norm': 0.31484559178352356, 'learning_rate': 0.0001, 'epoch': 0.96}
{'loss': 1.2503, 'grad_norm': 0.31874895095825195, 'learning_rate': 0.0001, 'epoch': 0.96}
{'loss': 1.1834, 'grad_norm': 0.28996333479881287, 'learning_rate': 0.0001, 'epoch': 0.96}
{'loss': 1.1733, 'grad_norm': 0.2913656234741211, 'learning_rate': 0.0001, 'epoch': 0.97}
{'loss': 1.1969, 'grad_norm': 0.2982765734195709, 'learning_rate': 0.0001, 'epoch': 0.97}
{'loss': 1.1796, 'grad_norm': 0.34283024072647095, 'learning_rate': 0.0001, 'epoch': 0.97}
{'loss': 1.1607, 'grad_norm': 0.3193168044090271, 'learning_rate': 0.0001, 'epoch': 0.98}
{'loss': 1.1304, 'grad_norm': 0.37915748357772827, 'learning_rate': 0.0001, 'epoch': 0.98}
{'loss': 1.2118, 'grad_norm': 0.2842554450035095, 'learning_rate': 0.0001, 'epoch': 0.98}
{'loss': 1.1417, 'grad_norm': 0.3082302212715149, 'learning_rate': 0.0001, 'epoch': 0.99}
{'loss': 1.1517, 'grad_norm': 0.32633909583091736, 'learning_rate': 0.0001, 'epoch': 0.99}
{'loss': 1.1687, 'grad_norm': 0.30882906913757324, 'learning_rate': 0.0001, 'epoch': 0.99}
{'loss': 1.218, 'grad_norm': 0.3236086368560791, 'learning_rate': 0.0001, 'epoch': 0.99}
{'loss': 1.2148, 'grad_norm': 0.3170182406902313, 'learning_rate': 0.0001, 'epoch': 1.0}
{'loss': 1.1526, 'grad_norm': 0.3088963031768799, 'learning_rate': 0.0001, 'epoch': 1.0}
{'loss': 1.106, 'grad_norm': 0.31881946325302124, 'learning_rate': 0.0001, 'epoch': 1.0}
{'eval_loss': 1.3453712463378906, 'eval_runtime': 248.2225, 'eval_samples_per_second': 27.314, 'eval_steps_per_second': 0.854, 'epoch': 1.0}
{'loss': 1.1598, 'grad_norm': 0.3407232463359833, 'learning_rate': 0.0001, 'epoch': 1.01}
{'loss': 1.08, 'grad_norm': 0.29656076431274414, 'learning_rate': 0.0001, 'epoch': 1.01}
{'loss': 1.1584, 'grad_norm': 0.32564789056777954, 'learning_rate': 0.0001, 'epoch': 1.01}
{'loss': 1.0993, 'grad_norm': 0.30535244941711426, 'learning_rate': 0.0001, 'epoch': 1.01}
{'loss': 1.108, 'grad_norm': 0.3131856620311737, 'learning_rate': 0.0001, 'epoch': 1.02}
[2025-06-02 13:34:37,455] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.1408, 'grad_norm': 0.32325801253318787, 'learning_rate': 0.0001, 'epoch': 1.02}
{'loss': 1.1726, 'grad_norm': 0.38909411430358887, 'learning_rate': 0.0001, 'epoch': 1.02}
{'loss': 1.1335, 'grad_norm': 0.29319003224372864, 'learning_rate': 0.0001, 'epoch': 1.03}
{'loss': 1.1415, 'grad_norm': 0.32115623354911804, 'learning_rate': 0.0001, 'epoch': 1.03}
{'loss': 1.0943, 'grad_norm': 0.3641589283943176, 'learning_rate': 0.0001, 'epoch': 1.03}
{'loss': 1.1829, 'grad_norm': 0.3407881259918213, 'learning_rate': 0.0001, 'epoch': 1.04}
{'loss': 1.0974, 'grad_norm': 0.3439313471317291, 'learning_rate': 0.0001, 'epoch': 1.04}
{'loss': 1.0923, 'grad_norm': 0.30603739619255066, 'learning_rate': 0.0001, 'epoch': 1.04}
{'loss': 1.1161, 'grad_norm': 0.33850613236427307, 'learning_rate': 0.0001, 'epoch': 1.04}
{'loss': 1.0812, 'grad_norm': 0.30651092529296875, 'learning_rate': 0.0001, 'epoch': 1.05}
{'loss': 1.133, 'grad_norm': 0.3434087932109833, 'learning_rate': 0.0001, 'epoch': 1.05}
{'loss': 1.1234, 'grad_norm': 0.32536566257476807, 'learning_rate': 0.0001, 'epoch': 1.05}
{'loss': 1.1498, 'grad_norm': 0.28929603099823, 'learning_rate': 0.0001, 'epoch': 1.06}
{'loss': 1.1062, 'grad_norm': 0.29546859860420227, 'learning_rate': 0.0001, 'epoch': 1.06}
{'loss': 1.1259, 'grad_norm': 0.4182150363922119, 'learning_rate': 0.0001, 'epoch': 1.06}
{'eval_loss': 1.3453850746154785, 'eval_runtime': 249.8314, 'eval_samples_per_second': 27.138, 'eval_steps_per_second': 0.849, 'epoch': 1.06}
{'loss': 1.1514, 'grad_norm': 0.34480658173561096, 'learning_rate': 0.0001, 'epoch': 1.06}
{'loss': 1.0755, 'grad_norm': 0.3674759566783905, 'learning_rate': 0.0001, 'epoch': 1.07}
{'loss': 1.1708, 'grad_norm': 0.3465211093425751, 'learning_rate': 0.0001, 'epoch': 1.07}
{'loss': 1.1481, 'grad_norm': 0.3046048879623413, 'learning_rate': 0.0001, 'epoch': 1.07}
{'loss': 1.1484, 'grad_norm': 0.353755384683609, 'learning_rate': 0.0001, 'epoch': 1.08}
{'loss': 1.1257, 'grad_norm': 0.3322429656982422, 'learning_rate': 0.0001, 'epoch': 1.08}
{'loss': 1.1444, 'grad_norm': 0.3183218538761139, 'learning_rate': 0.0001, 'epoch': 1.08}
{'loss': 1.1556, 'grad_norm': 0.32456183433532715, 'learning_rate': 0.0001, 'epoch': 1.09}
{'loss': 1.1397, 'grad_norm': 0.4592103064060211, 'learning_rate': 0.0001, 'epoch': 1.09}
{'loss': 1.1788, 'grad_norm': 0.35487526655197144, 'learning_rate': 0.0001, 'epoch': 1.09}
{'loss': 1.1352, 'grad_norm': 0.29484525322914124, 'learning_rate': 0.0001, 'epoch': 1.09}
{'loss': 1.1315, 'grad_norm': 0.3551277220249176, 'learning_rate': 0.0001, 'epoch': 1.1}
{'loss': 1.1499, 'grad_norm': 0.35826051235198975, 'learning_rate': 0.0001, 'epoch': 1.1}
{'loss': 1.1729, 'grad_norm': 0.35470250248908997, 'learning_rate': 0.0001, 'epoch': 1.1}
{'loss': 1.0857, 'grad_norm': 0.3178890347480774, 'learning_rate': 0.0001, 'epoch': 1.11}
{'loss': 1.1562, 'grad_norm': 0.3633881211280823, 'learning_rate': 0.0001, 'epoch': 1.11}
{'loss': 1.1122, 'grad_norm': 0.35908758640289307, 'learning_rate': 0.0001, 'epoch': 1.11}
{'loss': 1.1047, 'grad_norm': 0.3567257225513458, 'learning_rate': 0.0001, 'epoch': 1.12}
{'loss': 1.0899, 'grad_norm': 0.3101084530353546, 'learning_rate': 0.0001, 'epoch': 1.12}
{'loss': 1.1249, 'grad_norm': 0.33969247341156006, 'learning_rate': 0.0001, 'epoch': 1.12}
{'eval_loss': 1.3439974784851074, 'eval_runtime': 259.969, 'eval_samples_per_second': 26.08, 'eval_steps_per_second': 0.815, 'epoch': 1.12}
{'loss': 1.1224, 'grad_norm': 0.3110252022743225, 'learning_rate': 0.0001, 'epoch': 1.12}
{'loss': 1.1481, 'grad_norm': 0.3465375602245331, 'learning_rate': 0.0001, 'epoch': 1.13}
{'loss': 1.1526, 'grad_norm': 0.3470171093940735, 'learning_rate': 0.0001, 'epoch': 1.13}
{'loss': 1.1588, 'grad_norm': 0.37952789664268494, 'learning_rate': 0.0001, 'epoch': 1.13}
{'loss': 1.1091, 'grad_norm': 0.3267860412597656, 'learning_rate': 0.0001, 'epoch': 1.14}
{'loss': 1.0912, 'grad_norm': 0.3069424629211426, 'learning_rate': 0.0001, 'epoch': 1.14}
{'loss': 1.1036, 'grad_norm': 0.32485413551330566, 'learning_rate': 0.0001, 'epoch': 1.14}
{'loss': 1.1308, 'grad_norm': 0.38342705368995667, 'learning_rate': 0.0001, 'epoch': 1.14}
{'loss': 1.1094, 'grad_norm': 0.3375547230243683, 'learning_rate': 0.0001, 'epoch': 1.15}
{'loss': 1.0824, 'grad_norm': 0.33274975419044495, 'learning_rate': 0.0001, 'epoch': 1.15}
{'loss': 1.11, 'grad_norm': 0.40573936700820923, 'learning_rate': 0.0001, 'epoch': 1.15}
{'loss': 1.1245, 'grad_norm': 0.34038853645324707, 'learning_rate': 0.0001, 'epoch': 1.16}
{'loss': 1.1305, 'grad_norm': 0.3398899435997009, 'learning_rate': 0.0001, 'epoch': 1.16}
{'loss': 1.1629, 'grad_norm': 0.36838746070861816, 'learning_rate': 0.0001, 'epoch': 1.16}
{'loss': 1.1354, 'grad_norm': 0.3390536606311798, 'learning_rate': 0.0001, 'epoch': 1.17}
{'loss': 1.1323, 'grad_norm': 0.3705992102622986, 'learning_rate': 0.0001, 'epoch': 1.17}
{'loss': 1.1383, 'grad_norm': 0.3476364314556122, 'learning_rate': 0.0001, 'epoch': 1.17}
{'loss': 1.139, 'grad_norm': 0.3334161639213562, 'learning_rate': 0.0001, 'epoch': 1.17}
{'loss': 1.0859, 'grad_norm': 0.3645515441894531, 'learning_rate': 0.0001, 'epoch': 1.18}
{'loss': 1.0977, 'grad_norm': 0.32794827222824097, 'learning_rate': 0.0001, 'epoch': 1.18}
{'eval_loss': 1.3429476022720337, 'eval_runtime': 274.4036, 'eval_samples_per_second': 24.708, 'eval_steps_per_second': 0.773, 'epoch': 1.18}
{'loss': 1.1046, 'grad_norm': 0.36864906549453735, 'learning_rate': 0.0001, 'epoch': 1.18}
{'loss': 1.1337, 'grad_norm': 0.3444286286830902, 'learning_rate': 0.0001, 'epoch': 1.19}
{'loss': 1.1375, 'grad_norm': 0.3495725393295288, 'learning_rate': 0.0001, 'epoch': 1.19}
{'loss': 1.1887, 'grad_norm': 0.3343287706375122, 'learning_rate': 0.0001, 'epoch': 1.19}
