{"time":"2025-06-01T02:51:30.511067115+09:00","level":"INFO","msg":"stream: starting","core version":"0.19.11","symlink path":"/data_x/junkim100/projects/translation_it/wandb/run-20250601_025130-55x23eh5/logs/debug-core.log"}
{"time":"2025-06-01T02:51:30.936995217+09:00","level":"INFO","msg":"created new stream","id":"55x23eh5"}
{"time":"2025-06-01T02:51:30.937074546+09:00","level":"INFO","msg":"stream: started","id":"55x23eh5"}
{"time":"2025-06-01T02:51:30.937098351+09:00","level":"INFO","msg":"sender: started","stream_id":"55x23eh5"}
{"time":"2025-06-01T02:51:30.937095467+09:00","level":"INFO","msg":"writer: Do: started","stream_id":"55x23eh5"}
{"time":"2025-06-01T02:51:30.937139563+09:00","level":"INFO","msg":"handler: started","stream_id":"55x23eh5"}
{"time":"2025-06-01T02:51:31.30523006+09:00","level":"INFO","msg":"Starting system monitor"}
{"time":"2025-06-01T02:58:47.1207123+09:00","level":"ERROR","msg":"HTTP error","status":404,"method":"POST","url":"https://api.wandb.ai/files/junkim/llama-translation/55x23eh5/file_stream"}
{"time":"2025-06-01T02:58:47.120804307+09:00","level":"ERROR+4","msg":"filestream: fatal error: filestream: failed to upload: 404 Not Found path=files/junkim/llama-translation/55x23eh5/file_stream: {\"error\":\"run llama-translation/55x23eh5 not found while streaming file\"}"}
