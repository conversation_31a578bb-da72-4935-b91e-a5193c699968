Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 134.96it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 10,485,760 || all params: 8,040,779,776 || trainable%: 0.1304
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/Lima-X-Processed with max_length=9064
Process 0: Dataset loaded - Train: 1030, Val: 52
Dataset filtering results:
  Train: 1030 -> 1030 (100.0% retained)
  Val: 52 -> 52 (100.0% retained)
  Sequence lengths - Min: 76, Max: 9064, Mean: 1516.7
Process 0: Dataset prepared and synchronized
[2025-06-01 04:18:34,963] [INFO] [comm.py:669:init_distributed] cdb=None
🔧 ChunkedSequenceDataCollator initialized:
   Max sequence length: 9064
   Chunk size: 4096
   Overlap: 512
   Effective chunks per 9k sequence: 3

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:736: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training with sequence chunking on 4 GPUs...
   - Max sequence length: 9064 (chunked into 4k pieces)
   - Chunk size: 4096 tokens with 512 token overlap
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Base effective batch size: 32
   - Note: Actual batch size will be ~3x higher due to chunking
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Detected CUDA files, patching ldflags
Emitting ninja build file /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126/cpu_adam/build.ninja...
/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/torch/utils/cpp_extension.py:2356: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation.
If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
  warnings.warn(
Building extension module cpu_adam...
Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)
Loading extension module cpu_adam...
Time to load cpu_adam op: 2.362762928009033 seconds
Parameter Offload: Total persistent parameters: 5246976 in 417 params
[2025-06-01 04:18:59,871] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████| 99/99 [41:09<00:00, 20.18s/it]/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/utils/save_and_load.py:250: UserWarning: Setting `save_embedding_layers` to `True` as the embedding layer has been resized during finetuning.
{'loss': 4.3145, 'grad_norm': 7.2354302406311035, 'learning_rate': 0.0, 'epoch': 0.03}
{'loss': 4.2793, 'grad_norm': 7.822229385375977, 'learning_rate': 0.0001, 'epoch': 0.16}
{'loss': 3.3784, 'grad_norm': 4.465281963348389, 'learning_rate': 0.0001, 'epoch': 0.31}
{'loss': 2.2481, 'grad_norm': 2.054680347442627, 'learning_rate': 0.0001, 'epoch': 0.47}
{'loss': 1.8223, 'grad_norm': 1.26651132106781, 'learning_rate': 0.0001, 'epoch': 0.62}
{'loss': 1.6703, 'grad_norm': 0.8883180618286133, 'learning_rate': 0.0001, 'epoch': 0.78}
[2025-06-01 04:30:09,253] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.5173, 'grad_norm': 0.5542085766792297, 'learning_rate': 0.0001, 'epoch': 0.93}
{'loss': 1.4227, 'grad_norm': 0.7313990592956543, 'learning_rate': 0.0001, 'epoch': 1.06}
{'loss': 1.3725, 'grad_norm': 0.4753330945968628, 'learning_rate': 0.0001, 'epoch': 1.22}
{'loss': 1.2863, 'grad_norm': 0.5996339917182922, 'learning_rate': 0.0001, 'epoch': 1.37}
{'loss': 1.2079, 'grad_norm': 0.8013227581977844, 'learning_rate': 0.0001, 'epoch': 1.53}
{'loss': 1.1915, 'grad_norm': 0.29963281750679016, 'learning_rate': 0.0001, 'epoch': 1.68}
{'loss': 1.1154, 'grad_norm': 0.24620458483695984, 'learning_rate': 0.0001, 'epoch': 1.84}
{'loss': 1.1474, 'grad_norm': 0.3372691869735718, 'learning_rate': 0.0001, 'epoch': 1.99}
{'loss': 1.1357, 'grad_norm': 0.2038220316171646, 'learning_rate': 0.0001, 'epoch': 2.12}
{'loss': 1.1519, 'grad_norm': 0.17976151406764984, 'learning_rate': 0.0001, 'epoch': 2.28}
{'loss': 1.1236, 'grad_norm': 0.19190183281898499, 'learning_rate': 0.0001, 'epoch': 2.43}
{'loss': 1.0835, 'grad_norm': 0.18479135632514954, 'learning_rate': 0.0001, 'epoch': 2.59}
{'loss': 1.1203, 'grad_norm': 0.1741180568933487, 'learning_rate': 0.0001, 'epoch': 2.74}
{'loss': 1.0642, 'grad_norm': 0.17185117304325104, 'learning_rate': 0.0001, 'epoch': 2.9}
  warnings.warn(
100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████| 99/99 [41:47<00:00, 25.32s/it]
{'train_runtime': 2505.5849, 'train_samples_per_second': 1.233, 'train_steps_per_second': 0.04, 'train_loss': 1.5754504203796387, 'init_mem_cpu_alloc_delta': 12288, 'init_mem_gpu_alloc_delta': 0, 'init_mem_cpu_peaked_delta': 0, 'init_mem_gpu_peaked_delta': 0, 'train_mem_cpu_alloc_delta': 2143350784, 'train_mem_gpu_alloc_delta': 61210112, 'train_mem_cpu_peaked_delta': 15221198848, 'train_mem_gpu_peaked_delta': 57475786752, 'before_init_mem_cpu': 6040457216, 'before_init_mem_gpu': 512, 'epoch': 3.0}

=== GPU Memory Usage After Training Complete ===
GPU 0: 0.06GB/79.14GB (0.1%) - Free: 79.08GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
✅ DeepSpeed training completed in 0.70 hours!
