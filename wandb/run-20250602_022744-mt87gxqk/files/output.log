Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 135.07it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing multilingual dataset junkim100/multilingual_instruction_tuning_lima_bactrian with max_length=9064
Process 0: Supported languages: ['de', 'es', 'fr', 'it']
❌ Process 0: Error during training: 'validation'
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 713, in train_model
    dataset = prepare_dataset(
  File "/data_x/junkim100/projects/translation_it/train.py", line 419, in prepare_dataset
    f"Process {rank}: Dataset loaded - Train: {len(dataset['train'])}, Val: {len(dataset['validation'])}, Test: {len(dataset['test'])}"
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/dataset_dict.py", line 81, in __getitem__
    return super().__getitem__(k)
KeyError: 'validation'
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 960, in <module>
    train_model(
  File "/data_x/junkim100/projects/translation_it/train.py", line 869, in train_model
    raise e
  File "/data_x/junkim100/projects/translation_it/train.py", line 713, in train_model
    dataset = prepare_dataset(
  File "/data_x/junkim100/projects/translation_it/train.py", line 419, in prepare_dataset
    f"Process {rank}: Dataset loaded - Train: {len(dataset['train'])}, Val: {len(dataset['validation'])}, Test: {len(dataset['test'])}"
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/dataset_dict.py", line 81, in __getitem__
    return super().__getitem__(k)
KeyError: 'validation'
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 960, in <module>
[rank0]:     train_model(
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 869, in train_model
[rank0]:     raise e
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 713, in train_model
[rank0]:     dataset = prepare_dataset(
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 419, in prepare_dataset
[rank0]:     f"Process {rank}: Dataset loaded - Train: {len(dataset['train'])}, Val: {len(dataset['validation'])}, Test: {len(dataset['test'])}"
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/dataset_dict.py", line 81, in __getitem__
[rank0]:     return super().__getitem__(k)
[rank0]: KeyError: 'validation'
