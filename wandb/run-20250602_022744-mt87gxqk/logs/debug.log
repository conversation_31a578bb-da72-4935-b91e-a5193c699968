2025-06-02 02:27:44,234 INFO    MainThread:3863813 [wandb_setup.py:_flush():70] Current SDK version is 0.19.11
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_setup.py:_flush():70] Configure stats pid to 3863813
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_setup.py:_flush():70] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_setup.py:_flush():70] Loading settings from /data_x/junkim100/projects/translation_it/wandb/settings
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_setup.py:_flush():70] Loading settings from environment variables
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_init.py:setup_run_log_directory():724] Logging user logs to /data_x/junkim100/projects/translation_it/wandb/run-20250602_022744-mt87gxqk/logs/debug.log
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_init.py:setup_run_log_directory():725] Logging internal logs to /data_x/junkim100/projects/translation_it/wandb/run-20250602_022744-mt87gxqk/logs/debug-internal.log
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_init.py:init():852] calling init triggers
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_init.py:init():857] wandb.init called with sweep_config: {}
config: {'model_name': 'meta-llama/Meta-Llama-3.1-8B', 'dataset_name': 'junkim100/multilingual_instruction_tuning_lima_bactrian', 'world_size': 4, 'max_length': 9064, 'chunk_size': 4096, 'overlap_tokens': 512, 'chunks_per_9k_sequence': 3, 'method': 'LoRA + DeepSpeed ZeRO-3 + Sequence Chunking', 'lora_rank': 16, 'lora_alpha': 32, 'lora_dropout': 0.1, 'trainable_params': '42M', 'trainable_percent': '0.52%', 'deepspeed_stage': 3, 'per_device_batch_size': 1, 'gradient_accumulation_steps': 8, 'effective_batch_size': 32, 'learning_rate': 0.0001, 'supported_languages': ['de', 'es', 'fr', 'it'], 'reasoning_tokens': True, 'epochs': 2, 'chat_template_fixed': True, 'multilingual': True, '_wandb': {}}
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_init.py:init():893] starting backend
2025-06-02 02:27:44,235 INFO    MainThread:3863813 [wandb_init.py:init():897] sending inform_init request
2025-06-02 02:27:44,238 INFO    MainThread:3863813 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-06-02 02:27:44,238 INFO    MainThread:3863813 [wandb_init.py:init():907] backend started and connected
2025-06-02 02:27:44,245 INFO    MainThread:3863813 [wandb_init.py:init():1005] updated telemetry
2025-06-02 02:27:44,245 INFO    MainThread:3863813 [wandb_init.py:init():1029] communicating run to backend with 90.0 second timeout
2025-06-02 02:27:44,977 INFO    MainThread:3863813 [wandb_init.py:init():1104] starting run threads in backend
2025-06-02 02:27:45,117 INFO    MainThread:3863813 [wandb_run.py:_console_start():2573] atexit reg
2025-06-02 02:27:45,117 INFO    MainThread:3863813 [wandb_run.py:_redirect():2421] redirect: wrap_raw
2025-06-02 02:27:45,118 INFO    MainThread:3863813 [wandb_run.py:_redirect():2490] Wrapping output streams.
2025-06-02 02:27:45,118 INFO    MainThread:3863813 [wandb_run.py:_redirect():2513] Redirects installed.
2025-06-02 02:27:45,119 INFO    MainThread:3863813 [wandb_init.py:init():1150] run started, returning control to user process
