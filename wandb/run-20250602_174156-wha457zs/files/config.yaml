_wandb:
    value:
        cli_version: 0.19.11
        m: []
        python_version: 3.10.0
        t:
            "1":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
                - 105
            "2":
                - 1
                - 5
                - 11
                - 49
                - 51
                - 53
                - 55
                - 71
                - 98
                - 105
            "3":
                - 13
                - 15
                - 16
                - 23
                - 55
            "4": 3.10.0
            "5": 0.19.11
            "6": 4.52.4
            "8":
                - 5
            "12": 0.19.11
            "13": linux-x86_64
chat_template_fixed:
    value: true
dataset_name:
    value: junkim100/multilingual_instruction_tuning_lima_bactrian
deepspeed_stage:
    value: 3
effective_batch_size:
    value: 32
epochs:
    value: 2
gradient_accumulation_steps:
    value: 8
learning_rate:
    value: 0.0001
lora_alpha:
    value: 32
lora_dropout:
    value: 0.1
lora_rank:
    value: 16
max_length:
    value: 4096
method:
    value: LoRA + DeepSpeed ZeRO-3
model_name:
    value: meta-llama/Meta-Llama-3.1-8B
multilingual:
    value: true
per_device_batch_size:
    value: 1
reasoning_tokens:
    value: true
supported_languages:
    value:
        - de
        - es
        - fr
        - it
trainable_params:
    value: 42M
trainable_percent:
    value: 0.52%
world_size:
    value: 4
