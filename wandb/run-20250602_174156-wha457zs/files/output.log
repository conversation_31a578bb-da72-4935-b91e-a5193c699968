Process 0: Loading model and tokenizer for max_length=4096
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 131.87it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=4096
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/multilingual_instruction_tuning_lima_bactrian with max_length=4096
Process 0: Dataset loaded - Train: 54437, Val: 6805
Formatting dataset: 100%|██████████████████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [00:01<00:00, 30913.31 examples/s]
Formatting dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:00<00:00, 31214.58 examples/s]
Formatting dataset: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:00<00:00, 22974.17 examples/s]
Tokenizing dataset: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [00:40<00:00, 1343.89 examples/s]
Tokenizing dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:04<00:00, 1458.59 examples/s]
Tokenizing dataset: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:04<00:00, 1395.39 examples/s]
Filter: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 54437/54437 [00:19<00:00, 2852.04 examples/s]
Filter: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:02<00:00, 2780.14 examples/s]
Filter: 100%|█████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 6805/6805 [00:02<00:00, 2931.89 examples/s]
Dataset filtering results:
  Train: 54437 -> 54220 (99.6% retained)
  Val: 6805 -> 6780 (99.6% retained)
  Sequence lengths - Min: 51, Max: 4096, Mean: 400.0
Process 0: Dataset prepared and synchronized
[2025-06-02 17:44:24,301] [INFO] [comm.py:669:init_distributed] cdb=None
❌ Process 0: Error during training: Expected a string path to an existing deepspeed config, or a dictionary, or a base64 encoded string. Received: deepspeed_config_zero2.json
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 148, in __init__
    config = json.loads(config_file_or_dict)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 151, in __init__
    config_decoded = base64.urlsafe_b64decode(config_file_or_dict).decode("utf-8")
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/base64.py", line 133, in urlsafe_b64decode
    return b64decode(s)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/base64.py", line 87, in b64decode
    return binascii.a2b_base64(s)
binascii.Error: Incorrect padding

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 654, in train_model
    training_args = TrainingArguments(
  File "<string>", line 131, in __init__
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/training_args.py", line 2002, in __post_init__
    self.hf_deepspeed_config = HfTrainerDeepSpeedConfig(self.deepspeed)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/integrations/deepspeed.py", line 89, in __init__
    super().__init__(config_file_or_dict)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/integrations/deepspeed.py", line 79, in __init__
    super().__init__(config_file_or_dict)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 154, in __init__
    raise ValueError(
ValueError: Expected a string path to an existing deepspeed config, or a dictionary, or a base64 encoded string. Received: deepspeed_config_zero2.json
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 148, in __init__
    config = json.loads(config_file_or_dict)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/__init__.py", line 346, in loads
    return _default_decoder.decode(s)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 151, in __init__
    config_decoded = base64.urlsafe_b64decode(config_file_or_dict).decode("utf-8")
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/base64.py", line 133, in urlsafe_b64decode
    return b64decode(s)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/base64.py", line 87, in b64decode
    return binascii.a2b_base64(s)
binascii.Error: Incorrect padding

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 886, in <module>
    train_model(
  File "/data_x/junkim100/projects/translation_it/train.py", line 796, in train_model
    raise e
  File "/data_x/junkim100/projects/translation_it/train.py", line 654, in train_model
    training_args = TrainingArguments(
  File "<string>", line 131, in __init__
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/training_args.py", line 2002, in __post_init__
    self.hf_deepspeed_config = HfTrainerDeepSpeedConfig(self.deepspeed)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/integrations/deepspeed.py", line 89, in __init__
    super().__init__(config_file_or_dict)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/integrations/deepspeed.py", line 79, in __init__
    super().__init__(config_file_or_dict)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 154, in __init__
    raise ValueError(
ValueError: Expected a string path to an existing deepspeed config, or a dictionary, or a base64 encoded string. Received: deepspeed_config_zero2.json
[rank0]: Traceback (most recent call last):
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 148, in __init__
[rank0]:     config = json.loads(config_file_or_dict)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/__init__.py", line 346, in loads
[rank0]:     return _default_decoder.decode(s)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/decoder.py", line 337, in decode
[rank0]:     obj, end = self.raw_decode(s, idx=_w(s, 0).end())
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/json/decoder.py", line 355, in raw_decode
[rank0]:     raise JSONDecodeError("Expecting value", s, err.value) from None
[rank0]: json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

[rank0]: During handling of the above exception, another exception occurred:

[rank0]: Traceback (most recent call last):
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 151, in __init__
[rank0]:     config_decoded = base64.urlsafe_b64decode(config_file_or_dict).decode("utf-8")
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/base64.py", line 133, in urlsafe_b64decode
[rank0]:     return b64decode(s)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/base64.py", line 87, in b64decode
[rank0]:     return binascii.a2b_base64(s)
[rank0]: binascii.Error: Incorrect padding

[rank0]: During handling of the above exception, another exception occurred:

[rank0]: Traceback (most recent call last):
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 886, in <module>
[rank0]:     train_model(
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 796, in train_model
[rank0]:     raise e
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 654, in train_model
[rank0]:     training_args = TrainingArguments(
[rank0]:   File "<string>", line 131, in __init__
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/training_args.py", line 2002, in __post_init__
[rank0]:     self.hf_deepspeed_config = HfTrainerDeepSpeedConfig(self.deepspeed)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/integrations/deepspeed.py", line 89, in __init__
[rank0]:     super().__init__(config_file_or_dict)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/transformers/integrations/deepspeed.py", line 79, in __init__
[rank0]:     super().__init__(config_file_or_dict)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 154, in __init__
[rank0]:     raise ValueError(
[rank0]: ValueError: Expected a string path to an existing deepspeed config, or a dictionary, or a base64 encoded string. Received: deepspeed_config_zero2.json
