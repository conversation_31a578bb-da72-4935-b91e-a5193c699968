Process 0: Loading model and tokenizer for max_length=4096
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 121.50it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=4096
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/multilingual_instruction_tuning_lima_bactrian with max_length=4096
❌ Process 0: Error during training: Config name is missing.
Please pick one among the available configs: ['bactrian', 'combined', 'lima']
Example of usage:
	`load_dataset('junkim100/multilingual_instruction_tuning_lima_bactrian', 'bactrian')`
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 561, in train_model
    dataset = prepare_dataset(
  File "/data_x/junkim100/projects/translation_it/train.py", line 386, in prepare_dataset
    dataset = load_dataset(dataset_name)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/load.py", line 2062, in load_dataset
    builder_instance = load_dataset_builder(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/load.py", line 1819, in load_dataset_builder
    builder_instance: DatasetBuilder = builder_cls(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/builder.py", line 343, in __init__
    self.config, self.config_id = self._create_builder_config(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/builder.py", line 555, in _create_builder_config
    raise ValueError(
ValueError: Config name is missing.
Please pick one among the available configs: ['bactrian', 'combined', 'lima']
Example of usage:
	`load_dataset('junkim100/multilingual_instruction_tuning_lima_bactrian', 'bactrian')`
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 802, in <module>
    train_model(
  File "/data_x/junkim100/projects/translation_it/train.py", line 712, in train_model
    raise e
  File "/data_x/junkim100/projects/translation_it/train.py", line 561, in train_model
    dataset = prepare_dataset(
  File "/data_x/junkim100/projects/translation_it/train.py", line 386, in prepare_dataset
    dataset = load_dataset(dataset_name)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/load.py", line 2062, in load_dataset
    builder_instance = load_dataset_builder(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/load.py", line 1819, in load_dataset_builder
    builder_instance: DatasetBuilder = builder_cls(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/builder.py", line 343, in __init__
    self.config, self.config_id = self._create_builder_config(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/builder.py", line 555, in _create_builder_config
    raise ValueError(
ValueError: Config name is missing.
Please pick one among the available configs: ['bactrian', 'combined', 'lima']
Example of usage:
	`load_dataset('junkim100/multilingual_instruction_tuning_lima_bactrian', 'bactrian')`
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 802, in <module>
[rank0]:     train_model(
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 712, in train_model
[rank0]:     raise e
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 561, in train_model
[rank0]:     dataset = prepare_dataset(
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 386, in prepare_dataset
[rank0]:     dataset = load_dataset(dataset_name)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/load.py", line 2062, in load_dataset
[rank0]:     builder_instance = load_dataset_builder(
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/load.py", line 1819, in load_dataset_builder
[rank0]:     builder_instance: DatasetBuilder = builder_cls(
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/builder.py", line 343, in __init__
[rank0]:     self.config, self.config_id = self._create_builder_config(
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/builder.py", line 555, in _create_builder_config
[rank0]:     raise ValueError(
[rank0]: ValueError: Config name is missing.
[rank0]: Please pick one among the available configs: ['bactrian', 'combined', 'lima']
[rank0]: Example of usage:
[rank0]: 	`load_dataset('junkim100/multilingual_instruction_tuning_lima_bactrian', 'bactrian')`
