Process 0: Loading model and tokenizer for max_length=9064
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 133.45it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=9064
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 10,485,760 || all params: 8,040,779,776 || trainable%: 0.1304
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/Lima-X-Processed with max_length=9064
Process 0: Dataset loaded - Train: 1030, Val: 52
Dataset filtering results:
  Train: 1030 -> 1030 (100.0% retained)
  Val: 52 -> 52 (100.0% retained)
  Sequence lengths - Min: 76, Max: 9064, Mean: 1516.7
Process 0: Dataset prepared and synchronized
[2025-06-01 03:02:12,765] [INFO] [comm.py:669:init_distributed] cdb=None
🔧 ChunkedSequenceDataCollator initialized:
   Max sequence length: 9064
   Chunk size: 4096
   Overlap: 512
   Effective chunks per 9k sequence: 3

=== GPU Memory Usage Before Trainer Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
/data_x/junkim100/projects/translation_it/train.py:736: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `Trainer.__init__`. Use `processing_class` instead.
  trainer = Trainer(
No label_names provided for model class `PeftModelForCausalLM`. Since `PeftModel` hides base models input arguments, if label_names is not given, label_names can't be set automatically within `Trainer`. Note that empty label_names list will be used instead.

=== GPU Memory Usage After DeepSpeed Init ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
🚀 Starting DeepSpeed training with sequence chunking on 4 GPUs...
   - Max sequence length: 9064 (chunked into 4k pieces)
   - Chunk size: 4096 tokens with 512 token overlap
   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)
   - Batch size per device: 1
   - Gradient accumulation steps: 8
   - Base effective batch size: 32
   - Note: Actual batch size will be ~3x higher due to chunking
Installed CUDA version 12.4 does not match the version torch was compiled with 12.6 but since the APIs are compatible, accepting this combination
Using /mnt/raid6/junkim100/.cache/torch_extensions/py310_cu126 as PyTorch extensions root...
Loading extension module cpu_adam...
Time to load cpu_adam op: 2.427069664001465 seconds
Parameter Offload: Total persistent parameters: 5246976 in 417 params
[2025-06-01 03:02:37,841] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 66/66 [27:20<00:00, 18.34s/it]/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/peft/utils/save_and_load.py:250: UserWarning: Setting `save_embedding_layers` to `True` as the embedding layer has been resized during finetuning.
{'loss': 4.315, 'grad_norm': 7.383979320526123, 'learning_rate': 0.0, 'epoch': 0.03}
{'loss': 4.2646, 'grad_norm': 8.072896957397461, 'learning_rate': 0.0001, 'epoch': 0.16}
{'loss': 3.2872, 'grad_norm': 4.61266565322876, 'learning_rate': 0.0001, 'epoch': 0.31}
{'loss': 2.1875, 'grad_norm': 2.2551679611206055, 'learning_rate': 0.0001, 'epoch': 0.47}
{'loss': 1.7962, 'grad_norm': 1.418757438659668, 'learning_rate': 0.0001, 'epoch': 0.62}
{'loss': 1.6533, 'grad_norm': 1.2362189292907715, 'learning_rate': 0.0001, 'epoch': 0.78}
[2025-06-01 03:13:43,976] [WARNING] [stage3.py:2148:step] 1 pytorch allocator cache flushes since last step. this happens when there is high memory pressure and is detrimental to performance. if this is happening frequently consider adjusting settings to reduce memory consumption. If you are unable to make the cache flushes go away consider adding get_accelerator().empty_cache() calls in your training loop to ensure that all ranks flush their caches at the same time
{'loss': 1.5058, 'grad_norm': 0.5351508259773254, 'learning_rate': 0.0001, 'epoch': 0.93}
{'loss': 1.4164, 'grad_norm': 0.7337934374809265, 'learning_rate': 0.0001, 'epoch': 1.06}
{'loss': 1.3701, 'grad_norm': 0.4597868025302887, 'learning_rate': 0.0001, 'epoch': 1.22}
{'loss': 1.2969, 'grad_norm': 0.37811875343322754, 'learning_rate': 0.0001, 'epoch': 1.37}
{'loss': 1.2338, 'grad_norm': 0.45720070600509644, 'learning_rate': 0.0001, 'epoch': 1.53}
{'loss': 1.2294, 'grad_norm': 0.6168062686920166, 'learning_rate': 0.0001, 'epoch': 1.68}
{'loss': 1.1456, 'grad_norm': 0.6981340646743774, 'learning_rate': 0.0001, 'epoch': 1.84}
{'loss': 1.1643, 'grad_norm': 0.5203914642333984, 'learning_rate': 0.0001, 'epoch': 1.99}
  warnings.warn(
100%|██████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 66/66 [27:56<00:00, 25.40s/it]
{'train_runtime': 1674.598, 'train_samples_per_second': 1.23, 'train_steps_per_second': 0.039, 'train_loss': 1.8045227292812231, 'init_mem_cpu_alloc_delta': 12288, 'init_mem_gpu_alloc_delta': 0, 'init_mem_cpu_peaked_delta': 0, 'init_mem_gpu_peaked_delta': 0, 'train_mem_cpu_alloc_delta': 2128101376, 'train_mem_gpu_alloc_delta': 61125632, 'train_mem_cpu_peaked_delta': 17266798592, 'train_mem_gpu_peaked_delta': 57475871232, 'before_init_mem_cpu': 6059622400, 'before_init_mem_gpu': 512, 'epoch': 2.0}

=== GPU Memory Usage After Training Complete ===
GPU 0: 0.06GB/79.14GB (0.1%) - Free: 79.08GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
✅ DeepSpeed training completed in 0.47 hours!
