Process 0: Loading model and tokenizer for max_length=4096
Process 0: DeepSpeed mode - quantization disabled for compatibility
Process 0: Tokenizer loaded with vocab size: 128260
Process 0: Loading model from meta-llama/Meta-Llama-3.1-8B
Loading checkpoint shards: 100%|████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:00<00:00, 134.92it/s]
Process 0: Resizing token embeddings from 128256 to 128260
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
Process 0: Model loaded on CPU, DeepSpeed will handle device placement
Process 0: Passed model loading barrier

=== GPU Memory Usage After Model Loading ===
GPU 0: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 1: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 2: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
GPU 3: 0.00GB/79.14GB (0.0%) - Free: 79.14GB
==================================================
Process 0: Setting up LoRA for max_length=4096
Process 0: Using LoRA target modules: ['q_proj', 'k_proj', 'v_proj', 'o_proj', 'gate_proj', 'up_proj', 'down_proj']
Process 0: LoRA setup complete, DeepSpeed will handle gradient checkpointing
trainable params: 41,943,040 || all params: 8,072,237,056 || trainable%: 0.5196
Process 0: LoRA model prepared for DeepSpeed initialization
Process 0: Passed LoRA setup barrier
Process 0: Preparing dataset junkim100/multilingual_instruction_tuning_lima_bactrian with max_length=4096
Process 0: Dataset loaded - Train: 54437, Val: 6805
Formatting dataset:   0%|                                                                                                                     | 0/54437 [00:00<?, ? examples/s]
❌ Process 0: Error during training: "['de', 'es', 'fr', 'it']_input"
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 561, in train_model
    dataset = prepare_dataset(
  File "/data_x/junkim100/projects/translation_it/train.py", line 401, in prepare_dataset
    dataset = dataset.map(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/dataset_dict.py", line 944, in map
    dataset_dict[split] = dataset.map(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 557, in wrapper
    out: Union["Dataset", "DatasetDict"] = func(self, *args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3079, in map
    for rank, done, content in Dataset._map_single(**dataset_kwargs):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3525, in _map_single
    for i, batch in iter_outputs(shard_iterable):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3475, in iter_outputs
    yield i, apply_function(example, i, offset=offset)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3398, in apply_function
    processed_inputs = function(*fn_args, *additional_args, **fn_kwargs)
  File "/data_x/junkim100/projects/translation_it/train.py", line 394, in format_and_filter
    formatted = format_reasoning_chat_template(examples, tokenizer, target_language)
  File "/data_x/junkim100/projects/translation_it/train.py", line 346, in format_reasoning_chat_template
    for i in range(len(examples[input_column])):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/formatting/formatting.py", line 278, in __getitem__
    value = self.data[key]
KeyError: "['de', 'es', 'fr', 'it']_input"
Traceback (most recent call last):
  File "/data_x/junkim100/projects/translation_it/train.py", line 802, in <module>
    train_model(
  File "/data_x/junkim100/projects/translation_it/train.py", line 712, in train_model
    raise e
  File "/data_x/junkim100/projects/translation_it/train.py", line 561, in train_model
    dataset = prepare_dataset(
  File "/data_x/junkim100/projects/translation_it/train.py", line 401, in prepare_dataset
    dataset = dataset.map(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/dataset_dict.py", line 944, in map
    dataset_dict[split] = dataset.map(
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 557, in wrapper
    out: Union["Dataset", "DatasetDict"] = func(self, *args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3079, in map
    for rank, done, content in Dataset._map_single(**dataset_kwargs):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3525, in _map_single
    for i, batch in iter_outputs(shard_iterable):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3475, in iter_outputs
    yield i, apply_function(example, i, offset=offset)
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3398, in apply_function
    processed_inputs = function(*fn_args, *additional_args, **fn_kwargs)
  File "/data_x/junkim100/projects/translation_it/train.py", line 394, in format_and_filter
    formatted = format_reasoning_chat_template(examples, tokenizer, target_language)
  File "/data_x/junkim100/projects/translation_it/train.py", line 346, in format_reasoning_chat_template
    for i in range(len(examples[input_column])):
  File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/formatting/formatting.py", line 278, in __getitem__
    value = self.data[key]
KeyError: "['de', 'es', 'fr', 'it']_input"
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 802, in <module>
[rank0]:     train_model(
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 712, in train_model
[rank0]:     raise e
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 561, in train_model
[rank0]:     dataset = prepare_dataset(
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 401, in prepare_dataset
[rank0]:     dataset = dataset.map(
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/dataset_dict.py", line 944, in map
[rank0]:     dataset_dict[split] = dataset.map(
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 557, in wrapper
[rank0]:     out: Union["Dataset", "DatasetDict"] = func(self, *args, **kwargs)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3079, in map
[rank0]:     for rank, done, content in Dataset._map_single(**dataset_kwargs):
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3525, in _map_single
[rank0]:     for i, batch in iter_outputs(shard_iterable):
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3475, in iter_outputs
[rank0]:     yield i, apply_function(example, i, offset=offset)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/arrow_dataset.py", line 3398, in apply_function
[rank0]:     processed_inputs = function(*fn_args, *additional_args, **fn_kwargs)
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 394, in format_and_filter
[rank0]:     formatted = format_reasoning_chat_template(examples, tokenizer, target_language)
[rank0]:   File "/data_x/junkim100/projects/translation_it/train.py", line 346, in format_reasoning_chat_template
[rank0]:     for i in range(len(examples[input_column])):
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/it/lib/python3.10/site-packages/datasets/formatting/formatting.py", line 278, in __getitem__
[rank0]:     value = self.data[key]
[rank0]: KeyError: "['de', 'es', 'fr', 'it']_input"
