version: '3.8'

services:
  ml-training:
    build:
      context: .
      dockerfile: Dockerfile
    image: ml-training:cuda12.6
    container_name: ml-training-env
    
    # GPU support
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu]
    
    # Environment variables
    environment:
      - NVIDIA_VISIBLE_DEVICES=0,1,2,3  # Use GPUs 0-3 as requested
      - CUDA_VISIBLE_DEVICES=0,1,2,3
      - WANDB_API_KEY=${WANDB_API_KEY:-}
      - HF_TOKEN=${HF_TOKEN:-}
    
    # Volume mounts
    volumes:
      - ./:/workspace/translation_it  # Mount current directory
      - ~/.cache/huggingface:/home/<USER>/.cache/huggingface  # HF cache
      - ~/.wandb:/home/<USER>/.wandb  # WandB cache
      - /tmp/.X11-unix:/tmp/.X11-unix:rw  # For GUI apps if needed
    
    # Network settings
    ports:
      - "8888:8888"  # Jupy<PERSON>
      - "6006:6006"  # TensorBoard
      - "7860:7860"  # Gradio/Streamlit
    
    # Keep container running
    stdin_open: true
    tty: true
    
    # Working directory
    working_dir: /workspace/translation_it
    
    # Command to run
    command: /bin/bash
    
    # Shared memory size for PyTorch DataLoader
    shm_size: '16gb'
    
    # Resource limits
    mem_limit: 64g
    memswap_limit: 64g
