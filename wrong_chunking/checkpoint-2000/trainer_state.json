{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.1800073773515307, "eval_steps": 100, "global_step": 2000, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0005901881224640354, "grad_norm": 12.449291229248047, "learning_rate": 0.0, "loss": 5.7477, "step": 1}, {"epoch": 0.002950940612320177, "grad_norm": 10.512336730957031, "learning_rate": 3.133763780181526e-05, "loss": 5.4059, "step": 5}, {"epoch": 0.005901881224640354, "grad_norm": 9.367112159729004, "learning_rate": 4.4834023788451716e-05, "loss": 4.4219, "step": 10}, {"epoch": 0.00885282183696053, "grad_norm": 4.600208759307861, "learning_rate": 5.2728903485892547e-05, "loss": 3.4167, "step": 15}, {"epoch": 0.011803762449280709, "grad_norm": 2.3552050590515137, "learning_rate": 5.833040977508817e-05, "loss": 2.6622, "step": 20}, {"epoch": 0.014754703061600885, "grad_norm": 1.5560933351516724, "learning_rate": 6.267527560363053e-05, "loss": 2.0824, "step": 25}, {"epoch": 0.01770564367392106, "grad_norm": 1.1214321851730347, "learning_rate": 6.622528947252899e-05, "loss": 1.8801, "step": 30}, {"epoch": 0.020656584286241238, "grad_norm": 1.2868268489837646, "learning_rate": 6.922678343138837e-05, "loss": 1.618, "step": 35}, {"epoch": 0.023607524898561418, "grad_norm": 0.9000183343887329, "learning_rate": 7.182679576172462e-05, "loss": 1.6211, "step": 40}, {"epoch": 0.026558465510881594, "grad_norm": 1.33198881149292, "learning_rate": 7.412016916996981e-05, "loss": 1.5029, "step": 45}, {"epoch": 0.02950940612320177, "grad_norm": 0.9966739416122437, "learning_rate": 7.617166159026699e-05, "loss": 1.4657, "step": 50}, {"epoch": 0.03246034673552195, "grad_norm": 0.8287239670753479, "learning_rate": 7.802746222131873e-05, "loss": 1.4152, "step": 55}, {"epoch": 0.03541128734784212, "grad_norm": 0.6737128496170044, "learning_rate": 7.972167545916544e-05, "loss": 1.3517, "step": 60}, {"epoch": 0.0383622279601623, "grad_norm": 0.7575621604919434, "learning_rate": 8.128020055812762e-05, "loss": 1.3888, "step": 65}, {"epoch": 0.041313168572482475, "grad_norm": 0.6198861598968506, "learning_rate": 8.272316941802484e-05, "loss": 1.3403, "step": 70}, {"epoch": 0.044264109184802655, "grad_norm": 0.7704681754112244, "learning_rate": 8.40665412877078e-05, "loss": 1.2877, "step": 75}, {"epoch": 0.047215049797122835, "grad_norm": 0.753148078918457, "learning_rate": 8.532318174836105e-05, "loss": 1.3217, "step": 80}, {"epoch": 0.05016599040944301, "grad_norm": 0.6373308897018433, "learning_rate": 8.650361401336356e-05, "loss": 1.3163, "step": 85}, {"epoch": 0.05311693102176319, "grad_norm": 0.6265484094619751, "learning_rate": 8.761655515660627e-05, "loss": 1.2969, "step": 90}, {"epoch": 0.05606787163408337, "grad_norm": 0.6926285624504089, "learning_rate": 8.86693071665027e-05, "loss": 1.3712, "step": 95}, {"epoch": 0.05901881224640354, "grad_norm": 0.6044808030128479, "learning_rate": 8.966804757690343e-05, "loss": 1.3011, "step": 100}, {"epoch": 0.05901881224640354, "eval_loss": 1.4924620389938354, "eval_runtime": 246.2907, "eval_samples_per_second": 27.528, "eval_steps_per_second": 0.861, "step": 100}, {"epoch": 0.06196975285872372, "grad_norm": 0.5741841793060303, "learning_rate": 9.061804911546565e-05, "loss": 1.241, "step": 105}, {"epoch": 0.0649206934710439, "grad_norm": 0.6286064982414246, "learning_rate": 9.152384820795519e-05, "loss": 1.2734, "step": 110}, {"epoch": 0.06787163408336407, "grad_norm": 0.6774728298187256, "learning_rate": 9.23893759952249e-05, "loss": 1.3588, "step": 115}, {"epoch": 0.07082257469568425, "grad_norm": 0.5251493453979492, "learning_rate": 9.321806144580188e-05, "loss": 1.2822, "step": 120}, {"epoch": 0.07377351530800443, "grad_norm": 0.7161235213279724, "learning_rate": 9.40129134054458e-05, "loss": 1.274, "step": 125}, {"epoch": 0.0767244559203246, "grad_norm": 0.6714425086975098, "learning_rate": 9.477658654476408e-05, "loss": 1.2974, "step": 130}, {"epoch": 0.07967539653264478, "grad_norm": 0.6113528609275818, "learning_rate": 9.55114348540471e-05, "loss": 1.234, "step": 135}, {"epoch": 0.08262633714496495, "grad_norm": 0.5702859163284302, "learning_rate": 9.621955540466126e-05, "loss": 1.2742, "step": 140}, {"epoch": 0.08557727775728513, "grad_norm": 0.5634028911590576, "learning_rate": 9.690282442780122e-05, "loss": 1.2137, "step": 145}, {"epoch": 0.08852821836960531, "grad_norm": 0.6437089443206787, "learning_rate": 9.756292727434425e-05, "loss": 1.2929, "step": 150}, {"epoch": 0.09147915898192549, "grad_norm": 0.5431512594223022, "learning_rate": 9.82013834603667e-05, "loss": 1.2541, "step": 155}, {"epoch": 0.09443009959424567, "grad_norm": 0.6479554772377014, "learning_rate": 9.881956773499751e-05, "loss": 1.228, "step": 160}, {"epoch": 0.09738104020656584, "grad_norm": 0.6105641722679138, "learning_rate": 9.941872790539601e-05, "loss": 1.2546, "step": 165}, {"epoch": 0.10033198081888602, "grad_norm": 0.6162766218185425, "learning_rate": 0.0001, "loss": 1.2734, "step": 170}, {"epoch": 0.1032829214312062, "grad_norm": 0.6421405076980591, "learning_rate": 0.0001, "loss": 1.2618, "step": 175}, {"epoch": 0.10623386204352638, "grad_norm": 0.6490446925163269, "learning_rate": 0.0001, "loss": 1.2152, "step": 180}, {"epoch": 0.10918480265584656, "grad_norm": 0.7280729413032532, "learning_rate": 0.0001, "loss": 1.2847, "step": 185}, {"epoch": 0.11213574326816673, "grad_norm": 0.5215629935264587, "learning_rate": 0.0001, "loss": 1.239, "step": 190}, {"epoch": 0.1150866838804869, "grad_norm": 0.5083969831466675, "learning_rate": 0.0001, "loss": 1.2427, "step": 195}, {"epoch": 0.11803762449280708, "grad_norm": 0.5302383899688721, "learning_rate": 0.0001, "loss": 1.2721, "step": 200}, {"epoch": 0.11803762449280708, "eval_loss": 1.4297910928726196, "eval_runtime": 250.6542, "eval_samples_per_second": 27.049, "eval_steps_per_second": 0.846, "step": 200}, {"epoch": 0.12098856510512726, "grad_norm": 0.5137877464294434, "learning_rate": 0.0001, "loss": 1.2549, "step": 205}, {"epoch": 0.12393950571744744, "grad_norm": 0.6379396319389343, "learning_rate": 0.0001, "loss": 1.3315, "step": 210}, {"epoch": 0.12689044632976762, "grad_norm": 0.5948516726493835, "learning_rate": 0.0001, "loss": 1.255, "step": 215}, {"epoch": 0.1298413869420878, "grad_norm": 0.5186414122581482, "learning_rate": 0.0001, "loss": 1.2639, "step": 220}, {"epoch": 0.13279232755440798, "grad_norm": 0.5251172780990601, "learning_rate": 0.0001, "loss": 1.2426, "step": 225}, {"epoch": 0.13574326816672813, "grad_norm": 0.48480507731437683, "learning_rate": 0.0001, "loss": 1.2152, "step": 230}, {"epoch": 0.1386942087790483, "grad_norm": 0.47951027750968933, "learning_rate": 0.0001, "loss": 1.261, "step": 235}, {"epoch": 0.1416451493913685, "grad_norm": 0.5970630645751953, "learning_rate": 0.0001, "loss": 1.2475, "step": 240}, {"epoch": 0.14459609000368867, "grad_norm": 0.5175691246986389, "learning_rate": 0.0001, "loss": 1.2518, "step": 245}, {"epoch": 0.14754703061600885, "grad_norm": 0.6099365949630737, "learning_rate": 0.0001, "loss": 1.2629, "step": 250}, {"epoch": 0.15049797122832903, "grad_norm": 0.44505590200424194, "learning_rate": 0.0001, "loss": 1.1788, "step": 255}, {"epoch": 0.1534489118406492, "grad_norm": 0.5684008598327637, "learning_rate": 0.0001, "loss": 1.2755, "step": 260}, {"epoch": 0.1563998524529694, "grad_norm": 0.5378715395927429, "learning_rate": 0.0001, "loss": 1.2402, "step": 265}, {"epoch": 0.15935079306528957, "grad_norm": 0.4710020124912262, "learning_rate": 0.0001, "loss": 1.2311, "step": 270}, {"epoch": 0.16230173367760975, "grad_norm": 0.4516514539718628, "learning_rate": 0.0001, "loss": 1.2385, "step": 275}, {"epoch": 0.1652526742899299, "grad_norm": 0.46083661913871765, "learning_rate": 0.0001, "loss": 1.2273, "step": 280}, {"epoch": 0.16820361490225008, "grad_norm": 0.585292398929596, "learning_rate": 0.0001, "loss": 1.2456, "step": 285}, {"epoch": 0.17115455551457026, "grad_norm": 0.4513900876045227, "learning_rate": 0.0001, "loss": 1.1616, "step": 290}, {"epoch": 0.17410549612689044, "grad_norm": 0.47135844826698303, "learning_rate": 0.0001, "loss": 1.23, "step": 295}, {"epoch": 0.17705643673921062, "grad_norm": 0.5187608003616333, "learning_rate": 0.0001, "loss": 1.2186, "step": 300}, {"epoch": 0.17705643673921062, "eval_loss": 1.4048868417739868, "eval_runtime": 248.3007, "eval_samples_per_second": 27.306, "eval_steps_per_second": 0.854, "step": 300}, {"epoch": 0.1800073773515308, "grad_norm": 0.5357810258865356, "learning_rate": 0.0001, "loss": 1.1953, "step": 305}, {"epoch": 0.18295831796385098, "grad_norm": 0.5217350125312805, "learning_rate": 0.0001, "loss": 1.1933, "step": 310}, {"epoch": 0.18590925857617116, "grad_norm": 0.4613063335418701, "learning_rate": 0.0001, "loss": 1.1594, "step": 315}, {"epoch": 0.18886019918849134, "grad_norm": 0.4805125296115875, "learning_rate": 0.0001, "loss": 1.223, "step": 320}, {"epoch": 0.19181113980081152, "grad_norm": 0.5022006630897522, "learning_rate": 0.0001, "loss": 1.1926, "step": 325}, {"epoch": 0.19476208041313167, "grad_norm": 0.4623624086380005, "learning_rate": 0.0001, "loss": 1.2153, "step": 330}, {"epoch": 0.19771302102545185, "grad_norm": 0.44568437337875366, "learning_rate": 0.0001, "loss": 1.2157, "step": 335}, {"epoch": 0.20066396163777203, "grad_norm": 0.5664547681808472, "learning_rate": 0.0001, "loss": 1.195, "step": 340}, {"epoch": 0.2036149022500922, "grad_norm": 0.4419609010219574, "learning_rate": 0.0001, "loss": 1.2118, "step": 345}, {"epoch": 0.2065658428624124, "grad_norm": 0.47520682215690613, "learning_rate": 0.0001, "loss": 1.2231, "step": 350}, {"epoch": 0.20951678347473257, "grad_norm": 0.5029681324958801, "learning_rate": 0.0001, "loss": 1.1941, "step": 355}, {"epoch": 0.21246772408705275, "grad_norm": 0.5756416320800781, "learning_rate": 0.0001, "loss": 1.221, "step": 360}, {"epoch": 0.21541866469937293, "grad_norm": 0.40774229168891907, "learning_rate": 0.0001, "loss": 1.2048, "step": 365}, {"epoch": 0.2183696053116931, "grad_norm": 0.5055027604103088, "learning_rate": 0.0001, "loss": 1.2308, "step": 370}, {"epoch": 0.2213205459240133, "grad_norm": 0.4239030182361603, "learning_rate": 0.0001, "loss": 1.2006, "step": 375}, {"epoch": 0.22427148653633347, "grad_norm": 0.3832748234272003, "learning_rate": 0.0001, "loss": 1.1934, "step": 380}, {"epoch": 0.22722242714865362, "grad_norm": 0.468159556388855, "learning_rate": 0.0001, "loss": 1.2679, "step": 385}, {"epoch": 0.2301733677609738, "grad_norm": 0.5038846135139465, "learning_rate": 0.0001, "loss": 1.213, "step": 390}, {"epoch": 0.23312430837329398, "grad_norm": 0.3974873721599579, "learning_rate": 0.0001, "loss": 1.2091, "step": 395}, {"epoch": 0.23607524898561416, "grad_norm": 0.4505026638507843, "learning_rate": 0.0001, "loss": 1.2665, "step": 400}, {"epoch": 0.23607524898561416, "eval_loss": 1.3894466161727905, "eval_runtime": 249.2843, "eval_samples_per_second": 27.198, "eval_steps_per_second": 0.85, "step": 400}, {"epoch": 0.23902618959793434, "grad_norm": 0.4718388020992279, "learning_rate": 0.0001, "loss": 1.2102, "step": 405}, {"epoch": 0.24197713021025452, "grad_norm": 0.38462600111961365, "learning_rate": 0.0001, "loss": 1.2079, "step": 410}, {"epoch": 0.2449280708225747, "grad_norm": 0.4475425183773041, "learning_rate": 0.0001, "loss": 1.201, "step": 415}, {"epoch": 0.24787901143489488, "grad_norm": 0.4842807650566101, "learning_rate": 0.0001, "loss": 1.2378, "step": 420}, {"epoch": 0.25082995204721503, "grad_norm": 0.40634164214134216, "learning_rate": 0.0001, "loss": 1.2478, "step": 425}, {"epoch": 0.25378089265953524, "grad_norm": 0.42216214537620544, "learning_rate": 0.0001, "loss": 1.2138, "step": 430}, {"epoch": 0.2567318332718554, "grad_norm": 0.39252954721450806, "learning_rate": 0.0001, "loss": 1.1823, "step": 435}, {"epoch": 0.2596827738841756, "grad_norm": 0.3978445529937744, "learning_rate": 0.0001, "loss": 1.1766, "step": 440}, {"epoch": 0.26263371449649575, "grad_norm": 0.3954154849052429, "learning_rate": 0.0001, "loss": 1.227, "step": 445}, {"epoch": 0.26558465510881596, "grad_norm": 0.5098206400871277, "learning_rate": 0.0001, "loss": 1.1863, "step": 450}, {"epoch": 0.2685355957211361, "grad_norm": 0.3866317570209503, "learning_rate": 0.0001, "loss": 1.1808, "step": 455}, {"epoch": 0.27148653633345626, "grad_norm": 0.42024534940719604, "learning_rate": 0.0001, "loss": 1.2234, "step": 460}, {"epoch": 0.27443747694577647, "grad_norm": 0.49251362681388855, "learning_rate": 0.0001, "loss": 1.2343, "step": 465}, {"epoch": 0.2773884175580966, "grad_norm": 0.38760024309158325, "learning_rate": 0.0001, "loss": 1.1916, "step": 470}, {"epoch": 0.28033935817041683, "grad_norm": 0.393055260181427, "learning_rate": 0.0001, "loss": 1.2364, "step": 475}, {"epoch": 0.283290298782737, "grad_norm": 0.3966905176639557, "learning_rate": 0.0001, "loss": 1.1791, "step": 480}, {"epoch": 0.2862412393950572, "grad_norm": 0.4205932319164276, "learning_rate": 0.0001, "loss": 1.2097, "step": 485}, {"epoch": 0.28919218000737734, "grad_norm": 0.40774476528167725, "learning_rate": 0.0001, "loss": 1.1786, "step": 490}, {"epoch": 0.29214312061969755, "grad_norm": 0.4860667288303375, "learning_rate": 0.0001, "loss": 1.2093, "step": 495}, {"epoch": 0.2950940612320177, "grad_norm": 0.38234424591064453, "learning_rate": 0.0001, "loss": 1.1644, "step": 500}, {"epoch": 0.2950940612320177, "eval_loss": 1.3818562030792236, "eval_runtime": 246.1763, "eval_samples_per_second": 27.541, "eval_steps_per_second": 0.861, "step": 500}, {"epoch": 0.2980450018443379, "grad_norm": 0.4049389660358429, "learning_rate": 0.0001, "loss": 1.2156, "step": 505}, {"epoch": 0.30099594245665806, "grad_norm": 0.3692502975463867, "learning_rate": 0.0001, "loss": 1.2037, "step": 510}, {"epoch": 0.3039468830689782, "grad_norm": 0.44128918647766113, "learning_rate": 0.0001, "loss": 1.2138, "step": 515}, {"epoch": 0.3068978236812984, "grad_norm": 0.4006088674068451, "learning_rate": 0.0001, "loss": 1.2531, "step": 520}, {"epoch": 0.3098487642936186, "grad_norm": 0.2942383885383606, "learning_rate": 0.0001, "loss": 1.1661, "step": 525}, {"epoch": 0.3127997049059388, "grad_norm": 0.40336400270462036, "learning_rate": 0.0001, "loss": 1.2258, "step": 530}, {"epoch": 0.31575064551825893, "grad_norm": 0.4106636643409729, "learning_rate": 0.0001, "loss": 1.2138, "step": 535}, {"epoch": 0.31870158613057914, "grad_norm": 0.4566124379634857, "learning_rate": 0.0001, "loss": 1.2023, "step": 540}, {"epoch": 0.3216525267428993, "grad_norm": 0.43914926052093506, "learning_rate": 0.0001, "loss": 1.18, "step": 545}, {"epoch": 0.3246034673552195, "grad_norm": 0.42770352959632874, "learning_rate": 0.0001, "loss": 1.2313, "step": 550}, {"epoch": 0.32755440796753965, "grad_norm": 0.41804003715515137, "learning_rate": 0.0001, "loss": 1.2465, "step": 555}, {"epoch": 0.3305053485798598, "grad_norm": 0.42947250604629517, "learning_rate": 0.0001, "loss": 1.1721, "step": 560}, {"epoch": 0.33345628919218, "grad_norm": 0.4126758277416229, "learning_rate": 0.0001, "loss": 1.1603, "step": 565}, {"epoch": 0.33640722980450016, "grad_norm": 0.3431655466556549, "learning_rate": 0.0001, "loss": 1.1716, "step": 570}, {"epoch": 0.33935817041682037, "grad_norm": 0.36367183923721313, "learning_rate": 0.0001, "loss": 1.1917, "step": 575}, {"epoch": 0.3423091110291405, "grad_norm": 0.37171483039855957, "learning_rate": 0.0001, "loss": 1.1811, "step": 580}, {"epoch": 0.34526005164146073, "grad_norm": 0.40305355191230774, "learning_rate": 0.0001, "loss": 1.2025, "step": 585}, {"epoch": 0.3482109922537809, "grad_norm": 0.6737216114997864, "learning_rate": 0.0001, "loss": 1.2184, "step": 590}, {"epoch": 0.3511619328661011, "grad_norm": 0.3790748417377472, "learning_rate": 0.0001, "loss": 1.1775, "step": 595}, {"epoch": 0.35411287347842124, "grad_norm": 0.3921985626220703, "learning_rate": 0.0001, "loss": 1.206, "step": 600}, {"epoch": 0.35411287347842124, "eval_loss": 1.3737961053848267, "eval_runtime": 247.3884, "eval_samples_per_second": 27.406, "eval_steps_per_second": 0.857, "step": 600}, {"epoch": 0.35706381409074145, "grad_norm": 0.35289466381073, "learning_rate": 0.0001, "loss": 1.211, "step": 605}, {"epoch": 0.3600147547030616, "grad_norm": 0.33914104104042053, "learning_rate": 0.0001, "loss": 1.1978, "step": 610}, {"epoch": 0.36296569531538175, "grad_norm": 0.37348440289497375, "learning_rate": 0.0001, "loss": 1.1504, "step": 615}, {"epoch": 0.36591663592770196, "grad_norm": 0.3958370089530945, "learning_rate": 0.0001, "loss": 1.2192, "step": 620}, {"epoch": 0.3688675765400221, "grad_norm": 0.39797595143318176, "learning_rate": 0.0001, "loss": 1.2206, "step": 625}, {"epoch": 0.3718185171523423, "grad_norm": 0.43255582451820374, "learning_rate": 0.0001, "loss": 1.1687, "step": 630}, {"epoch": 0.3747694577646625, "grad_norm": 0.4527657628059387, "learning_rate": 0.0001, "loss": 1.2094, "step": 635}, {"epoch": 0.3777203983769827, "grad_norm": 0.34012940526008606, "learning_rate": 0.0001, "loss": 1.1974, "step": 640}, {"epoch": 0.38067133898930283, "grad_norm": 0.36931538581848145, "learning_rate": 0.0001, "loss": 1.2065, "step": 645}, {"epoch": 0.38362227960162304, "grad_norm": 0.3569357097148895, "learning_rate": 0.0001, "loss": 1.1745, "step": 650}, {"epoch": 0.3865732202139432, "grad_norm": 0.34146326780319214, "learning_rate": 0.0001, "loss": 1.1673, "step": 655}, {"epoch": 0.38952416082626334, "grad_norm": 0.35260283946990967, "learning_rate": 0.0001, "loss": 1.1861, "step": 660}, {"epoch": 0.39247510143858355, "grad_norm": 0.4133412837982178, "learning_rate": 0.0001, "loss": 1.1988, "step": 665}, {"epoch": 0.3954260420509037, "grad_norm": 0.3880932629108429, "learning_rate": 0.0001, "loss": 1.2176, "step": 670}, {"epoch": 0.3983769826632239, "grad_norm": 0.33228799700737, "learning_rate": 0.0001, "loss": 1.1912, "step": 675}, {"epoch": 0.40132792327554406, "grad_norm": 0.3206666111946106, "learning_rate": 0.0001, "loss": 1.2024, "step": 680}, {"epoch": 0.40427886388786427, "grad_norm": 0.3273152709007263, "learning_rate": 0.0001, "loss": 1.1899, "step": 685}, {"epoch": 0.4072298045001844, "grad_norm": 0.40319663286209106, "learning_rate": 0.0001, "loss": 1.1743, "step": 690}, {"epoch": 0.41018074511250463, "grad_norm": 0.3892875909805298, "learning_rate": 0.0001, "loss": 1.1828, "step": 695}, {"epoch": 0.4131316857248248, "grad_norm": 0.39169618487358093, "learning_rate": 0.0001, "loss": 1.2053, "step": 700}, {"epoch": 0.4131316857248248, "eval_loss": 1.3690263032913208, "eval_runtime": 246.9137, "eval_samples_per_second": 27.459, "eval_steps_per_second": 0.859, "step": 700}, {"epoch": 0.416082626337145, "grad_norm": 0.4526151716709137, "learning_rate": 0.0001, "loss": 1.2106, "step": 705}, {"epoch": 0.41903356694946514, "grad_norm": 0.3414866328239441, "learning_rate": 0.0001, "loss": 1.163, "step": 710}, {"epoch": 0.4219845075617853, "grad_norm": 0.33304673433303833, "learning_rate": 0.0001, "loss": 1.1912, "step": 715}, {"epoch": 0.4249354481741055, "grad_norm": 0.42594268918037415, "learning_rate": 0.0001, "loss": 1.1921, "step": 720}, {"epoch": 0.42788638878642565, "grad_norm": 0.33425864577293396, "learning_rate": 0.0001, "loss": 1.2233, "step": 725}, {"epoch": 0.43083732939874586, "grad_norm": 0.3625440299510956, "learning_rate": 0.0001, "loss": 1.1964, "step": 730}, {"epoch": 0.433788270011066, "grad_norm": 0.3822436034679413, "learning_rate": 0.0001, "loss": 1.1828, "step": 735}, {"epoch": 0.4367392106233862, "grad_norm": 0.3039187788963318, "learning_rate": 0.0001, "loss": 1.1799, "step": 740}, {"epoch": 0.4396901512357064, "grad_norm": 0.37662774324417114, "learning_rate": 0.0001, "loss": 1.1424, "step": 745}, {"epoch": 0.4426410918480266, "grad_norm": 0.2947549819946289, "learning_rate": 0.0001, "loss": 1.1697, "step": 750}, {"epoch": 0.44559203246034673, "grad_norm": 0.38249680399894714, "learning_rate": 0.0001, "loss": 1.1676, "step": 755}, {"epoch": 0.44854297307266694, "grad_norm": 0.4027719795703888, "learning_rate": 0.0001, "loss": 1.1899, "step": 760}, {"epoch": 0.4514939136849871, "grad_norm": 0.3985890746116638, "learning_rate": 0.0001, "loss": 1.1422, "step": 765}, {"epoch": 0.45444485429730724, "grad_norm": 0.36851444840431213, "learning_rate": 0.0001, "loss": 1.2159, "step": 770}, {"epoch": 0.45739579490962745, "grad_norm": 0.3305245339870453, "learning_rate": 0.0001, "loss": 1.1967, "step": 775}, {"epoch": 0.4603467355219476, "grad_norm": 0.3373122215270996, "learning_rate": 0.0001, "loss": 1.1573, "step": 780}, {"epoch": 0.4632976761342678, "grad_norm": 0.40627261996269226, "learning_rate": 0.0001, "loss": 1.2034, "step": 785}, {"epoch": 0.46624861674658796, "grad_norm": 0.4473290741443634, "learning_rate": 0.0001, "loss": 1.2213, "step": 790}, {"epoch": 0.46919955735890817, "grad_norm": 0.39021629095077515, "learning_rate": 0.0001, "loss": 1.1793, "step": 795}, {"epoch": 0.4721504979712283, "grad_norm": 0.3755252957344055, "learning_rate": 0.0001, "loss": 1.2166, "step": 800}, {"epoch": 0.4721504979712283, "eval_loss": 1.366101861000061, "eval_runtime": 246.4874, "eval_samples_per_second": 27.506, "eval_steps_per_second": 0.86, "step": 800}, {"epoch": 0.47510143858354853, "grad_norm": 0.3602404296398163, "learning_rate": 0.0001, "loss": 1.1429, "step": 805}, {"epoch": 0.4780523791958687, "grad_norm": 0.35179710388183594, "learning_rate": 0.0001, "loss": 1.1833, "step": 810}, {"epoch": 0.48100331980818883, "grad_norm": 0.3609860837459564, "learning_rate": 0.0001, "loss": 1.1892, "step": 815}, {"epoch": 0.48395426042050904, "grad_norm": 0.40376830101013184, "learning_rate": 0.0001, "loss": 1.1814, "step": 820}, {"epoch": 0.4869052010328292, "grad_norm": 0.3921487033367157, "learning_rate": 0.0001, "loss": 1.1775, "step": 825}, {"epoch": 0.4898561416451494, "grad_norm": 0.3765113055706024, "learning_rate": 0.0001, "loss": 1.1903, "step": 830}, {"epoch": 0.49280708225746955, "grad_norm": 0.3921235203742981, "learning_rate": 0.0001, "loss": 1.1597, "step": 835}, {"epoch": 0.49575802286978976, "grad_norm": 0.34277284145355225, "learning_rate": 0.0001, "loss": 1.2156, "step": 840}, {"epoch": 0.4987089634821099, "grad_norm": 0.36174461245536804, "learning_rate": 0.0001, "loss": 1.2437, "step": 845}, {"epoch": 0.5016599040944301, "grad_norm": 0.4500541687011719, "learning_rate": 0.0001, "loss": 1.2092, "step": 850}, {"epoch": 0.5046108447067503, "grad_norm": 0.3848503530025482, "learning_rate": 0.0001, "loss": 1.2351, "step": 855}, {"epoch": 0.5075617853190705, "grad_norm": 0.38285553455352783, "learning_rate": 0.0001, "loss": 1.2352, "step": 860}, {"epoch": 0.5105127259313906, "grad_norm": 0.3328910768032074, "learning_rate": 0.0001, "loss": 1.1481, "step": 865}, {"epoch": 0.5134636665437108, "grad_norm": 0.33165690302848816, "learning_rate": 0.0001, "loss": 1.192, "step": 870}, {"epoch": 0.5164146071560309, "grad_norm": 0.37010708451271057, "learning_rate": 0.0001, "loss": 1.1854, "step": 875}, {"epoch": 0.5193655477683512, "grad_norm": 0.35477691888809204, "learning_rate": 0.0001, "loss": 1.2189, "step": 880}, {"epoch": 0.5223164883806714, "grad_norm": 0.3677918016910553, "learning_rate": 0.0001, "loss": 1.2665, "step": 885}, {"epoch": 0.5252674289929915, "grad_norm": 0.392135888338089, "learning_rate": 0.0001, "loss": 1.199, "step": 890}, {"epoch": 0.5282183696053117, "grad_norm": 0.33336618542671204, "learning_rate": 0.0001, "loss": 1.1592, "step": 895}, {"epoch": 0.5311693102176319, "grad_norm": 0.31889161467552185, "learning_rate": 0.0001, "loss": 1.2137, "step": 900}, {"epoch": 0.5311693102176319, "eval_loss": 1.3621083498001099, "eval_runtime": 247.4751, "eval_samples_per_second": 27.397, "eval_steps_per_second": 0.857, "step": 900}, {"epoch": 0.5341202508299521, "grad_norm": 0.27766871452331543, "learning_rate": 0.0001, "loss": 1.2304, "step": 905}, {"epoch": 0.5370711914422722, "grad_norm": 0.33129802346229553, "learning_rate": 0.0001, "loss": 1.1632, "step": 910}, {"epoch": 0.5400221320545924, "grad_norm": 0.37082311511039734, "learning_rate": 0.0001, "loss": 1.1903, "step": 915}, {"epoch": 0.5429730726669125, "grad_norm": 0.40135613083839417, "learning_rate": 0.0001, "loss": 1.2044, "step": 920}, {"epoch": 0.5459240132792328, "grad_norm": 0.36218884587287903, "learning_rate": 0.0001, "loss": 1.1882, "step": 925}, {"epoch": 0.5488749538915529, "grad_norm": 0.36676305532455444, "learning_rate": 0.0001, "loss": 1.1786, "step": 930}, {"epoch": 0.5518258945038731, "grad_norm": 0.4113537669181824, "learning_rate": 0.0001, "loss": 1.2475, "step": 935}, {"epoch": 0.5547768351161932, "grad_norm": 0.35803937911987305, "learning_rate": 0.0001, "loss": 1.1733, "step": 940}, {"epoch": 0.5577277757285135, "grad_norm": 0.4070746600627899, "learning_rate": 0.0001, "loss": 1.204, "step": 945}, {"epoch": 0.5606787163408337, "grad_norm": 0.4393693208694458, "learning_rate": 0.0001, "loss": 1.2017, "step": 950}, {"epoch": 0.5636296569531538, "grad_norm": 0.3669402301311493, "learning_rate": 0.0001, "loss": 1.2381, "step": 955}, {"epoch": 0.566580597565474, "grad_norm": 0.37681281566619873, "learning_rate": 0.0001, "loss": 1.2058, "step": 960}, {"epoch": 0.5695315381777941, "grad_norm": 0.3346240818500519, "learning_rate": 0.0001, "loss": 1.1947, "step": 965}, {"epoch": 0.5724824787901144, "grad_norm": 0.3743688762187958, "learning_rate": 0.0001, "loss": 1.2058, "step": 970}, {"epoch": 0.5754334194024345, "grad_norm": 0.3186356723308563, "learning_rate": 0.0001, "loss": 1.1743, "step": 975}, {"epoch": 0.5783843600147547, "grad_norm": 0.3614408075809479, "learning_rate": 0.0001, "loss": 1.1639, "step": 980}, {"epoch": 0.5813353006270748, "grad_norm": 0.27605751156806946, "learning_rate": 0.0001, "loss": 1.1383, "step": 985}, {"epoch": 0.5842862412393951, "grad_norm": 0.3820742964744568, "learning_rate": 0.0001, "loss": 1.1958, "step": 990}, {"epoch": 0.5872371818517153, "grad_norm": 0.3408990204334259, "learning_rate": 0.0001, "loss": 1.145, "step": 995}, {"epoch": 0.5901881224640354, "grad_norm": 0.33466634154319763, "learning_rate": 0.0001, "loss": 1.2051, "step": 1000}, {"epoch": 0.5901881224640354, "eval_loss": 1.3581589460372925, "eval_runtime": 247.6481, "eval_samples_per_second": 27.378, "eval_steps_per_second": 0.856, "step": 1000}, {"epoch": 0.5931390630763556, "grad_norm": 0.29110196232795715, "learning_rate": 0.0001, "loss": 1.1286, "step": 1005}, {"epoch": 0.5960900036886758, "grad_norm": 0.39917507767677307, "learning_rate": 0.0001, "loss": 1.1957, "step": 1010}, {"epoch": 0.599040944300996, "grad_norm": 0.46603864431381226, "learning_rate": 0.0001, "loss": 1.2235, "step": 1015}, {"epoch": 0.6019918849133161, "grad_norm": 0.37322765588760376, "learning_rate": 0.0001, "loss": 1.1961, "step": 1020}, {"epoch": 0.6049428255256363, "grad_norm": 0.36014920473098755, "learning_rate": 0.0001, "loss": 1.2031, "step": 1025}, {"epoch": 0.6078937661379564, "grad_norm": 0.36356082558631897, "learning_rate": 0.0001, "loss": 1.1871, "step": 1030}, {"epoch": 0.6108447067502767, "grad_norm": 0.35309362411499023, "learning_rate": 0.0001, "loss": 1.1842, "step": 1035}, {"epoch": 0.6137956473625968, "grad_norm": 0.37153396010398865, "learning_rate": 0.0001, "loss": 1.2012, "step": 1040}, {"epoch": 0.616746587974917, "grad_norm": 0.4099673926830292, "learning_rate": 0.0001, "loss": 1.2355, "step": 1045}, {"epoch": 0.6196975285872371, "grad_norm": 0.41647520661354065, "learning_rate": 0.0001, "loss": 1.2069, "step": 1050}, {"epoch": 0.6226484691995574, "grad_norm": 0.28108876943588257, "learning_rate": 0.0001, "loss": 1.1836, "step": 1055}, {"epoch": 0.6255994098118776, "grad_norm": 0.3371139466762543, "learning_rate": 0.0001, "loss": 1.1613, "step": 1060}, {"epoch": 0.6285503504241977, "grad_norm": 0.38452470302581787, "learning_rate": 0.0001, "loss": 1.1463, "step": 1065}, {"epoch": 0.6315012910365179, "grad_norm": 0.40959247946739197, "learning_rate": 0.0001, "loss": 1.1721, "step": 1070}, {"epoch": 0.634452231648838, "grad_norm": 0.31261909008026123, "learning_rate": 0.0001, "loss": 1.1507, "step": 1075}, {"epoch": 0.6374031722611583, "grad_norm": 0.3806215822696686, "learning_rate": 0.0001, "loss": 1.1355, "step": 1080}, {"epoch": 0.6403541128734784, "grad_norm": 0.3014231324195862, "learning_rate": 0.0001, "loss": 1.1687, "step": 1085}, {"epoch": 0.6433050534857986, "grad_norm": 0.35170215368270874, "learning_rate": 0.0001, "loss": 1.2029, "step": 1090}, {"epoch": 0.6462559940981187, "grad_norm": 0.3542157709598541, "learning_rate": 0.0001, "loss": 1.1798, "step": 1095}, {"epoch": 0.649206934710439, "grad_norm": 0.3632979989051819, "learning_rate": 0.0001, "loss": 1.1725, "step": 1100}, {"epoch": 0.649206934710439, "eval_loss": 1.3547154664993286, "eval_runtime": 248.1431, "eval_samples_per_second": 27.323, "eval_steps_per_second": 0.854, "step": 1100}, {"epoch": 0.6521578753227592, "grad_norm": 0.3374481499195099, "learning_rate": 0.0001, "loss": 1.1479, "step": 1105}, {"epoch": 0.6551088159350793, "grad_norm": 0.34452196955680847, "learning_rate": 0.0001, "loss": 1.1752, "step": 1110}, {"epoch": 0.6580597565473995, "grad_norm": 0.3467633128166199, "learning_rate": 0.0001, "loss": 1.1502, "step": 1115}, {"epoch": 0.6610106971597196, "grad_norm": 0.3299087584018707, "learning_rate": 0.0001, "loss": 1.1449, "step": 1120}, {"epoch": 0.6639616377720399, "grad_norm": 0.3783397376537323, "learning_rate": 0.0001, "loss": 1.1769, "step": 1125}, {"epoch": 0.66691257838436, "grad_norm": 0.3245580792427063, "learning_rate": 0.0001, "loss": 1.1904, "step": 1130}, {"epoch": 0.6698635189966802, "grad_norm": 0.3698376715183258, "learning_rate": 0.0001, "loss": 1.1979, "step": 1135}, {"epoch": 0.6728144596090003, "grad_norm": 0.3766252398490906, "learning_rate": 0.0001, "loss": 1.2051, "step": 1140}, {"epoch": 0.6757654002213206, "grad_norm": 0.34350407123565674, "learning_rate": 0.0001, "loss": 1.1357, "step": 1145}, {"epoch": 0.6787163408336407, "grad_norm": 0.38537052273750305, "learning_rate": 0.0001, "loss": 1.2092, "step": 1150}, {"epoch": 0.6816672814459609, "grad_norm": 0.3605525493621826, "learning_rate": 0.0001, "loss": 1.2057, "step": 1155}, {"epoch": 0.684618222058281, "grad_norm": 0.31560054421424866, "learning_rate": 0.0001, "loss": 1.1926, "step": 1160}, {"epoch": 0.6875691626706013, "grad_norm": 0.34913086891174316, "learning_rate": 0.0001, "loss": 1.2337, "step": 1165}, {"epoch": 0.6905201032829215, "grad_norm": 0.34916892647743225, "learning_rate": 0.0001, "loss": 1.2076, "step": 1170}, {"epoch": 0.6934710438952416, "grad_norm": 0.35321515798568726, "learning_rate": 0.0001, "loss": 1.1513, "step": 1175}, {"epoch": 0.6964219845075618, "grad_norm": 0.3224838376045227, "learning_rate": 0.0001, "loss": 1.1692, "step": 1180}, {"epoch": 0.6993729251198819, "grad_norm": 0.32030728459358215, "learning_rate": 0.0001, "loss": 1.2017, "step": 1185}, {"epoch": 0.7023238657322022, "grad_norm": 0.3288993537425995, "learning_rate": 0.0001, "loss": 1.1755, "step": 1190}, {"epoch": 0.7052748063445223, "grad_norm": 0.3415464162826538, "learning_rate": 0.0001, "loss": 1.1533, "step": 1195}, {"epoch": 0.7082257469568425, "grad_norm": 0.337858110666275, "learning_rate": 0.0001, "loss": 1.17, "step": 1200}, {"epoch": 0.7082257469568425, "eval_loss": 1.3537287712097168, "eval_runtime": 247.6889, "eval_samples_per_second": 27.373, "eval_steps_per_second": 0.856, "step": 1200}, {"epoch": 0.7111766875691626, "grad_norm": 0.37323233485221863, "learning_rate": 0.0001, "loss": 1.1984, "step": 1205}, {"epoch": 0.7141276281814829, "grad_norm": 0.37474894523620605, "learning_rate": 0.0001, "loss": 1.1828, "step": 1210}, {"epoch": 0.717078568793803, "grad_norm": 0.28909632563591003, "learning_rate": 0.0001, "loss": 1.1669, "step": 1215}, {"epoch": 0.7200295094061232, "grad_norm": 0.369081050157547, "learning_rate": 0.0001, "loss": 1.1545, "step": 1220}, {"epoch": 0.7229804500184434, "grad_norm": 0.3468446135520935, "learning_rate": 0.0001, "loss": 1.1607, "step": 1225}, {"epoch": 0.7259313906307635, "grad_norm": 0.36472147703170776, "learning_rate": 0.0001, "loss": 1.1644, "step": 1230}, {"epoch": 0.7288823312430838, "grad_norm": 0.37396541237831116, "learning_rate": 0.0001, "loss": 1.2037, "step": 1235}, {"epoch": 0.7318332718554039, "grad_norm": 0.29168689250946045, "learning_rate": 0.0001, "loss": 1.1631, "step": 1240}, {"epoch": 0.7347842124677241, "grad_norm": 0.27392587065696716, "learning_rate": 0.0001, "loss": 1.1341, "step": 1245}, {"epoch": 0.7377351530800442, "grad_norm": 0.3431527614593506, "learning_rate": 0.0001, "loss": 1.2066, "step": 1250}, {"epoch": 0.7406860936923645, "grad_norm": 0.30904772877693176, "learning_rate": 0.0001, "loss": 1.1022, "step": 1255}, {"epoch": 0.7436370343046846, "grad_norm": 0.3317582905292511, "learning_rate": 0.0001, "loss": 1.1328, "step": 1260}, {"epoch": 0.7465879749170048, "grad_norm": 0.3039371371269226, "learning_rate": 0.0001, "loss": 1.1804, "step": 1265}, {"epoch": 0.749538915529325, "grad_norm": 0.3079144358634949, "learning_rate": 0.0001, "loss": 1.1261, "step": 1270}, {"epoch": 0.7524898561416451, "grad_norm": 0.3599222004413605, "learning_rate": 0.0001, "loss": 1.1768, "step": 1275}, {"epoch": 0.7554407967539654, "grad_norm": 0.31710904836654663, "learning_rate": 0.0001, "loss": 1.1666, "step": 1280}, {"epoch": 0.7583917373662855, "grad_norm": 0.327048659324646, "learning_rate": 0.0001, "loss": 1.1456, "step": 1285}, {"epoch": 0.7613426779786057, "grad_norm": 0.3233148753643036, "learning_rate": 0.0001, "loss": 1.1516, "step": 1290}, {"epoch": 0.7642936185909258, "grad_norm": 0.31001752614974976, "learning_rate": 0.0001, "loss": 1.1785, "step": 1295}, {"epoch": 0.7672445592032461, "grad_norm": 0.3062288761138916, "learning_rate": 0.0001, "loss": 1.172, "step": 1300}, {"epoch": 0.7672445592032461, "eval_loss": 1.3487768173217773, "eval_runtime": 247.4551, "eval_samples_per_second": 27.399, "eval_steps_per_second": 0.857, "step": 1300}, {"epoch": 0.7701954998155662, "grad_norm": 0.3496793508529663, "learning_rate": 0.0001, "loss": 1.1683, "step": 1305}, {"epoch": 0.7731464404278864, "grad_norm": 0.3297085165977478, "learning_rate": 0.0001, "loss": 1.1949, "step": 1310}, {"epoch": 0.7760973810402065, "grad_norm": 0.35025104880332947, "learning_rate": 0.0001, "loss": 1.1865, "step": 1315}, {"epoch": 0.7790483216525267, "grad_norm": 0.36191660165786743, "learning_rate": 0.0001, "loss": 1.1404, "step": 1320}, {"epoch": 0.781999262264847, "grad_norm": 0.3237777650356293, "learning_rate": 0.0001, "loss": 1.1829, "step": 1325}, {"epoch": 0.7849502028771671, "grad_norm": 0.29511624574661255, "learning_rate": 0.0001, "loss": 1.1168, "step": 1330}, {"epoch": 0.7879011434894873, "grad_norm": 0.33152714371681213, "learning_rate": 0.0001, "loss": 1.1806, "step": 1335}, {"epoch": 0.7908520841018074, "grad_norm": 0.39324647188186646, "learning_rate": 0.0001, "loss": 1.202, "step": 1340}, {"epoch": 0.7938030247141277, "grad_norm": 0.2905988097190857, "learning_rate": 0.0001, "loss": 1.1871, "step": 1345}, {"epoch": 0.7967539653264478, "grad_norm": 0.39540359377861023, "learning_rate": 0.0001, "loss": 1.1572, "step": 1350}, {"epoch": 0.799704905938768, "grad_norm": 0.2932383418083191, "learning_rate": 0.0001, "loss": 1.2477, "step": 1355}, {"epoch": 0.8026558465510881, "grad_norm": 0.2975446581840515, "learning_rate": 0.0001, "loss": 1.168, "step": 1360}, {"epoch": 0.8056067871634084, "grad_norm": 0.33861738443374634, "learning_rate": 0.0001, "loss": 1.142, "step": 1365}, {"epoch": 0.8085577277757285, "grad_norm": 0.3614901900291443, "learning_rate": 0.0001, "loss": 1.1618, "step": 1370}, {"epoch": 0.8115086683880487, "grad_norm": 0.27153632044792175, "learning_rate": 0.0001, "loss": 1.1751, "step": 1375}, {"epoch": 0.8144596090003688, "grad_norm": 0.30300816893577576, "learning_rate": 0.0001, "loss": 1.1611, "step": 1380}, {"epoch": 0.817410549612689, "grad_norm": 0.31228122115135193, "learning_rate": 0.0001, "loss": 1.188, "step": 1385}, {"epoch": 0.8203614902250093, "grad_norm": 0.3612673878669739, "learning_rate": 0.0001, "loss": 1.1972, "step": 1390}, {"epoch": 0.8233124308373294, "grad_norm": 0.3396519720554352, "learning_rate": 0.0001, "loss": 1.1499, "step": 1395}, {"epoch": 0.8262633714496496, "grad_norm": 0.3056199848651886, "learning_rate": 0.0001, "loss": 1.176, "step": 1400}, {"epoch": 0.8262633714496496, "eval_loss": 1.3480890989303589, "eval_runtime": 246.7397, "eval_samples_per_second": 27.478, "eval_steps_per_second": 0.859, "step": 1400}, {"epoch": 0.8292143120619697, "grad_norm": 0.3566998839378357, "learning_rate": 0.0001, "loss": 1.1992, "step": 1405}, {"epoch": 0.83216525267429, "grad_norm": 0.39576825499534607, "learning_rate": 0.0001, "loss": 1.157, "step": 1410}, {"epoch": 0.8351161932866101, "grad_norm": 0.36689692735671997, "learning_rate": 0.0001, "loss": 1.1689, "step": 1415}, {"epoch": 0.8380671338989303, "grad_norm": 0.3026866316795349, "learning_rate": 0.0001, "loss": 1.1621, "step": 1420}, {"epoch": 0.8410180745112504, "grad_norm": 0.3225614130496979, "learning_rate": 0.0001, "loss": 1.2227, "step": 1425}, {"epoch": 0.8439690151235706, "grad_norm": 0.29500293731689453, "learning_rate": 0.0001, "loss": 1.1737, "step": 1430}, {"epoch": 0.8469199557358909, "grad_norm": 0.2833113968372345, "learning_rate": 0.0001, "loss": 1.1713, "step": 1435}, {"epoch": 0.849870896348211, "grad_norm": 0.32218798995018005, "learning_rate": 0.0001, "loss": 1.1553, "step": 1440}, {"epoch": 0.8528218369605312, "grad_norm": 0.32544612884521484, "learning_rate": 0.0001, "loss": 1.1748, "step": 1445}, {"epoch": 0.8557727775728513, "grad_norm": 0.3194770812988281, "learning_rate": 0.0001, "loss": 1.1312, "step": 1450}, {"epoch": 0.8587237181851716, "grad_norm": 0.29077011346817017, "learning_rate": 0.0001, "loss": 1.1717, "step": 1455}, {"epoch": 0.8616746587974917, "grad_norm": 0.4108298718929291, "learning_rate": 0.0001, "loss": 1.126, "step": 1460}, {"epoch": 0.8646255994098119, "grad_norm": 0.3263062536716461, "learning_rate": 0.0001, "loss": 1.1641, "step": 1465}, {"epoch": 0.867576540022132, "grad_norm": 0.321237713098526, "learning_rate": 0.0001, "loss": 1.1273, "step": 1470}, {"epoch": 0.8705274806344522, "grad_norm": 0.3150706887245178, "learning_rate": 0.0001, "loss": 1.1269, "step": 1475}, {"epoch": 0.8734784212467724, "grad_norm": 0.31895971298217773, "learning_rate": 0.0001, "loss": 1.1442, "step": 1480}, {"epoch": 0.8764293618590926, "grad_norm": 0.3172248303890228, "learning_rate": 0.0001, "loss": 1.1877, "step": 1485}, {"epoch": 0.8793803024714127, "grad_norm": 0.27037733793258667, "learning_rate": 0.0001, "loss": 1.1694, "step": 1490}, {"epoch": 0.8823312430837329, "grad_norm": 0.3313964307308197, "learning_rate": 0.0001, "loss": 1.1688, "step": 1495}, {"epoch": 0.8852821836960532, "grad_norm": 0.30192407965660095, "learning_rate": 0.0001, "loss": 1.169, "step": 1500}, {"epoch": 0.8852821836960532, "eval_loss": 1.346632719039917, "eval_runtime": 247.3078, "eval_samples_per_second": 27.415, "eval_steps_per_second": 0.857, "step": 1500}, {"epoch": 0.8882331243083733, "grad_norm": 0.3626715838909149, "learning_rate": 0.0001, "loss": 1.1616, "step": 1505}, {"epoch": 0.8911840649206935, "grad_norm": 0.3132845461368561, "learning_rate": 0.0001, "loss": 1.2257, "step": 1510}, {"epoch": 0.8941350055330136, "grad_norm": 0.3729867935180664, "learning_rate": 0.0001, "loss": 1.195, "step": 1515}, {"epoch": 0.8970859461453339, "grad_norm": 0.8032135367393494, "learning_rate": 0.0001, "loss": 1.1903, "step": 1520}, {"epoch": 0.900036886757654, "grad_norm": 0.28687193989753723, "learning_rate": 0.0001, "loss": 1.1823, "step": 1525}, {"epoch": 0.9029878273699742, "grad_norm": 0.30333882570266724, "learning_rate": 0.0001, "loss": 1.1859, "step": 1530}, {"epoch": 0.9059387679822943, "grad_norm": 0.29158881306648254, "learning_rate": 0.0001, "loss": 1.1147, "step": 1535}, {"epoch": 0.9088897085946145, "grad_norm": 0.31466299295425415, "learning_rate": 0.0001, "loss": 1.141, "step": 1540}, {"epoch": 0.9118406492069348, "grad_norm": 0.3682771623134613, "learning_rate": 0.0001, "loss": 1.1875, "step": 1545}, {"epoch": 0.9147915898192549, "grad_norm": 0.34508758783340454, "learning_rate": 0.0001, "loss": 1.1938, "step": 1550}, {"epoch": 0.917742530431575, "grad_norm": 0.31624600291252136, "learning_rate": 0.0001, "loss": 1.1748, "step": 1555}, {"epoch": 0.9206934710438952, "grad_norm": 0.35846298933029175, "learning_rate": 0.0001, "loss": 1.155, "step": 1560}, {"epoch": 0.9236444116562155, "grad_norm": 0.2583761215209961, "learning_rate": 0.0001, "loss": 1.093, "step": 1565}, {"epoch": 0.9265953522685356, "grad_norm": 0.32738620042800903, "learning_rate": 0.0001, "loss": 1.1635, "step": 1570}, {"epoch": 0.9295462928808558, "grad_norm": 0.3147200644016266, "learning_rate": 0.0001, "loss": 1.1665, "step": 1575}, {"epoch": 0.9324972334931759, "grad_norm": 0.30471929907798767, "learning_rate": 0.0001, "loss": 1.14, "step": 1580}, {"epoch": 0.9354481741054961, "grad_norm": 0.3011219799518585, "learning_rate": 0.0001, "loss": 1.1625, "step": 1585}, {"epoch": 0.9383991147178163, "grad_norm": 0.288665235042572, "learning_rate": 0.0001, "loss": 1.1681, "step": 1590}, {"epoch": 0.9413500553301365, "grad_norm": 0.27973607182502747, "learning_rate": 0.0001, "loss": 1.1574, "step": 1595}, {"epoch": 0.9443009959424566, "grad_norm": 0.3426041901111603, "learning_rate": 0.0001, "loss": 1.1965, "step": 1600}, {"epoch": 0.9443009959424566, "eval_loss": 1.3439240455627441, "eval_runtime": 246.3907, "eval_samples_per_second": 27.517, "eval_steps_per_second": 0.86, "step": 1600}, {"epoch": 0.9472519365547768, "grad_norm": 0.2948189973831177, "learning_rate": 0.0001, "loss": 1.1343, "step": 1605}, {"epoch": 0.9502028771670971, "grad_norm": 0.3621533513069153, "learning_rate": 0.0001, "loss": 1.1804, "step": 1610}, {"epoch": 0.9531538177794172, "grad_norm": 0.3160797953605652, "learning_rate": 0.0001, "loss": 1.1919, "step": 1615}, {"epoch": 0.9561047583917374, "grad_norm": 0.311749130487442, "learning_rate": 0.0001, "loss": 1.1445, "step": 1620}, {"epoch": 0.9590556990040575, "grad_norm": 0.31484559178352356, "learning_rate": 0.0001, "loss": 1.214, "step": 1625}, {"epoch": 0.9620066396163777, "grad_norm": 0.31874895095825195, "learning_rate": 0.0001, "loss": 1.2503, "step": 1630}, {"epoch": 0.9649575802286979, "grad_norm": 0.28996333479881287, "learning_rate": 0.0001, "loss": 1.1834, "step": 1635}, {"epoch": 0.9679085208410181, "grad_norm": 0.2913656234741211, "learning_rate": 0.0001, "loss": 1.1733, "step": 1640}, {"epoch": 0.9708594614533382, "grad_norm": 0.2982765734195709, "learning_rate": 0.0001, "loss": 1.1969, "step": 1645}, {"epoch": 0.9738104020656584, "grad_norm": 0.34283024072647095, "learning_rate": 0.0001, "loss": 1.1796, "step": 1650}, {"epoch": 0.9767613426779787, "grad_norm": 0.3193168044090271, "learning_rate": 0.0001, "loss": 1.1607, "step": 1655}, {"epoch": 0.9797122832902988, "grad_norm": 0.37915748357772827, "learning_rate": 0.0001, "loss": 1.1304, "step": 1660}, {"epoch": 0.982663223902619, "grad_norm": 0.2842554450035095, "learning_rate": 0.0001, "loss": 1.2118, "step": 1665}, {"epoch": 0.9856141645149391, "grad_norm": 0.3082302212715149, "learning_rate": 0.0001, "loss": 1.1417, "step": 1670}, {"epoch": 0.9885651051272594, "grad_norm": 0.32633909583091736, "learning_rate": 0.0001, "loss": 1.1517, "step": 1675}, {"epoch": 0.9915160457395795, "grad_norm": 0.30882906913757324, "learning_rate": 0.0001, "loss": 1.1687, "step": 1680}, {"epoch": 0.9944669863518997, "grad_norm": 0.3236086368560791, "learning_rate": 0.0001, "loss": 1.218, "step": 1685}, {"epoch": 0.9974179269642198, "grad_norm": 0.3170182406902313, "learning_rate": 0.0001, "loss": 1.2148, "step": 1690}, {"epoch": 1.0, "grad_norm": 0.3088963031768799, "learning_rate": 0.0001, "loss": 1.1526, "step": 1695}, {"epoch": 1.0029509406123203, "grad_norm": 0.31881946325302124, "learning_rate": 0.0001, "loss": 1.106, "step": 1700}, {"epoch": 1.0029509406123203, "eval_loss": 1.3453712463378906, "eval_runtime": 248.2225, "eval_samples_per_second": 27.314, "eval_steps_per_second": 0.854, "step": 1700}, {"epoch": 1.0059018812246403, "grad_norm": 0.3407232463359833, "learning_rate": 0.0001, "loss": 1.1598, "step": 1705}, {"epoch": 1.0088528218369606, "grad_norm": 0.29656076431274414, "learning_rate": 0.0001, "loss": 1.08, "step": 1710}, {"epoch": 1.0118037624492806, "grad_norm": 0.32564789056777954, "learning_rate": 0.0001, "loss": 1.1584, "step": 1715}, {"epoch": 1.0147547030616009, "grad_norm": 0.30535244941711426, "learning_rate": 0.0001, "loss": 1.0993, "step": 1720}, {"epoch": 1.0177056436739211, "grad_norm": 0.3131856620311737, "learning_rate": 0.0001, "loss": 1.108, "step": 1725}, {"epoch": 1.0206565842862412, "grad_norm": 0.32325801253318787, "learning_rate": 0.0001, "loss": 1.1408, "step": 1730}, {"epoch": 1.0236075248985614, "grad_norm": 0.38909411430358887, "learning_rate": 0.0001, "loss": 1.1726, "step": 1735}, {"epoch": 1.0265584655108817, "grad_norm": 0.29319003224372864, "learning_rate": 0.0001, "loss": 1.1335, "step": 1740}, {"epoch": 1.0295094061232017, "grad_norm": 0.32115623354911804, "learning_rate": 0.0001, "loss": 1.1415, "step": 1745}, {"epoch": 1.032460346735522, "grad_norm": 0.3641589283943176, "learning_rate": 0.0001, "loss": 1.0943, "step": 1750}, {"epoch": 1.035411287347842, "grad_norm": 0.3407881259918213, "learning_rate": 0.0001, "loss": 1.1829, "step": 1755}, {"epoch": 1.0383622279601623, "grad_norm": 0.3439313471317291, "learning_rate": 0.0001, "loss": 1.0974, "step": 1760}, {"epoch": 1.0413131685724826, "grad_norm": 0.30603739619255066, "learning_rate": 0.0001, "loss": 1.0923, "step": 1765}, {"epoch": 1.0442641091848026, "grad_norm": 0.33850613236427307, "learning_rate": 0.0001, "loss": 1.1161, "step": 1770}, {"epoch": 1.0472150497971229, "grad_norm": 0.30651092529296875, "learning_rate": 0.0001, "loss": 1.0812, "step": 1775}, {"epoch": 1.050165990409443, "grad_norm": 0.3434087932109833, "learning_rate": 0.0001, "loss": 1.133, "step": 1780}, {"epoch": 1.0531169310217632, "grad_norm": 0.32536566257476807, "learning_rate": 0.0001, "loss": 1.1234, "step": 1785}, {"epoch": 1.0560678716340834, "grad_norm": 0.28929603099823, "learning_rate": 0.0001, "loss": 1.1498, "step": 1790}, {"epoch": 1.0590188122464035, "grad_norm": 0.29546859860420227, "learning_rate": 0.0001, "loss": 1.1062, "step": 1795}, {"epoch": 1.0619697528587237, "grad_norm": 0.4182150363922119, "learning_rate": 0.0001, "loss": 1.1259, "step": 1800}, {"epoch": 1.0619697528587237, "eval_loss": 1.3453850746154785, "eval_runtime": 249.8314, "eval_samples_per_second": 27.138, "eval_steps_per_second": 0.849, "step": 1800}, {"epoch": 1.064920693471044, "grad_norm": 0.34480658173561096, "learning_rate": 0.0001, "loss": 1.1514, "step": 1805}, {"epoch": 1.067871634083364, "grad_norm": 0.3674759566783905, "learning_rate": 0.0001, "loss": 1.0755, "step": 1810}, {"epoch": 1.0708225746956843, "grad_norm": 0.3465211093425751, "learning_rate": 0.0001, "loss": 1.1708, "step": 1815}, {"epoch": 1.0737735153080044, "grad_norm": 0.3046048879623413, "learning_rate": 0.0001, "loss": 1.1481, "step": 1820}, {"epoch": 1.0767244559203246, "grad_norm": 0.353755384683609, "learning_rate": 0.0001, "loss": 1.1484, "step": 1825}, {"epoch": 1.0796753965326449, "grad_norm": 0.3322429656982422, "learning_rate": 0.0001, "loss": 1.1257, "step": 1830}, {"epoch": 1.082626337144965, "grad_norm": 0.3183218538761139, "learning_rate": 0.0001, "loss": 1.1444, "step": 1835}, {"epoch": 1.0855772777572852, "grad_norm": 0.32456183433532715, "learning_rate": 0.0001, "loss": 1.1556, "step": 1840}, {"epoch": 1.0885282183696052, "grad_norm": 0.4592103064060211, "learning_rate": 0.0001, "loss": 1.1397, "step": 1845}, {"epoch": 1.0914791589819255, "grad_norm": 0.35487526655197144, "learning_rate": 0.0001, "loss": 1.1788, "step": 1850}, {"epoch": 1.0944300995942458, "grad_norm": 0.29484525322914124, "learning_rate": 0.0001, "loss": 1.1352, "step": 1855}, {"epoch": 1.0973810402065658, "grad_norm": 0.3551277220249176, "learning_rate": 0.0001, "loss": 1.1315, "step": 1860}, {"epoch": 1.100331980818886, "grad_norm": 0.35826051235198975, "learning_rate": 0.0001, "loss": 1.1499, "step": 1865}, {"epoch": 1.103282921431206, "grad_norm": 0.35470250248908997, "learning_rate": 0.0001, "loss": 1.1729, "step": 1870}, {"epoch": 1.1062338620435264, "grad_norm": 0.3178890347480774, "learning_rate": 0.0001, "loss": 1.0857, "step": 1875}, {"epoch": 1.1091848026558466, "grad_norm": 0.3633881211280823, "learning_rate": 0.0001, "loss": 1.1562, "step": 1880}, {"epoch": 1.1121357432681667, "grad_norm": 0.35908758640289307, "learning_rate": 0.0001, "loss": 1.1122, "step": 1885}, {"epoch": 1.115086683880487, "grad_norm": 0.3567257225513458, "learning_rate": 0.0001, "loss": 1.1047, "step": 1890}, {"epoch": 1.118037624492807, "grad_norm": 0.3101084530353546, "learning_rate": 0.0001, "loss": 1.0899, "step": 1895}, {"epoch": 1.1209885651051272, "grad_norm": 0.33969247341156006, "learning_rate": 0.0001, "loss": 1.1249, "step": 1900}, {"epoch": 1.1209885651051272, "eval_loss": 1.3439974784851074, "eval_runtime": 259.969, "eval_samples_per_second": 26.08, "eval_steps_per_second": 0.815, "step": 1900}, {"epoch": 1.1239395057174475, "grad_norm": 0.3110252022743225, "learning_rate": 0.0001, "loss": 1.1224, "step": 1905}, {"epoch": 1.1268904463297675, "grad_norm": 0.3465375602245331, "learning_rate": 0.0001, "loss": 1.1481, "step": 1910}, {"epoch": 1.1298413869420878, "grad_norm": 0.3470171093940735, "learning_rate": 0.0001, "loss": 1.1526, "step": 1915}, {"epoch": 1.132792327554408, "grad_norm": 0.37952789664268494, "learning_rate": 0.0001, "loss": 1.1588, "step": 1920}, {"epoch": 1.135743268166728, "grad_norm": 0.3267860412597656, "learning_rate": 0.0001, "loss": 1.1091, "step": 1925}, {"epoch": 1.1386942087790484, "grad_norm": 0.3069424629211426, "learning_rate": 0.0001, "loss": 1.0912, "step": 1930}, {"epoch": 1.1416451493913684, "grad_norm": 0.32485413551330566, "learning_rate": 0.0001, "loss": 1.1036, "step": 1935}, {"epoch": 1.1445960900036887, "grad_norm": 0.38342705368995667, "learning_rate": 0.0001, "loss": 1.1308, "step": 1940}, {"epoch": 1.147547030616009, "grad_norm": 0.3375547230243683, "learning_rate": 0.0001, "loss": 1.1094, "step": 1945}, {"epoch": 1.150497971228329, "grad_norm": 0.33274975419044495, "learning_rate": 0.0001, "loss": 1.0824, "step": 1950}, {"epoch": 1.1534489118406492, "grad_norm": 0.40573936700820923, "learning_rate": 0.0001, "loss": 1.11, "step": 1955}, {"epoch": 1.1563998524529695, "grad_norm": 0.34038853645324707, "learning_rate": 0.0001, "loss": 1.1245, "step": 1960}, {"epoch": 1.1593507930652895, "grad_norm": 0.3398899435997009, "learning_rate": 0.0001, "loss": 1.1305, "step": 1965}, {"epoch": 1.1623017336776098, "grad_norm": 0.36838746070861816, "learning_rate": 0.0001, "loss": 1.1629, "step": 1970}, {"epoch": 1.1652526742899298, "grad_norm": 0.3390536606311798, "learning_rate": 0.0001, "loss": 1.1354, "step": 1975}, {"epoch": 1.16820361490225, "grad_norm": 0.3705992102622986, "learning_rate": 0.0001, "loss": 1.1323, "step": 1980}, {"epoch": 1.1711545555145704, "grad_norm": 0.3476364314556122, "learning_rate": 0.0001, "loss": 1.1383, "step": 1985}, {"epoch": 1.1741054961268904, "grad_norm": 0.3334161639213562, "learning_rate": 0.0001, "loss": 1.139, "step": 1990}, {"epoch": 1.1770564367392107, "grad_norm": 0.3645515441894531, "learning_rate": 0.0001, "loss": 1.0859, "step": 1995}, {"epoch": 1.1800073773515307, "grad_norm": 0.32794827222824097, "learning_rate": 0.0001, "loss": 1.0977, "step": 2000}, {"epoch": 1.1800073773515307, "eval_loss": 1.3429476022720337, "eval_runtime": 274.4036, "eval_samples_per_second": 24.708, "eval_steps_per_second": 0.773, "step": 2000}], "logging_steps": 5, "max_steps": 3390, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 228788312219648.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}