# Multi-stage Docker build for ML training environment with CUDA 12.6
FROM nvidia/cuda:12.6-devel-ubuntu22.04 as base

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3-pip \
    python3.10-venv \
    git \
    wget \
    curl \
    build-essential \
    cmake \
    ninja-build \
    libopenmpi-dev \
    libaio-dev \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic links for python
RUN ln -s /usr/bin/python3.10 /usr/bin/python && \
    ln -s /usr/bin/pip3 /usr/bin/pip

# Upgrade pip
RUN python -m pip install --upgrade pip setuptools wheel

# Install PyTorch 2.7.0 with CUDA 12.6 support
RUN pip install torch==2.7.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu126

# Install specific package versions
RUN pip install \
    transformers==4.52.3 \
    deepspeed==0.16.9 \
    numpy==1.26.3

# Install additional ML dependencies
RUN pip install \
    accelerate \
    datasets \
    tokenizers \
    peft \
    bitsandbytes \
    wandb \
    scipy \
    scikit-learn \
    matplotlib \
    seaborn \
    jupyter \
    ipykernel \
    tqdm \
    psutil

# Install development tools
RUN pip install \
    black \
    flake8 \
    pytest \
    ipdb \
    pre-commit

# Create working directory
WORKDIR /workspace

# Create non-root user for security
RUN useradd -m -s /bin/bash mluser && \
    chown -R mluser:mluser /workspace

# Switch to non-root user
USER mluser

# Set up environment for the user
ENV PATH=/home/<USER>/.local/bin:${PATH}

# Verify installations
RUN python -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}')"
RUN python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
RUN python -c "import deepspeed; print(f'DeepSpeed version: {deepspeed.__version__}')"
RUN python -c "import numpy; print(f'NumPy version: {numpy.__version__}')"

# Expose Jupyter port
EXPOSE 8888

# Default command
CMD ["/bin/bash"]
