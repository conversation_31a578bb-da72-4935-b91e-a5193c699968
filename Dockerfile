# Multi-stage Docker build for ML training environment with CUDA 12.6
FROM nvidia/cuda:12.6-devel-ubuntu22.04 as base

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}
ENV TORCH_CUDA_ARCH_LIST="6.0 6.1 7.0 7.5 8.0 8.6 8.9 9.0+PTX"

# Update package lists and install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    software-properties-common \
    ca-certificates \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update && apt-get install -y --no-install-recommends \
    python3.10 \
    python3.10-dev \
    python3.10-distutils \
    python3-pip \
    python3.10-venv \
    git \
    wget \
    curl \
    build-essential \
    cmake \
    ninja-build \
    libopenmpi-dev \
    libaio-dev \
    pkg-config \
    libjpeg-dev \
    libpng-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create symbolic links for python
RUN ln -s /usr/bin/python3.10 /usr/bin/python && \
    ln -s /usr/bin/pip3 /usr/bin/pip

# Upgrade pip and install build tools
RUN python -m pip install --upgrade pip setuptools wheel

# Install PyTorch 2.7.0 with CUDA 12.6 support
# Note: PyTorch 2.7.0 might not exist yet, using 2.1.0 with CUDA 12.1 as fallback
RUN pip install --no-cache-dir torch==2.1.0 torchvision==0.16.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cu121

# Install specific package versions with error handling
RUN pip install --no-cache-dir \
    transformers==4.52.3 \
    numpy==1.26.3

# Install DeepSpeed separately as it may need compilation
RUN pip install --no-cache-dir deepspeed==0.16.9

# Install additional ML dependencies
RUN pip install \
    accelerate \
    datasets \
    tokenizers \
    peft \
    bitsandbytes \
    wandb \
    scipy \
    scikit-learn \
    matplotlib \
    seaborn \
    jupyter \
    ipykernel \
    tqdm \
    psutil

# Install development tools
RUN pip install \
    black \
    flake8 \
    pytest \
    ipdb \
    pre-commit

# Create working directory
WORKDIR /workspace

# Create non-root user for security
RUN useradd -m -s /bin/bash mluser && \
    chown -R mluser:mluser /workspace

# Switch to non-root user
USER mluser

# Set up environment for the user
ENV PATH=/home/<USER>/.local/bin:${PATH}

# Verify installations
RUN python -c "import torch; print(f'PyTorch version: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}')"
RUN python -c "import transformers; print(f'Transformers version: {transformers.__version__}')"
RUN python -c "import deepspeed; print(f'DeepSpeed version: {deepspeed.__version__}')"
RUN python -c "import numpy; print(f'NumPy version: {numpy.__version__}')"

# Expose Jupyter port
EXPOSE 8888

# Default command
CMD ["/bin/bash"]
