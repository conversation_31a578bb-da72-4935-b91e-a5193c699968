#!/usr/bin/env python3
"""
Debug script to verify translation token usage in the CLI
"""

import sys
sys.path.append('.')
from chat_cli import Chat<PERSON>ot

def debug_translation_token():
    """Debug the translation token usage"""
    
    print("🔍 Debugging Translation Token Usage")
    print("=" * 50)
    
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    
    try:
        # Create chatbot instance
        print("🤖 Creating ChatBot instance...")
        chatbot = ChatBot(
            model_path=model_path,
            system_prompt="You are a helpful assistant",
            use_4bit=False,
            max_length=2048
        )
        
        # Test message
        test_message = "Translate 'Hello world' to Italian"
        
        print(f"\n📝 Test message: '{test_message}'")
        
        # Add user message to conversation
        chatbot.conversation_history.append({"role": "user", "content": test_message})
        
        # Format conversation and check for translation token
        formatted_prompt = chatbot._format_conversation()
        
        print("\n🔍 Generated prompt:")
        print("-" * 40)
        print(formatted_prompt)
        print("-" * 40)
        
        # Check if translation token is present
        if "<translation>" in formatted_prompt:
            print("✅ SUCCESS: <translation> token found in prompt!")
            
            # Find the position
            pos = formatted_prompt.find("<translation>")
            context_start = max(0, pos - 50)
            context_end = min(len(formatted_prompt), pos + 50)
            context = formatted_prompt[context_start:context_end]
            
            print(f"📍 Token position: {pos}")
            print(f"🔍 Context: ...{context}...")
            
        else:
            print("❌ ISSUE: <translation> token NOT found in prompt!")
            
        # Check tokenizer directly
        print("\n🔧 Checking tokenizer directly...")
        if hasattr(chatbot.tokenizer, 'apply_chat_template'):
            print("✅ Tokenizer has apply_chat_template method")
            
            # Test with simple conversation
            test_conversation = [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello"}
            ]
            
            template_result = chatbot.tokenizer.apply_chat_template(
                test_conversation,
                tokenize=False,
                add_generation_prompt=True
            )
            
            print("\n📋 Direct template result:")
            print("-" * 30)
            print(template_result)
            print("-" * 30)
            
            if "<translation>" in template_result:
                print("✅ Translation token found in direct template!")
            else:
                print("❌ Translation token NOT found in direct template!")
                
        else:
            print("❌ Tokenizer does not have apply_chat_template method")
            
        # Check translation tokens in vocabulary
        print("\n🔤 Checking translation tokens in vocabulary...")
        start_token = "<translation>"
        end_token = "</translation>"
        
        if start_token in chatbot.tokenizer.get_vocab():
            start_id = chatbot.tokenizer.convert_tokens_to_ids(start_token)
            print(f"✅ {start_token} found - ID: {start_id}")
        else:
            print(f"❌ {start_token} NOT found in vocabulary")
            
        if end_token in chatbot.tokenizer.get_vocab():
            end_id = chatbot.tokenizer.convert_tokens_to_ids(end_token)
            print(f"✅ {end_token} found - ID: {end_id}")
        else:
            print(f"❌ {end_token} NOT found in vocabulary")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_translation_token()
