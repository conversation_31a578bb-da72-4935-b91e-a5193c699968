#!/usr/bin/env python3
"""
Test script to verify translation token generation with the trained model
"""

import os
import sys
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel
import warnings

# Add current directory to path to import chat_cli
sys.path.append('.')
from chat_cli import ChatBot

warnings.filterwarnings("ignore")


def test_translation_output():
    """Test the translation token generation with specific Italian inputs"""

    print("🎯 Testing Translation Token Generation")
    print("=" * 60)

    # Model path - using the checkpoint with correct chat template
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"

    # Check if model exists
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("Available checkpoints:")
        for path in ["./it_lora8_epoch3", "./llama-reasoning-finetuned-9k-deepspeed"]:
            if os.path.exists(path):
                checkpoints = [
                    d for d in os.listdir(path) if d.startswith("checkpoint-")
                ]
                if checkpoints:
                    print(f"  {path}: {sorted(checkpoints)}")
        return False

    print(f"📂 Loading model from: {model_path}")

    try:
        # Load tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print(f"✅ Tokenizer loaded - Vocab size: {len(tokenizer)}")

        # Verify translation tokens exist
        translation_tokens = [
            "<in_translation>",
            "</in_translation>",
            "<out_translation>",
            "</out_translation>",
        ]
        missing_tokens = []

        for token in translation_tokens:
            if token not in tokenizer.get_vocab():
                missing_tokens.append(token)
            else:
                token_id = tokenizer.convert_tokens_to_ids(token)
                print(f"✅ {token} found - ID: {token_id}")

        if missing_tokens:
            print(f"❌ Missing tokens: {missing_tokens}")
            return False

        # Load base model first with CPU to avoid memory issues
        print("🔄 Loading base model on CPU...")
        base_model_name = "meta-llama/Meta-Llama-3.1-8B"
        model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.bfloat16,
            device_map="cpu",  # Load on CPU first
            trust_remote_code=True,
        )

        # Resize token embeddings to match the trained model
        print(
            f"🔧 Resizing embeddings from {model.config.vocab_size} to {len(tokenizer)}"
        )
        model.resize_token_embeddings(len(tokenizer))

        # Now load the LoRA adapter
        print("🔄 Loading LoRA adapter...")
        from peft import PeftModel

        model = PeftModel.from_pretrained(model, model_path)

        # Move to GPU after loading
        print("🔄 Moving model to GPU...")
        model = model.to("cuda")
        print("✅ Model loaded successfully")

        # Test inputs
        test_inputs = [
            "Che cos'è la teoria della relatività?",
            "Quale condizione del sesto teorema di impossibilità di Arrhenius violano le visioni a livello critico?\n\nOpzioni di risposta:\nA. Dominanza Egalitaria\nB. Priorità Generale Non Estrema\nC. Non Elitismo\nD. Debole Non Sadismo\nE. Debole Addizione di Qualità",
        ]

        print(f"\n🧪 Testing with {len(test_inputs)} Italian inputs...")
        print("=" * 60)

        for i, italian_input in enumerate(test_inputs, 1):
            print(f"\n{i}. Testing Input:")
            print(
                f"   📝 Italian: {italian_input[:100]}{'...' if len(italian_input) > 100 else ''}"
            )

            # Create conversation
            conversation = [{"role": "user", "content": italian_input}]

            # Apply chat template
            try:
                formatted_prompt = tokenizer.apply_chat_template(
                    conversation, tokenize=False, add_generation_prompt=True
                )

                print(f"   📋 Prompt ends with: ...{formatted_prompt[-50:]}")

                # Check if prompt ends with <in_translation>
                if formatted_prompt.endswith("<in_translation>"):
                    print("   ✅ Prompt correctly ends with <in_translation>")
                else:
                    print("   ❌ Prompt does not end with <in_translation>")
                    print(f"   📋 Actual ending: {formatted_prompt[-100:]}")

                # Tokenize and generate
                inputs = tokenizer(formatted_prompt, return_tensors="pt")

                # Move to same device as model
                device = next(model.parameters()).device
                inputs = {k: v.to(device) for k, v in inputs.items()}

                print("   🔄 Generating response...")

                # Generate with appropriate parameters
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=1024,  # Increased for longer responses
                        do_sample=True,
                        temperature=0.3,  # Lower temperature for more consistent format
                        top_p=0.9,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=[
                            tokenizer.eos_token_id,
                            tokenizer.convert_tokens_to_ids("</out_translation>"),
                        ],
                    )

                # Decode response
                full_response = tokenizer.decode(outputs[0], skip_special_tokens=False)

                # Extract just the generated part
                generated_part = full_response[len(formatted_prompt) :]

                print(f"   🤖 Generated Response:")
                print(f"   {'-' * 50}")
                print(f"   {generated_part}")
                print(f"   {'-' * 50}")

                # Check for translation tokens in response
                has_in_translation = "<in_translation>" in generated_part
                has_out_translation = "<out_translation>" in generated_part
                has_in_translation_end = "</in_translation>" in generated_part
                has_out_translation_end = "</out_translation>" in generated_part

                print(f"   📊 Token Analysis:")
                print(f"      <in_translation>: {'✅' if has_in_translation else '❌'}")
                print(
                    f"      </in_translation>: {'✅' if has_in_translation_end else '❌'}"
                )
                print(
                    f"      <out_translation>: {'✅' if has_out_translation else '❌'}"
                )
                print(
                    f"      </out_translation>: {'✅' if has_out_translation_end else '❌'}"
                )

                # Check expected format
                if (
                    has_in_translation
                    and has_in_translation_end
                    and has_out_translation
                    and has_out_translation_end
                ):
                    print("   🎉 SUCCESS: All translation tokens found!")

                    # Try to extract the parts
                    try:
                        # Find the translation sections
                        in_start = generated_part.find("<in_translation>") + len(
                            "<in_translation>"
                        )
                        in_end = generated_part.find("</in_translation>")
                        out_start = generated_part.find("<out_translation>") + len(
                            "<out_translation>"
                        )
                        out_end = generated_part.find("</out_translation>")

                        if (
                            in_start > 0
                            and in_end > in_start
                            and out_start > 0
                            and out_end > out_start
                        ):
                            english_translation = generated_part[
                                in_start:in_end
                            ].strip()
                            italian_response = generated_part[out_start:out_end].strip()
                            middle_content = generated_part[
                                in_end
                                + len("</in_translation>") : generated_part.find(
                                    "<out_translation>"
                                )
                            ].strip()

                            print(f"   📋 Extracted Parts:")
                            print(
                                f"      🇬🇧 English Translation: {english_translation}"
                            )
                            print(
                                f"      💭 Middle Content: {middle_content[:100]}{'...' if len(middle_content) > 100 else ''}"
                            )
                            print(f"      🇮🇹 Italian Response: {italian_response}")

                    except Exception as e:
                        print(f"   ⚠️  Could not extract parts: {e}")

                else:
                    print("   ❌ ISSUE: Missing some translation tokens")

            except Exception as e:
                print(f"   ❌ Error during generation: {e}")
                import traceback

                traceback.print_exc()

        return True

    except Exception as e:
        print(f"❌ Error loading model: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """Main function"""
    print("🤖 Translation Token Generation Test")
    print("=" * 60)

    success = test_translation_output()

    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)

    if success:
        print("🎉 Test completed successfully!")
        print("✅ Check the output above to verify translation token generation")
    else:
        print("❌ Test failed - check error messages above")

    print("\n💡 Expected format:")
    print("   <in_translation>English translation of Italian input</in_translation>")
    print("   English reasoning/response content")
    print("   <out_translation>Italian translation of response</out_translation>")


if __name__ == "__main__":
    main()
