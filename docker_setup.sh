#!/bin/bash

# Docker setup script for ML training environment with CUDA 12.6
# Usage: ./docker_setup.sh [build|run|exec|stop|logs|clean]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="ml-training:cuda12.6"
CONTAINER_NAME="ml-training-env"

# Functions
print_usage() {
    echo -e "${BLUE}Usage: $0 [command]${NC}"
    echo ""
    echo "Commands:"
    echo "  build   - Build the Docker image"
    echo "  run     - Run the container with GPU support"
    echo "  exec    - Execute bash in running container"
    echo "  stop    - Stop the running container"
    echo "  logs    - Show container logs"
    echo "  clean   - Remove container and image"
    echo "  status  - Show container status"
    echo ""
    echo "Examples:"
    echo "  $0 build && $0 run    # Build and run"
    echo "  $0 exec               # Enter running container"
}

check_nvidia_docker() {
    if ! command -v nvidia-docker &> /dev/null && ! docker info | grep -q nvidia; then
        echo -e "${RED}Error: NVIDIA Docker support not found!${NC}"
        echo "Please install nvidia-container-toolkit:"
        echo "  https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html"
        exit 1
    fi
}

check_gpu() {
    echo -e "${BLUE}Checking GPU availability...${NC}"
    if command -v nvidia-smi &> /dev/null; then
        nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits
    else
        echo -e "${YELLOW}Warning: nvidia-smi not found. GPU support may not work.${NC}"
    fi
}

build_image() {
    echo -e "${BLUE}Building Docker image: ${IMAGE_NAME}${NC}"
    
    # Check if Dockerfile exists
    if [ ! -f "Dockerfile" ]; then
        echo -e "${RED}Error: Dockerfile not found in current directory${NC}"
        exit 1
    fi
    
    # Build with progress and cache
    docker build \
        --tag ${IMAGE_NAME} \
        --progress=plain \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        .
    
    echo -e "${GREEN}✅ Image built successfully: ${IMAGE_NAME}${NC}"
}

run_container() {
    echo -e "${BLUE}Starting container: ${CONTAINER_NAME}${NC}"
    
    # Stop existing container if running
    if docker ps -q -f name=${CONTAINER_NAME} | grep -q .; then
        echo -e "${YELLOW}Stopping existing container...${NC}"
        docker stop ${CONTAINER_NAME}
    fi
    
    # Remove existing container if exists
    if docker ps -aq -f name=${CONTAINER_NAME} | grep -q .; then
        echo -e "${YELLOW}Removing existing container...${NC}"
        docker rm ${CONTAINER_NAME}
    fi
    
    # Check for required environment files
    ENV_FILE=""
    if [ -f ".env" ]; then
        ENV_FILE="--env-file .env"
        echo -e "${GREEN}Found .env file, loading environment variables${NC}"
    fi
    
    # Run container with GPU support
    docker run -d \
        --name ${CONTAINER_NAME} \
        --gpus '"device=0,1,2,3"' \
        --shm-size=16g \
        --memory=64g \
        --memory-swap=64g \
        -p 8888:8888 \
        -p 6006:6006 \
        -p 7860:7860 \
        -v "$(pwd)":/workspace/translation_it \
        -v ~/.cache/huggingface:/home/<USER>/.cache/huggingface \
        -v ~/.wandb:/home/<USER>/.wandb \
        -w /workspace/translation_it \
        -e NVIDIA_VISIBLE_DEVICES=0,1,2,3 \
        -e CUDA_VISIBLE_DEVICES=0,1,2,3 \
        ${ENV_FILE} \
        --restart unless-stopped \
        ${IMAGE_NAME} \
        sleep infinity
    
    echo -e "${GREEN}✅ Container started: ${CONTAINER_NAME}${NC}"
    echo -e "${BLUE}To enter the container, run: $0 exec${NC}"
}

exec_container() {
    if ! docker ps -q -f name=${CONTAINER_NAME} | grep -q .; then
        echo -e "${RED}Error: Container ${CONTAINER_NAME} is not running${NC}"
        echo -e "${BLUE}Start it with: $0 run${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}Entering container: ${CONTAINER_NAME}${NC}"
    docker exec -it ${CONTAINER_NAME} /bin/bash
}

stop_container() {
    echo -e "${BLUE}Stopping container: ${CONTAINER_NAME}${NC}"
    docker stop ${CONTAINER_NAME} || true
    echo -e "${GREEN}✅ Container stopped${NC}"
}

show_logs() {
    echo -e "${BLUE}Showing logs for: ${CONTAINER_NAME}${NC}"
    docker logs -f ${CONTAINER_NAME}
}

clean_all() {
    echo -e "${YELLOW}Cleaning up Docker resources...${NC}"
    
    # Stop and remove container
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    
    # Remove image
    docker rmi ${IMAGE_NAME} 2>/dev/null || true
    
    # Clean up dangling images
    docker image prune -f
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

show_status() {
    echo -e "${BLUE}Container Status:${NC}"
    if docker ps -q -f name=${CONTAINER_NAME} | grep -q .; then
        echo -e "${GREEN}✅ Container is running${NC}"
        docker ps -f name=${CONTAINER_NAME} --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    else
        echo -e "${YELLOW}⚠️  Container is not running${NC}"
    fi
    
    echo -e "\n${BLUE}Image Status:${NC}"
    if docker images -q ${IMAGE_NAME} | grep -q .; then
        echo -e "${GREEN}✅ Image exists${NC}"
        docker images ${IMAGE_NAME} --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
    else
        echo -e "${YELLOW}⚠️  Image not found${NC}"
    fi
}

# Main script logic
case "${1:-}" in
    build)
        check_nvidia_docker
        check_gpu
        build_image
        ;;
    run)
        check_nvidia_docker
        check_gpu
        run_container
        ;;
    exec)
        exec_container
        ;;
    stop)
        stop_container
        ;;
    logs)
        show_logs
        ;;
    clean)
        clean_all
        ;;
    status)
        show_status
        ;;
    *)
        print_usage
        exit 1
        ;;
esac
