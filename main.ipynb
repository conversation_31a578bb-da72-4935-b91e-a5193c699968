{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Cell 1: Import required libraries\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import requests\n", "import json\n", "from datasets import load_dataset\n", "import numpy as np\n", "from transformers import AutoTokenizer\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_length_distribution(lengths, title=\"Text Length Distribution\"):\n", "    \"\"\"\n", "    Create visualizations for text length distribution\n", "    \"\"\"\n", "    if lengths is None:\n", "        print(\"No data to plot\")\n", "        return\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    fig.suptitle(title, fontsize=16)\n", "\n", "    # Histogram\n", "    axes[0,0].hist(lengths, bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[0,0].set_title('Length Distribution')\n", "    axes[0,0].set_xlabel('Text Length')\n", "    axes[0,0].set_ylabel('Frequency')\n", "\n", "    # Box plot\n", "    axes[0,1].boxplot(lengths)\n", "    axes[0,1].set_title('Length Box Plot')\n", "    axes[0,1].set_ylabel('Text Length')\n", "\n", "    # Cumulative distribution\n", "    sorted_lengths = np.sort(lengths)\n", "    cumulative = np.arange(1, len(sorted_lengths) + 1) / len(sorted_lengths)\n", "    axes[1,0].plot(sorted_lengths, cumulative, marker='o', markersize=2)\n", "    axes[1,0].set_title('Cumulative Distribution')\n", "    axes[1,0].set_xlabel('Text Length')\n", "    axes[1,0].set_ylabel('Cumulative Probability')\n", "\n", "    # Statistics summary\n", "    stats_text = f\"\"\"\n", "    Count: {len(lengths)}\n", "    Min: {min(lengths)}\n", "    Max: {max(lengths)}\n", "    Mean: {np.mean(lengths):.2f}\n", "    Median: {np.median(lengths):.2f}\n", "    Std: {np.std(lengths):.2f}\n", "    \"\"\"\n", "    axes[1,1].text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')\n", "    axes[1,1].set_title('Statistics Summary')\n", "    axes[1,1].axis('off')\n", "\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def analyze_dataset_direct(dataset_name, column_name, tokenizer, split='train'):\n", "    \"\"\"\n", "    Analyze dataset column lengths by loading the dataset directly\n", "    \"\"\"\n", "    try:\n", "        # Load the dataset\n", "        dataset = load_dataset(dataset_name)\n", "\n", "        # There's an item in junnei/ko-limo that has an empty solution. Remove it.\n", "        dataset['train'] = dataset['train'].filter(lambda x: len(x['solution']) > 0)\n", "        dataset['train']\n", "\n", "        # Remove data with length larger than 40960\n", "        dataset['train'] = dataset['train'].filter(lambda x: len(x['solution']) < 40960)\n", "\n", "        # Get the specified split\n", "        data_split = dataset[split]\n", "\n", "        # Check if column exists\n", "        if column_name in data_split.column_names:\n", "            # Extract the column data\n", "            column_data = data_split[column_name]\n", "\n", "            # Calculate lengths\n", "            # lengths = [len(str(item)) for item in column_data]\n", "\n", "            #calcluate token length\n", "            lengths = [len(tokenizer(str(item))['input_ids']) for item in column_data]\n", "\n", "            # Calculate statistics\n", "            stats = {\n", "                'count': len(lengths),\n", "                'min_length': min(lengths),\n", "                'max_length': max(lengths),\n", "                'mean_length': np.mean(lengths),\n", "                'median_length': np.median(lengths),\n", "                'std_length': np.std(lengths)\n", "            }\n", "\n", "            plot_length_distribution(lengths, f\"{dataset_name} {split} {column_name}\")\n", "            print(\"Dataset Statistics:\")\n", "            for key, value in stats.items():\n", "                print(f\"{key}: {value}\")\n", "            return lengths, stats\n", "        else:\n", "            print(f\"Column '{column_name}' not found. Available columns: {data_split.column_names}\")\n", "            return None, None\n", "\n", "    except Exception as e:\n", "        print(f\"Error loading dataset: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["tokenizer = AutoTokenizer.from_pretrained(\"meta-llama/Llama-3.1-8B\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["lengths, stats = analyze_dataset_direct(\"GAIR/LIMO\", \"solution\", tokenizer)\n", "lengths, stats = analyze_dataset_direct(\"junnei/ko-limo\", \"solution\", tokenizer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset\n", "from datasets import load_dataset\n", "\n", "# I'm going to create a new dataset where question column from junnei/ko-limo is kept as question, question from GAIR/LIMO as translation, solution from GAIR/LIMO as solution, and answer from GAIR/LIMO as answer.\n", "kolimo = load_dataset(\"junnei/ko-limo\")\n", "limo = load_dataset(\"GAIR/LIMO\")\n", "\n", "new_data = {\n", "    'question': kolimo['train']['question'],\n", "    'translation': limo['train']['question'],\n", "    'solution': limo['train']['solution'],\n", "    'answer': limo['train']['answer']\n", "}\n", "\n", "new_dataset = Dataset.from_dict(new_data)\n", "\n", "# Remove data with length larger than 40960 and less than 1\n", "new_dataset = new_dataset.filter(lambda x: len(x['question']) < 40960)\n", "new_dataset = new_dataset.filter(lambda x: len(x['question']) > 0)\n", "new_dataset = new_dataset.filter(lambda x: len(x['translation']) < 40960)\n", "new_dataset = new_dataset.filter(lambda x: len(x['translation']) > 0)\n", "\n", "# split train and test\n", "new_dataset = new_dataset.train_test_split(test_size=0.2, seed=42)\n", "\n", "new_dataset.push_to_hub(\"junkim100/limo_crosslingual_ko_en\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def max_token_length(dataset, tokenizer, columns):\n", "    return max([len(tokenizer(' '.join([x[col] for col in columns]))['input_ids']) for x in dataset])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["limo_crosslingual_ko_en = load_dataset(\"junkim100/limo_crosslingual_ko_en\")\n", "\n", "max_token_length(limo_crosslingual_ko_en['train'], tokenizer, ['question', 'translation', 'solution', 'answer'])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["9064"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["processed = load_dataset(\"junkim100/Lima-X-Processed\")\n", "\n", "max_token_length(processed['train'], tokenizer, ['en_input', 'en_output', 'de_input', 'de_output'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# LIMA"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported.\n", "Configuration set for dataset: lamarr-org/Lima-X\n", "Target languages: EN, DE, ES, FR, IT\n", "Data aggregators initialized.\n", "\n", "Starting processing for dataset: lamarr-org/Lima-X\n", "\n", "Processing language: EN\n", "  Processing original split: train for language EN\n", "  Processing original split: val for language EN\n", "\n", "Processing language: DE\n", "  Processing original split: train for language DE\n", "  Processing original split: val for language DE\n", "\n", "Processing language: ES\n", "  Processing original split: train for language ES\n", "  Processing original split: val for language ES\n", "\n", "Processing language: FR\n", "  Processing original split: train for language FR\n", "  Processing original split: val for language FR\n", "\n", "Processing language: IT\n", "  Processing original split: train for language IT\n", "  Processing original split: val for language IT\n", "\n", "Finished loading and initial processing of all specified languages and original splits.\n", "\n", "Final column names for the new dataset structure: id_numeric, en_input, en_output, de_input, de_output, es_input, es_output, fr_input, fr_output, it_input, it_output\n", "\n", "Number of records prepared for the new 'train' split: 1030\n", "Number of records prepared for the new 'val' split: 52\n", "\n", "Hugging Face Dataset objects created for 'train' and 'val' splits.\n", "\n", "--- Final Combined Hugging Face DatasetDict ---\n", "DatasetDict({\n", "    train: Dataset({\n", "        features: ['id_numeric', 'en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 1030\n", "    })\n", "    val: Dataset({\n", "        features: ['id_numeric', 'en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 52\n", "    })\n", "})\n", "\n", "--- Example of the first record in the new 'train' split ---\n", "id_numeric                                                    0\n", "en_input      Can brain cells move? By movement I mean long ...\n", "en_output     The question is relatively broad and one shoul...\n", "de_input      K<PERSON>nnen sich Gehirnzellen bewegen? Mit Bewegung...\n", "de_output     Die Frage ist relativ weit gefasst, und man so...\n", "es_input      ¿<PERSON>ueden moverse las células cerebrales? Por mo...\n", "es_output     La pregunta es relativamente amplia y hay que ...\n", "fr_input      Les cellules du cerveau peuvent-elles se dépla...\n", "fr_output     La question est relativement vaste et il faut ...\n", "it_input      Le cellule cerebrali possono muoversi? Per mov...\n", "it_output     La questione è relativamente ampia e bisogna t...\n", "dtype: object\n", "\n", "--- Example of the first record in the new 'val' split ---\n", "id_numeric                                                    0\n", "en_input      Why is the price on the shelves for drinks oft...\n", "en_output     You are paying for the drink and a deposit for...\n", "de_input      Warum unterscheiden sich die Preise in den Get...\n", "de_output     Sie zahlen für das Getränk und ein Pfand für d...\n", "es_input      ¿Por qué el precio de las bebidas en las estan...\n", "es_output     Pagas por la bebida y un depósito por la botel...\n", "fr_input      Pourquoi le prix des boissons affiché dans les...\n", "fr_output     Vous payez la boisson et une consigne pour la ...\n", "it_input      Perché il prezzo delle bevande sugli scaffali ...\n", "it_output     Si paga la bevanda e un deposito per la bottig...\n", "dtype: object\n", "\n", "--- Sample of the new 'train' data (first 3 rows as DataFrame) ---\n", "   id_numeric                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          en_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    en_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                de_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        de_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   es_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 es_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               fr_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     fr_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    it_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      it_output\n", "0           0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              Can brain cells move? By movement I mean long distance migration (preferably within the brain only).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       The question is relatively broad and one should take into account that the brain not only consists of neurons, but also glial cells (supportive cells) and pre-mitotic neuronal stem cells. Furthermore, as critical fellow-scientists have indicated, developmental stage is very important, as the developing embryonic brain is very different from the adult brain.\\nHowever, after sifting through various publications, the answer to the question is actually remarkably simple: Yes, brain cells migrate.\\nIn  the adult brain glial cells migrate in the brain (Klämb<PERSON>, 2009). Glial cells are involved in a myriad of functions, but a notable example of migrating glial cells are the oligodendrocytes that migrate relative long distances to find their target axons onto which they wrap themselves to form the insulating myelin sheath (<PERSON><PERSON> and <PERSON>, 2002).\\nNeuronal stem cells migrate over long distances in response to injury (<PERSON><PERSON><PERSON> et al., 2004) and they migrate from specific stem-cell locations (e.g., hippocampus and subventricular zone) to other regions (<PERSON>, 2003).\\nPost-mitotic, but non-differentiated neurons have been shown to migrate in the adult brain in fish (<PERSON> et al., 2012), and in mammals and non-human primates as well (<PERSON><PERSON> et al., 2011).\\nNot surprisingly, glial cells, stem cells and neurons also migrate during embryonic development. Most notably, post-mitotic neurons destined to fulfill peripheral functions have to migrate over relatively long distances from the neural crest to their target locations (Neuroscience, 2nd ed, Neuronal Migration).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Können sich Gehirnzellen bewegen? Mit Bewegung meine ich die Wanderung über große Entfernungen (vorzugsweise nur innerhalb des Gehirns).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Die Frage ist relativ weit gefasst, und man sollte berücksichtigen, dass das Gehirn nicht nur aus Neuronen besteht, sondern auch aus Gliazellen (Stützzellen) und prä-mitotischen neuronalen Stammzellen. Außerdem ist, wie kritische Kollegen anmerkten, das Entwicklungsstadium sehr wichtig, da sich das sich entwickelnde embryonale Gehirn stark vom erwachsenen Gehirn unterscheidet.\\nNach Durchsicht verschiedener Veröffentlichungen ist die Antwort auf die Frage jedoch eigentlich bemerkenswert einfach: Ja, Gehirnzellen wandern.\\nIm erwachsenen Gehirn wandern Gliazellen im Gehirn (Klämbt, 2009). Gliazellen sind an einer Vielzahl von Funktionen beteiligt, aber ein bemerkenswertes Beispiel für wandernde Gliazellen sind die Oligodendrozyten, die über relativ große Entfernungen wandern, um ihre Zielaxone zu finden, um die sie sich wickeln und die isolierende Myelinscheide bilden (Tsai und Miller, 2002).\\nNeuronale Stammzellen wandern als Reaktion auf Verletzungen über weite Entfernungen (Imitola et al., 2004) und sie wandern von bestimmten Stammzellstandorten (z. B. Hippocampus und subventrikuläre Zone) in andere Regionen (Clarke, 2003).\\nBei Fischen (Scott et al., 2012), aber auch bei Säugetieren und nichtmenschlichen Primaten (Sawada et al., 2011) wurde gezeigt, dass post-mitotische, aber nicht-differenzierte Neuronen in das erwachsene Gehirn wandern.\\nEs überrascht nicht, dass Gliazellen, Stammzellen und Neuronen auch während der Embryonalentwicklung wandern. Vor allem postmitotische Neuronen, die periphere Funktionen erfüllen sollen, müssen über relativ lange Strecken von der Neuralleiste zu ihren Zielorten wandern (Neuroscience, 2nd ed, Neuronal Migration).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     ¿Pueden moverse las células cerebrales? Por movimiento entiendo migración a larga distancia (preferiblemente sólo dentro del cerebro).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  La pregunta es relativamente amplia y hay que tener en cuenta que el cerebro no sólo está formado por neuronas, sino también por células gliales (células de sostén) y células madre neuronales premíticas. Además, como han indicado compañeros científicos críticos, la etapa de desarrollo es muy importante, ya que el cerebro embrionario en desarrollo es muy diferente del cerebro adulto.\\nSin embargo, tras escudriñar diversas publicaciones, la respuesta a la pregunta es en realidad notablemente sencilla: Sí, las células cerebrales migran.\\nEn el cerebro adulto, las células gliales migran en el cerebro (Klämbt, 2009). Las células gliales están implicadas en una miríada de funciones, pero un ejemplo notable de células gliales migratorias son los oligodendrocitos, que migran distancias relativamente largas para encontrar sus axones diana sobre los que se envuelven para formar la vaina aislante de mielina (Tsai y Miller, 2002).\\nLas células madre neuronales migran largas distancias en respuesta a una lesión (Imitola et al., 2004) y migran desde localizaciones específicas de células madre (por ejemplo, hipocampo y zona subventricular) a otras regiones (Clarke, 2003).\\nSe ha demostrado que las neuronas postmitóticas, pero no diferenciadas, migran en el cerebro adulto en peces (Scott et al., 2012), y también en mamíferos y primates no humanos (Sawada et al., 2011).\\nNo es sorprendente que las células gliales, las células madre y las neuronas también migren durante el desarrollo embrionario. En particular, las neuronas posmitóticas destinadas a desempeñar funciones periféricas tienen que migrar distancias relativamente largas desde la cresta neural hasta sus lugares de destino (Neuroscience, 2nd ed, Neuronal Migration).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Les cellules du cerveau peuvent-elles se déplacer ? Par mouvement, j'entends une migration sur une longue distance (de préférence à l'intérieur du cerveau uniquement).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     La question est relativement vaste et il faut tenir compte du fait que le cerveau n'est pas seulement constitué de neurones, mais aussi de cellules gliales (cellules de soutien) et de cellules souches neuronales pré-mitotiques. En outre, comme l'ont indiqué des collègues scientifiques critiques, le stade de développement est très important, car le cerveau embryonnaire en développement est très différent du cerveau adulte.\\nCependant, après avoir passé au crible diverses publications, la réponse à la question est en fait remarquablement simple : Oui, les cellules cérébrales migrent.\\nDans le cerveau adulte, les cellules gliales migrent dans le cerveau (Klämbt, 2009). Les cellules gliales sont impliquées dans une myriade de fonctions, mais un exemple notable de cellules gliales migrantes sont les oligodendrocytes qui migrent sur des distances relativement longues pour trouver leurs axones cibles sur lesquels ils s'enroulent pour former la gaine isolante de myéline (Tsai et Miller, 2002).\\nLes cellules souches neuronales migrent sur de longues distances en réponse à une lésion (Imitola et al., 2004) et elles migrent d'endroits spécifiques (par exemple, l'hippocampe et la zone subventriculaire) vers d'autres régions (Clarke, 2003).\\nIl a été démontré que les neurones post-mitotiques, mais non différenciés, migrent dans le cerveau adulte des poissons (Scott et al., 2012), ainsi que chez les mammifères et les primates non humains (Sawada et al., 2011).\\nIl n'est pas surprenant que les cellules gliales, les cellules souches et les neurones migrent également au cours du développement embryonnaire. En particulier, les neurones post-mitotiques destinés à remplir des fonctions périphériques doivent migrer sur des distances relativement longues depuis la crête neurale jusqu'à leur emplacement cible (Neuroscience, 2e édition, Neuronal Migration).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Le cellule cerebrali possono muoversi? Per movimento intendo migrazione a lunga distanza (preferibilmente solo all'interno del cervello).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              La questione è relativamente ampia e bisogna tenere conto del fatto che il cervello non è costituito solo da neuroni, ma anche da cellule gliali (cellule di supporto) e da cellule staminali neuronali pre-mitotiche. Inoltre, come hanno indicato i colleghi critici, lo stadio di sviluppo è molto importante, poiché il cervello embrionale in via di sviluppo è molto diverso da quello adulto.\\nTuttavia, dopo aver passato al setaccio diverse pubblicazioni, la risposta alla domanda è in realtà straordinariamente semplice: Sì, le cellule cerebrali migrano.\\nNel cervello adulto le cellule gliali migrano (Klämbt, 2009). Le cellule gliali sono coinvolte in una miriade di funzioni, ma un esempio notevole di cellule gliali che migrano sono gli oligodendrociti che migrano per distanze relativamente lunghe per trovare gli assoni bersaglio sui quali si avvolgono per formare la guaina mielinica isolante (Tsai e Miller, 2002).\\nLe cellule staminali neuronali migrano su lunghe distanze in risposta a lesioni (Imitola et al., 2004) e migrano da specifiche sedi di cellule staminali (ad esempio, ippocampo e zona subventricolare) ad altre regioni (Clarke, 2003).\\nÈ stato dimostrato che i neuroni post-mitotici, ma non differenziati, migrano nel cervello adulto dei pesci (Scott et al., 2012) e anche nei mammiferi e nei primati non umani (Sawada et al., 2011).\\nNon sorprende che anche le cellule gliali, le cellule staminali e i neuroni migrino durante lo sviluppo embrionale. In particolare, i neuroni post-mitotici destinati a svolgere funzioni periferiche devono migrare per distanze relativamente lunghe dalla cresta neurale alle loro sedi di destinazione (Neuroscience, 2nd ed, Neuronal Migration).\n", "1           1  In our computer systems lecture we were introduced to the MIPS processor. It was (re)developed over the course of the term and has in fact been quite easy to understand. It uses a RISC design, that is its elementary commands are regularly encoded and there are only few of them in order to keep the wires simple.\\nIt was mentioned that CISC follows a different philosophy. I looked briefly at the x86 instruction set and was shocked. I can not image how anyone would want to build a processor that uses so complex a command set!\\nSo I figure there have to be good arguments why large portions of the processor market use CISC architectures. What are they?   There is a general historical trend.\\nIn the olden days, memories were small, and so programs were perforce small. Also, compilers were not very smart, and many programs were written in assembler, so it was considered a good thing to be able to write a program using few instructions. Instruction pipelines were simple, and processors grabbed one instruction at a time to execute it. The machinery inside the processor was quite complex anyway; decoding instructions was not felt to be much of a burden. \\nIn the 1970s, CPU and compiler designers realized that having such complex instructions was not so helpful after all. It was difficult to design processors in which those instructions were really efficient, and it was difficult to design compilers that really took advantage of these instructions. Chip area and compiler complexity was better spent on more generic pursuits such as more general-purpose registers. The Wikipedia article on RISC explains this in more detail.\\nMIPS is the ultimate RISC architecture, which is why it's taught so often.\\nThe x86 family is a bit different. It was originally a CISC architecture meant for systems with very small memory (no room for large instructions), and has undergone many successive versions. Today's x86 instruction set is not only complicated because it's CISC, but because it's really a 8088 with a 80386 with a Pentium possibly with an x86_64 processor.\\nIn today's world, RISC and CISC are no longer the black-and-white distinction they might have been once. Most CPU architectures have evolved to different shades of grey.\\nOn the RISC side, some modern MIPS variants have added multiplication and division instructions, with a non-uniform encoding. ARM processors have become more complex: many of them have a 16-bit instruction set called Thumb in addition to the “original” 32-bit instructions, not to mention Jazelle to execute JVM instructions on the CPU. Modern ARM processors also have SIMD instructions for multimedia applications: some complex instructions do pay after all.\\nOn the CISC side, all recent processors are to some extent RISC inside. They have microcode to define all these complex macro instructions. The sheer complexity of the processor makes the design of each model take several years, even with a RISC design, what with the large number of components, with pipelining and predictive execution and whatnot.\\nSo why do the fastest processors remain CISC outside? Part of it, in the case of the x86 (32-bit and 64-bit) family, is historical compatibility. But that's not the whole of it. In the early 2000s, Intel tried pushing the Itanium architecture. Itanium is an extreme case of complex instructions (not really CISC, though: its design has been dubbed EPIC). It even does away with the old-fashioned idea of executing instructions in sequence: all instructions are executed in parallel until the next barrier. One of the reasons Itanium didn't take is that nobody, whether at Intel or elsewhere, could write a decent compiler for it. Now a good old mostly-sequential processor like x86_64, that's something we understand.  In unserer Vorlesung über Computersysteme wurden wir mit dem MIPS-Prozessor bekannt gemacht. Er wurde im Laufe des Semesters (neu) entwickelt und ist eigentlich recht einfach zu verstehen. Er verwendet ein RISC-Design, d.h. seine elementaren Befehle sind regelmäßig kodiert und es gibt nur wenige davon, um die Verdrahtung einfach zu halten.\\nEs wurde erwähnt, dass CISC eine andere Philosophie verfolgt. Ich habe mir kurz den x86-Befehlssatz angesehen und war schockiert. Ich kann mir nicht vorstellen, wie jemand einen Prozessor bauen will, der einen so komplexen Befehlssatz verwendet!\\nEs muss also gute Argumente geben, warum große Teile des Prozessormarktes CISC-Architekturen verwenden. Welche sind das?  Es gibt einen allgemeinen historischen Trend.\\nFrüher waren die Speicher klein und die Programme daher zwangsläufig auch klein. Außerdem waren die Compiler nicht sehr intelligent, und viele Programme wurden in Assembler geschrieben, so dass es als vorteilhaft galt, ein Programm mit wenigen Anweisungen schreiben zu können. Die Befehlspipelines waren einfach, und die Prozessoren nahmen einen Befehl nach dem anderen auf und führten ihn aus. Die Maschinerie im Inneren des Prozessors war ohnehin recht komplex; die Dekodierung von Anweisungen wurde nicht als große Belastung empfunden.\\nIn den 1970er Jahren erkannten die Entwickler von CPUs und Compilern, dass solch komplexe Befehle doch nicht so hilfreich waren. Es war schwierig, Prozessoren zu entwickeln, in denen diese Befehle wirklich effizient waren, und es war schwierig, Compiler zu entwickeln, die diese Befehle wirklich nutzten. Der Platz auf dem Chip und die Komplexität des Compilers wurden besser für allgemeinere Aufgaben wie z. B. Mehrzweckregister verwendet. Der Wikipedia-Artikel über RISC erklärt dies ausführlicher.\\nMIPS ist die ultimative RISC-Architektur, weshalb sie auch so oft gelehrt wird.\\nDie x86-Familie ist ein wenig anders. Ursprünglich handelte es sich um eine CISC-Architektur, die für Systeme mit sehr kleinem Speicher (kein Platz für große Befehle) gedacht war und viele aufeinander folgende Versionen durchlaufen hat. Der heutige x86-Befehlssatz ist nicht nur kompliziert, weil es sich um eine CISC-Architektur handelt, sondern weil es sich in Wirklichkeit um einen 8088 mit einem 80386 mit einem Pentium, möglicherweise mit einem x86_64-Prozessor, handelt.\\nIn der heutigen Welt sind RISC und CISC nicht mehr die Schwarz-Weiß-Unterscheidung, die sie vielleicht einmal waren. Die meisten CPU-Architekturen haben sich zu verschiedenen Grautönen entwickelt.\\nAuf der RISC-Seite haben einige moderne MIPS-Varianten Multiplikations- und Divisionsbefehle mit einer uneinheitlichen Kodierung hinzugefügt. ARM-Prozessoren sind komplexer geworden: Viele von ihnen haben zusätzlich zu den \"ursprünglichen\" 32-Bit-Befehlen einen 16-Bit-Befehlssatz namens Thumb, ganz zu schweigen von Jazelle zur Ausführung von JVM-Befehlen auf der CPU. Moderne ARM-Prozessoren verfügen auch über SIMD-Befehle für Multimedia-Anwendungen: einige komplexe Befehle lohnen sich also doch.\\nAuf der CISC-Seite sind alle neueren Prozessoren bis zu einem gewissen Grad RISC-intern. Sie verfügen über Mikrocode, um all diese komplexen Makrobefehle zu definieren. Die schiere Komplexität des Prozessors führt dazu, dass die Entwicklung jedes Modells mehrere Jahre in Anspruch nimmt, selbst bei einem RISC-Design, angesichts der großen Anzahl von Komponenten, des Pipelining und der vorausschauenden Ausführung und so weiter.\\nWarum also bleiben die schnellsten Prozessoren außerhalb des CISC-Bereichs? Im Fall der x86-Familie (32-Bit und 64-Bit) ist ein Teil davon die historische Kompatibilität. Aber das ist nicht der ganze Grund. In den frühen 2000er Jahren versuchte Intel, die Itanium-Architektur voranzutreiben. Itanium ist ein Extremfall komplexer Anweisungen (allerdings nicht wirklich CISC: sein Design wurde als EPIC bezeichnet). Er verzichtet sogar auf die altmodische Idee, Befehle nacheinander auszuführen: Alle Befehle werden bis zur nächsten Barriere parallel ausgeführt. Einer der Gründe, warum sich Itanium nicht durchgesetzt hat, ist, dass niemand, weder bei Intel noch anderswo, einen anständigen Compiler dafür schreiben konnte. Bei einem guten alten, meist sequenziellen Prozessor wie x86_64 ist das etwas, das wir verstehen.  En nuestra clase sobre sistemas informáticos conocimos el procesador MIPS. Se ha ido (re)desarrollando a lo largo del curso y, de hecho, es bastante fácil de entender. Utiliza un diseño RISC, es decir, sus comandos elementales están codificados de forma regular y sólo hay unos pocos para mantener los cables simples.\\nSe ha mencionado que CISC sigue una filosofía diferente. Miré brevemente el conjunto de instrucciones x86 y me quedé asombrado. No puedo imaginarme cómo alguien querría construir un procesador que utiliza un conjunto de instrucciones tan complejo.\\nAsí que me imagino que tiene que haber buenos argumentos para que gran parte del mercado de procesadores utilice arquitecturas CISC. ¿Cuáles son?  Existe una tendencia histórica general.\\nAntiguamente, las memorias eran pequeñas y, por tanto, los programas también lo eran. Además, los compiladores no eran muy inteligentes y muchos programas se escribían en ensamblador, por lo que se consideraba positivo poder escribir un programa utilizando pocas instrucciones. Los pipelines de instrucciones eran sencillos, y los procesadores cogían una instrucción cada vez para ejecutarla. De todas formas, la maquinaria del procesador era bastante compleja, por lo que decodificar instrucciones no se consideraba una gran carga.\\nEn los años 70, los diseñadores de CPU y compiladores se dieron cuenta de que tener instrucciones tan complejas no era tan útil después de todo. Era difícil diseñar procesadores en los que esas instrucciones fueran realmente eficientes, y era difícil diseñar compiladores que realmente aprovecharan esas instrucciones. El área del chip y la complejidad del compilador se empleaban mejor en tareas más genéricas, como registros de propósito más general. El artículo de Wikipedia sobre RISC lo explica con más detalle.\\nMIPS es la arquitectura RISC por excelencia, y por eso se enseña tan a menudo.\\nLa familia x86 es un poco diferente. Originalmente era una arquitectura CISC pensada para sistemas con muy poca memoria (sin espacio para instrucciones grandes), y ha sufrido muchas versiones sucesivas. El conjunto de instrucciones x86 actual no sólo es complicado por ser CISC, sino porque en realidad es un 8088 con un 80386 con un Pentium posiblemente con un procesador x86_64.\\nEn el mundo actual, RISC y CISC ya no son la distinción en blanco y negro que podían ser antaño. La mayoría de las arquitecturas de CPU han evolucionado hacia distintos tonos de gris.\\nEn el lado RISC, algunas variantes modernas de MIPS han añadido instrucciones de multiplicación y división, con una codificación no uniforme. Los procesadores ARM se han vuelto más complejos: muchos de ellos tienen un conjunto de instrucciones de 16 bits llamado Thumb, además de las instrucciones \"originales\" de 32 bits, por no hablar de Jazelle para ejecutar instrucciones JVM en la CPU. Los procesadores ARM modernos también tienen instrucciones SIMD para aplicaciones multimedia: al fin y al cabo, algunas instrucciones complejas merecen la pena.\\nEn el lado CISC, todos los procesadores recientes son hasta cierto punto RISC en su interior. Disponen de microcódigo para definir todas estas macroinstrucciones complejas. La mera complejidad del procesador hace que el diseño de cada modelo lleve varios años, incluso con un diseño RISC, debido al gran número de componentes, el pipelining, la ejecución predictiva y demás.\\nEntonces, ¿por qué los procesadores más rápidos siguen siendo CISC fuera? En parte, en el caso de la familia x86 (32 y 64 bits), por compatibilidad histórica. Pero eso no es todo. A principios de la década de 2000, Intel intentó impulsar la arquitectura Itanium. Itanium es un caso extremo de instrucciones complejas (aunque no realmente CISC: su diseño se ha denominado EPIC). Incluso prescinde de la anticuada idea de ejecutar las instrucciones en secuencia: todas las instrucciones se ejecutan en paralelo hasta la siguiente barrera. Una de las razones por las que Itanium no triunfó es que nadie, ni en Intel ni en ningún otro sitio, fue capaz de escribir un compilador decente para él. Ahora, un buen procesador secuencial como x86_64 es algo que entendemos.  Dans notre cours sur les systèmes informatiques, nous avons découvert le processeur MIPS. Il a été (re)développé au cours du semestre et s'est en fait avéré assez facile à comprendre. Il utilise une conception RISC, c'est-à-dire que ses commandes élémentaires sont codées régulièrement et qu'elles sont peu nombreuses pour que les fils restent simples.\\nIl a été mentionné que le CISC suit une philosophie différente. J'ai examiné brièvement le jeu d'instructions x86 et j'ai été choqué. Je n'arrive pas à imaginer comment quelqu'un pourrait vouloir construire un processeur qui utilise un jeu d'instructions aussi complexe !\\nJe me suis donc dit qu'il devait y avoir de bons arguments pour expliquer pourquoi une grande partie du marché des processeurs utilise des architectures CISC. De quoi s'agit-il ?  Il existe une tendance historique générale.\\nAutrefois, les mémoires étaient petites, et donc les programmes étaient forcément petits. En outre, les compilateurs n'étaient pas très intelligents et de nombreux programmes étaient écrits en assembleur, de sorte qu'il était considéré comme une bonne chose de pouvoir écrire un programme en utilisant peu d'instructions. Les pipelines d'instructions étaient simples et les processeurs saisissaient une instruction à la fois pour l'exécuter. La machinerie à l'intérieur du processeur était de toute façon assez complexe ; le décodage des instructions n'était pas considéré comme un fardeau.\\nDans les années 1970, les concepteurs d'unités centrales et de compilateurs se sont rendu compte que des instructions aussi complexes n'étaient finalement pas si utiles. Il était difficile de concevoir des processeurs dans lesquels ces instructions étaient vraiment efficaces, et il était difficile de concevoir des compilateurs qui tirent vraiment parti de ces instructions. La surface des puces et la complexité des compilateurs étaient mieux utilisées pour des tâches plus génériques telles que des registres à usage plus général. L'article de Wikipédia sur les RISC explique cela plus en détail.\\nMIPS est l'architecture RISC par excellence, c'est pourquoi elle est si souvent enseignée.\\nLa famille x86 est un peu différente. Il s'agissait à l'origine d'une architecture CISC destinée aux systèmes dotés d'une très petite mémoire (pas de place pour les instructions volumineuses), qui a connu de nombreuses versions successives. Le jeu d'instructions x86 d'aujourd'hui n'est pas seulement compliqué parce qu'il s'agit d'une architecture CISC, mais aussi parce qu'il s'agit en fait d'un 8088 avec un 80386 avec un Pentium éventuellement avec un processeur x86_64.\\nDans le monde d'aujourd'hui, RISC et CISC ne sont plus la distinction noire et blanche qu'ils ont pu être par le passé. La plupart des architectures de processeurs ont évolué vers différentes nuances de gris.\\nDu côté RISC, certaines variantes modernes du MIPS ont ajouté des instructions de multiplication et de division, avec un codage non uniforme. Les processeurs ARM sont devenus plus complexes : nombre d'entre eux disposent d'un jeu d'instructions de 16 bits appelé Thumb en plus des instructions \"originales\" de 32 bits, sans parler de Jazelle pour exécuter les instructions JVM sur l'unité centrale. Les processeurs ARM modernes disposent également d'instructions SIMD pour les applications multimédias : certaines instructions complexes sont en effet payantes.\\nDu côté CISC, tous les processeurs récents sont, dans une certaine mesure, RISC à l'intérieur. Ils disposent d'un microcode pour définir toutes ces macro-instructions complexes. La complexité même du processeur fait que la conception de chaque modèle prend plusieurs années, même avec une conception RISC, compte tenu du grand nombre de composants, du pipelining, de l'exécution prédictive, etc.\\nAlors pourquoi les processeurs les plus rapides restent-ils des processeurs CISC ? Dans le cas de la famille x86 (32 et 64 bits), il s'agit en partie d'une compatibilité historique. Mais ce n'est pas tout. Au début des années 2000, Intel a tenté de promouvoir l'architecture Itanium. L'Itanium est un cas extrême d'instructions complexes (pas vraiment CISC, cependant : sa conception a été baptisée EPIC). Elle abandonne même l'idée démodée d'exécuter les instructions en séquence : toutes les instructions sont exécutées en parallèle jusqu'à la barrière suivante. L'une des raisons pour lesquelles l'Itanium n'a pas été adopté est que personne, que ce soit chez Intel ou ailleurs, n'a pu écrire un compilateur décent pour lui. Maintenant, un bon vieux processeur essentiellement séquentiel comme x86_64, c'est quelque chose que nous comprenons.  Durante la lezione sui sistemi informatici ci è stato presentato il processore MIPS. Questo processore è stato (ri)sviluppato nel corso del semestre ed è stato abbastanza facile da capire. Utilizza un design RISC, cioè i suoi comandi elementari sono codificati regolarmente e sono pochi per mantenere i fili semplici.\\nÈ stato detto che il CISC segue una filosofia diversa. Ho dato una breve occhiata al set di istruzioni x86 e sono rimasto scioccato. Non riesco a immaginare come qualcuno possa voler costruire un processore che utilizza un set di comandi così complesso!\\nQuindi ho pensato che ci devono essere buoni argomenti per cui gran parte del mercato dei processori utilizza architetture CISC. Quali sono?  Esiste una tendenza storica generale.\\nUn tempo le memorie erano piccole e quindi i programmi erano per forza piccoli. Inoltre, i compilatori non erano molto intelligenti e molti programmi erano scritti in assembler, per cui era considerata una buona cosa poter scrivere un programma utilizzando poche istruzioni. Le pipeline di istruzioni erano semplici e i processori prendevano un'istruzione alla volta per eseguirla. Il macchinario all'interno del processore era comunque piuttosto complesso; la decodifica delle istruzioni non era considerata un grosso peso.\\nNegli anni '70, i progettisti di CPU e compilatori si resero conto che avere istruzioni così complesse non era poi così utile. Era difficile progettare processori in cui queste istruzioni fossero davvero efficienti, ed era difficile progettare compilatori che sfruttassero davvero queste istruzioni. L'area del chip e la complessità del compilatore erano meglio impiegate in attività più generiche, come i registri di uso più generale. L'articolo di Wikipedia sui RISC spiega questo aspetto in modo più dettagliato.\\nMIPS è l'architettura RISC per eccellenza, ed è per questo che viene insegnata così spesso.\\nLa famiglia x86 è un po' diversa. Originariamente era un'architettura CISC destinata a sistemi con memoria molto piccola (non c'è spazio per istruzioni di grandi dimensioni) e ha subito molte versioni successive. L'odierno set di istruzioni x86 non è complicato solo perché è CISC, ma anche perché è in realtà un 8088 con un 80386 con un Pentium forse con un processore x86_64.\\nNel mondo di oggi, RISC e CISC non sono più una distinzione in bianco e nero come lo erano un tempo. La maggior parte delle architetture delle CPU si è evoluta verso diverse sfumature di grigio.\\nSul versante RISC, alcune moderne varianti di MIPS hanno aggiunto istruzioni di moltiplicazione e divisione, con una codifica non uniforme. I processori ARM sono diventati più complessi: molti di essi hanno un set di istruzioni a 16 bit chiamato Thumb in aggiunta alle istruzioni \"originali\" a 32 bit, per non parlare di Jazelle per eseguire le istruzioni JVM sulla CPU. I moderni processori ARM dispongono anche di istruzioni SIMD per le applicazioni multimediali: alcune istruzioni complesse, dopotutto, pagano.\\nPer quanto riguarda il lato CISC, tutti i processori recenti sono in qualche misura RISC all'interno. Hanno un microcodice per definire tutte le macroistruzioni complesse. La complessità del processore fa sì che la progettazione di ogni modello richieda diversi anni, anche se si tratta di un progetto RISC, per via del gran numero di componenti, del pipelining, dell'esecuzione predittiva e così via.\\nAllora perché i processori più veloci rimangono CISC? In parte, nel caso della famiglia x86 (32 e 64 bit), si tratta di compatibilità storica. Ma non è tutto. Nei primi anni 2000, Intel ha cercato di spingere l'architettura Itanium. Itanium è un caso estremo di istruzioni complesse (non proprio CISC, però: il suo design è stato definito EPIC). Non esiste nemmeno l'idea antiquata di eseguire le istruzioni in sequenza: tutte le istruzioni vengono eseguite in parallelo fino alla barriera successiva. Uno dei motivi per cui Itanium non ha avuto successo è che nessuno, né in Intel né altrove, è riuscito a scrivere un compilatore decente per Itanium. Ora, un buon vecchio processore prevalentemente sequenziale come x86_64, è qualcosa che comprendiamo.\n", "2           2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         View tabular file such as <PERSON><PERSON> from command line, having horizontal and vertical scrolling would be great.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Sure, please take a look at csvkit. It provides a set of tools that adhere to the UNIX philosophy (meaning they are small, simple, single-purposed and can be combined). \\n\\nHere is an example that extracts the ten most populated cities in Germany from the free Maxmind World Cities database and displays the result in a console-readable format:\\n```$ csvgrep -e iso-8859-1 -c 1 -m \"de\" worldcitiespop | csvgrep -c 5 -r \"\\d+\"\\n  | csvsort -r -c 5 -l | csvcut -c 1,2,4,6 | head -n 11 | csvlook\\n-----------------------------------------------------\\n|  line_number | Country | AccentCity | Population  |\\n-----------------------------------------------------\\n|  1           | de      | Berlin     | 3398362     |\\n|  2           | de      | Hamburg    | 1733846     |\\n|  3           | de      | Munich     | 1246133     |\\n|  4           | de      | Cologne    | 968823      |\\n|  5           | de      | Frankfurt  | 648034      |\\n|  6           | de      | Dortmund   | 594255      |\\n|  7           | de      | Stuttgart  | 591688      |\\n|  8           | de      | Düsseldorf | 577139      |\\n|  9           | de      | Essen      | 576914      |\\n|  10          | de      | Bremen     | 546429      |\\n-----------------------------------------------------\\n```\\n\\nCsvkit is platform independent because it is written in Python.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Anzeige von Tabellendateien wie CSV von der Kommandozeile aus, horizontales und vertikales Scrollen wäre toll.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Sicher, werfen Sie einen Blick auf csvkit. Es bietet eine Reihe von Werkzeugen, die der UNIX-Philosophie folgen (d.h. sie sind klein, einfach, zweckgebunden und können kombiniert werden).\\n\\nHier ist ein Beispiel, das die zehn bevölkerungsreichsten Städte in Deutschland aus der freien Maxmind World Cities Datenbank extrahiert und das Ergebnis in einem konsolenlesbaren Format anzeigt:\\n```$ csvgrep -e iso-8859-1 -c 1 -m \"de\" worldcitiespop | csvgrep -c 5 -r \"\\d+\"\\n  | csvsort -r -c 5 -l | csvcut -c 1,2,4,6 | head -n 11 | csvlook\\n-----------------------------------------------------\\n| line_number | Land | AccentCity | Bevölkerung |\\n-----------------------------------------------------\\n| 1 | de | Berlin | 3398362 | de\\n| 2 | de | Hamburg | 1733846\\n| 3 | de | München | 1246133\\n| 4 | de | Köln | 968823\\n| 5 | de | Frankfurt | 648034\\n| 6 | de | Dortmund | 594255 | Dortmund\\n| 7 | die | Stuttgart | 591688\\n| 8 | the | Düsseldorf | 577139\\n| 9 | de | Essen | 576914\\n| 10 | de | Bremen | 546429\\n-----------------------------------------------------\\n```\\n\\nCsvkit ist plattformunabhängig, da es in Python geschrieben ist.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Ver archivo tabular como CSV desde la línea de comandos, tener desplazamiento horizontal y vertical sería genial.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Claro, echa un vistazo a csvkit. Proporciona un conjunto de herramientas que se adhieren a la filosofía UNIX (lo que significa que son pequeñas, simples, de un solo propósito y se pueden combinar).\\n\\nHe aquí un ejemplo que extrae las diez ciudades más pobladas de Alemania de la base de datos gratuita Maxmind World Cities y muestra el resultado en un formato legible por consola:\\n```$ csvgrep -e iso-8859-1 -c 1 -m \"de\" worldcitiespop | csvgrep -c 5 -r \"\\d+\"\\n  | csvvsort -r -c 5 -l | csvcut -c 1,2,4,6 | head -n 11 | csvlook\\n-----------------------------------------------------\\n| País Ciudad Acento Población\\n-----------------------------------------------------\\n| 1 de Berlín 3398362 de\\n| 2 Hamburgo 1733846\\n| 3 Múnich 1246133\\n| 4. Colonia 968823\\n| 5. Frankfurt 648034\\n| 6. de Dortmund 594255\\n| 7. Stuttgart 591688\\n| 8. Düsseldorf 577139\\n| 9. Essen 576914\\n| 10. Bremen 546429\\n-----------------------------------------------------\\n```\\n\\nCsvkit es independiente de la plataforma porque está escrito en Python.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            Visualiser un fichier tabulaire tel que CSV à partir de la ligne de commande, avoir un défilement horizontal et vertical serait formidable.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       Bien sûr, jetez un coup d'œil à csvkit. Il fournit un ensemble d'outils qui adhèrent à la philosophie UNIX (c'est-à-dire qu'ils sont petits, simples, à usage unique et peuvent être combinés).\\n\\nVoici un exemple qui extrait les dix villes les plus peuplées d'Allemagne de la base de données gratuite Maxmind World Cities et affiche le résultat dans un format lisible par la console :\\n``$ csvgrep -e iso-8859-1 -c 1 -m \"de\" worldcitiespop | csvgrep -c 5 -r \"\\d+\"\\n  | csvsort -r -c 5 -l | csvcut -c 1,2,4,6 | head -n 11 | csvlook\\n-----------------------------------------------------\\n| numéro_de_ligne | Pays | AccentCity | Population |\\n-----------------------------------------------------\\n| 1 | de | Berlin | 3398362 | de\\n| 2 | de | Hamburg | 1733846\\n| 3 | de Munich | 1246133\\n| 4 | de | Cologne | 968823\\n| 5 | de | Francfort | 648034\\n| 6 | de | Dortmund | 594255 | Dortmund\\n| 7 | le | Stuttgart | 591688\\n| Düsseldorf | 577139\\n| 9 | de | Essen | 576914\\n| 10 | de | Bremen | 546429\\n-----------------------------------------------------\\n```\\n\\nCsvkit est indépendant de la plate-forme car il est écrit en Python.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Visualizzare file tabellari come CSV dalla riga di comando, con scorrimento orizzontale e verticale sarebbe fantastico.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Certo, date un'occhiata a csvkit. Fornisce un insieme di strumenti che aderiscono alla filosofia UNIX (cioè sono piccoli, semplici, a scopo singolo e possono essere combinati).\\n\\nEcco un esempio che estrae le dieci città più popolate della Germania dal database gratuito Maxmind World Cities e mostra il risultato in un formato leggibile da console:\\n```$ csvgrep -e iso-8859-1 -c 1 -m \"de\" worldcitiespop | csvgrep -c 5 -r \"\\d+\"\\n  | csvsort -r -c 5 -l | csvcut -c 1,2,4,6 | head -n 11 | csvlook\\n-----------------------------------------------------\\n| numero_linea | Paese | AccentCity | Popolazione |\\n-----------------------------------------------------\\n| 1 | de | Berlino | 3398362 | de\\n| 2 | de | Amburgo | 1733846\\n| 3 | de Monaco | 1246133\\n| 4 | de | Colonia | 968823\\n| 5 | de | Francoforte | 648034\\n| 6 | de | Dortmund | 594255 | Dortmund\\n| 7 | the | Stoccarda | 591688\\n| 8 | the | Düsseldorf | 577139\\n| 9 | de | Essen | 576914\\n| 10 | de | Brema | 546429\\n-----------------------------------------------------\\n```\\n\\nCsvkit è indipendente dalla piattaforma perché è scritto in Python.\n", "\n", "--- Sample of the new 'val' data (first 3 rows as DataFrame) ---\n", "   id_numeric                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             en_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      en_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                de_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 de_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      es_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          es_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              fr_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  fr_output                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                it_input                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      it_output\n", "0           0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      Why is the price on the shelves for drinks often different from what I actually pay in supermarkets in Germany?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                You are paying for the drink and a deposit for the bottle. The deposit should be listed on the shelves, but usually in small print. Look for something like \"zzgl. 0,15 Pfand\". As to your initial thought, prices on supermarket shelves include VAT. \\nThere are deposits on many but not all drink containers, the rules are complicated. Deposits apply to non-reusable containers as well as reusable ones, this is to discourage littering and to make sure that the reusable containers are not an economic disadvantage. These deposits can be 8 ct, 10 ct, 15 ct, or 25 ct.\\nYou can reclaim your deposit, even in a different shop than the one where you purchased it, provided they sell the same bottles. Most larger supermarkets have machines to scan the bottles and print out a receipt that can be turned in at the cash register. It is possible to reclaim money even if you do not buy anything.\\nMost Germans return their empty bottles and claim the deposit, of course. When you do not want to do that, it is considered polite to put the bottle next to a public waste bin, not into it. Some people will collect them and reclaim the deposit as a way to make a little money. \\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Warum unterscheiden sich die Preise in den Getränkeregalen oft von dem, was ich in den Supermärkten in Deutschland bezahle?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  Sie zahlen für das Getränk und ein Pfand für die Flasche. Der Pfandbetrag sollte auf den Regalen angegeben sein, aber in der Regel im Kleingedruckten. Suchen Sie nach etwas wie \"zzgl.. 0,15 Pfand\". Was Ihren ersten Gedanken angeht, so enthalten die Preise in den Supermarktregalen die Mehrwertsteuer.\\nAuf viele, aber nicht alle Getränkeverpackungen wird Pfand erhoben, die Regeln sind kompliziert. Das Pfand gilt sowohl für Einweg- als auch für Mehrweggebinde, um der Vermüllung entgegenzuwirken und sicherzustellen, dass die Mehrweggebinde nicht zu einem wirtschaftlichen Nachteil werden. Diese Pfandbeträge können 8 ct, 10 ct, 15 ct oder 25 ct betragen.\\nSie können Ihr Pfand zurückfordern, auch in einem anderen Geschäft als dem, in dem Sie es gekauft haben, sofern dort die gleichen Flaschen verkauft werden. Die meisten größeren Supermärkte verfügen über Geräte, die die Flaschen einscannen und einen Beleg ausdrucken, den Sie an der Kasse vorlegen können. Es ist möglich, das Geld zurückzufordern, auch wenn Sie nichts gekauft haben.\\nDie meisten Deutschen bringen ihre leeren Flaschen zurück und fordern natürlich das Pfand ein. Wenn Sie das nicht tun wollen, gilt es als höflich, die Flasche neben einen öffentlichen Abfalleimer zu stellen, nicht in ihn hinein. Manche Leute sammeln sie ein und fordern das Pfand zurück, um sich ein wenig Geld zu verdienen.\\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             ¿Por qué el precio de las bebidas en las estanterías suele ser diferente del que pago realmente en los supermercados de Alemania?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Pagas por la bebida y un depósito por la botella. El depósito debe figurar en los estantes, pero normalmente en letra pequeña. Busque algo como \"zzgl. 0,15 Pfand\". En cuanto a tu idea inicial, los precios en los estantes de los supermercados incluyen el IVA.\\nHay depósitos en muchos envases de bebidas, pero no en todos, las normas son complicadas. Los depósitos se aplican tanto a los envases no reutilizables como a los reutilizables, para desincentivar el vertido de basura y asegurarse de que los envases reutilizables no supongan una desventaja económica. Estos depósitos pueden ser de 8 ct, 10 ct, 15 ct o 25 ct.\\nPuedes reclamar tu depósito, incluso en una tienda distinta de la que lo compraste, siempre que vendan las mismas botellas. La mayoría de los grandes supermercados tienen máquinas que escanean las botellas e imprimen un recibo que se puede entregar en la caja registradora. Es posible reclamar dinero aunque no se compre nada.\\nLa mayoría de los alemanes devuelven sus botellas vacías y reclaman el depósito, por supuesto. Si no quiere hacerlo, se considera educado depositar la botella junto a una papelera pública, no dentro de ella. Algunas personas las recogen y reclaman el depósito como forma de ganar algo de dinero.\\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              Pourquoi le prix des boissons affiché dans les rayons est-il souvent différent de celui que je paie dans les supermarchés en Allemagne ?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Vous payez la boisson et une consigne pour la bouteille. La consigne doit être indiquée sur les étagères, mais généralement en petits caractères. Cherchez quelque chose comme \"zzgl. 0,15 Pfand\". Pour répondre à votre première question, les prix affichés dans les supermarchés s'entendent TVA comprise.\\nIl existe des consignes sur de nombreux emballages de boissons, mais pas sur tous, et les règles sont complexes. Les consignes s'appliquent aux récipients non réutilisables ainsi qu'aux récipients réutilisables, afin de décourager les déchets et de s'assurer que les récipients réutilisables ne constituent pas un désavantage économique. Ces consignes peuvent être de 8 ct, 10 ct, 15 ct ou 25 ct.\\nVous pouvez récupérer votre consigne, même dans un magasin différent de celui où vous l'avez achetée, à condition qu'il vende les mêmes bouteilles. La plupart des grands supermarchés disposent de machines qui scannent les bouteilles et impriment un reçu que vous pouvez présenter à la caisse. Il est possible de récupérer de l'argent même si vous n'achetez rien.\\nLa plupart des Allemands rapportent leurs bouteilles vides et réclament la consigne, bien entendu. Si vous ne voulez pas le faire, il est poli de déposer la bouteille à côté d'une poubelle publique, et non dans celle-ci. Certaines personnes les récupèrent et réclament la consigne pour se faire un peu d'argent.\\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Perché il prezzo delle bevande sugli scaffali è spesso diverso da quello che effettivamente pago nei supermercati in Germania?                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Si paga la bevanda e un deposito per la bottiglia. Il deposito dovrebbe essere indicato sugli scaffali, ma di solito è scritto in piccolo. Cercate qualcosa come \"zzgl. 0,15 Pfand\". Per quanto riguarda il tuo pensiero iniziale, i prezzi sugli scaffali dei supermercati sono comprensivi di IVA.\\nCi sono depositi su molti ma non su tutti i contenitori per bevande, le regole sono complicate. I depositi si applicano sia ai contenitori non riutilizzabili che a quelli riutilizzabili, per scoraggiare il littering e per assicurarsi che i contenitori riutilizzabili non rappresentino uno svantaggio economico. I depositi possono essere di 8 ct, 10 ct, 15 ct o 25 ct.\\nÈ possibile richiedere il deposito anche in un negozio diverso da quello in cui è stato acquistato, purché venda le stesse bottiglie. La maggior parte dei supermercati più grandi dispone di macchine che scansionano le bottiglie e stampano uno scontrino che può essere consegnato alla cassa. È possibile recuperare il denaro anche se non si acquista nulla.\\nLa maggior parte dei tedeschi restituisce le bottiglie vuote e reclama il deposito, ovviamente. Se non si vuole farlo, è considerato educato mettere la bottiglia accanto a un cestino pubblico, non dentro. Alcune persone le raccolgono e reclamano la cauzione per guadagnare un po' di soldi.\\n\n", "1           1  I often talk to programmers who say \"Don't put multiple return statements in the same  method.\" When I ask them to tell me the reasons why, all I get is \"The coding standard says so.\" or \"It's confusing.\" When they show me solutions with a single return statement, the code looks uglier to me. For example:\\n```\\nif (condition)\\n   return 42;\\nelse\\n   return 97;\\n\\n```\\n\\n\"This is ugly, you have to use a local variable!\"\\n```\\nint result;\\nif (condition)\\n   result = 42;\\nelse\\n   result = 97;\\nreturn result;\\n\\n```\\n\\nHow does this 50% code bloat make the program any easier to understand? Personally, I find it harder, because the state space has just increased by another variable that could easily have been prevented.\\nOf course, normally I would just write:\\n```\\nreturn (condition) ? 42 : 97;\\n\\n```\\n\\nBut many programmers eschew the conditional operator and prefer the long form.\\nWhere did this notion of \"one return only\" come from? Is there a historical reason why this convention came about?\\n  \"Single Entry, Single Exit\" was written when most programming was done in assembly language, FORTRAN, or COBOL.  It has been widely misinterpreted, because modern languages do not support the practices <PERSON><PERSON><PERSON> was warning against.\\n\"Single Entry\" meant \"do not create alternate entry points for functions\".  In assembly language, of course, it is possible to enter a function at any instruction.  FORTRAN supported multiple entries to functions with the ```\\nENTRY\\n```\\n statement:\\n```\\n      SUBROUTINE S(X, Y)\\n      R = SQRT(X*X + Y*Y)\\nC ALTERNATE ENTRY USED WHEN R IS ALREADY KNOWN\\n      ENTRY S2(R)\\n      ...\\n      RETURN\\n      END\\n\\nC USAGE\\n      CALL S(3,4)\\nC ALTERNATE USAGE\\n      CALL S2(5)\\n\\n```\\n\\n\"Single Exit\" meant that a function should only return to one place: the statement immediately following the call.  It did not mean that a function should only return from one place.  When Structured Programming was written, it was common practice for a function to indicate an error by returning to an alternate location.  FORTRAN supported this via \"alternate return\":\\n```\\nC SUBROUTINE WITH ALTERNATE RETURN.  THE '*' IS A PLACE HOLDER FOR THE ERROR RETURN\\n      SUBROUTINE QSOLVE(A, B, C, X1, X2, *)\\n      DISCR = B*B - 4*A*C\\nC NO SOLUTIONS, RETURN TO ERROR HANDLING LOCATION\\n      IF DISCR .LT. 0 RETURN 1\\n      SD = SQRT(DISCR)\\n      DENOM = 2*A\\n      X1 = (-B + SD) / DENOM\\n      X2 = (-B - SD) / DENOM\\n      RETURN\\n      END\\n\\nC USE OF ALTERNATE RETURN\\n      CALL QSOLVE(1, 0, 1, X1, X2, *99)\\nC SOLUTION FOUND\\n      ...\\nC QSOLVE RETURNS HERE IF NO SOLUTIONS\\n99    PRINT 'NO SOLUTIONS'\\n\\n```\\n\\nBoth these techniques were highly error prone.  Use of alternate entries often left some variable uninitialized.  Use of alternate returns had all the problems of a GOTO statement, with the additional complication that the branch condition was not adjacent to the branch, but somewhere in the subroutine.\\nThanks to  for finding the original paper. See , page 28 (printed page number is 24). Not limited to functions.\\n  Ich spreche oft mit Programmierern, die sagen: \"Man sollte nicht mehrere Rückgabeanweisungen in dieselbe Methode einfügen.\" Wenn ich sie bitte, mir die Gründe dafür zu nennen, ist alles, was ich bekomme, \"Der Kodierungsstandard sagt das\" oder \"Es ist verwirrend\". Wenn sie mir Lösungen mit einer einzigen Rückgabeanweisung zeigen, sieht der Code für mich noch hässlicher aus. Zum Beispiel:\\n```\\nif (Bedingung)\\n   return 42;\\nsonst\\n   97 zurückgeben;\\n\\n```\\n\\n\"Das ist hässlich, du musst eine lokale Variable verwenden!\"\\n```\\nint Ergebnis;\\nif (Bedingung)\\n   Ergebnis = 42;\\nsonst\\n   Ergebnis = 97;\\nErgebnis zurückgeben;\\n\\n```\\n\\nInwiefern macht diese 50%ige Codeaufblähung das Programm leichter verständlich? Ich persönlich finde es schwieriger, denn der Zustandsraum hat sich gerade um eine weitere Variable vergrößert, was leicht hätte verhindert werden können.\\nNatürlich würde ich normalerweise einfach schreiben:\\n```\\nreturn (Bedingung) ? 42 : 97;\\n\\n```\\n\\nAber viele Programmierer meiden den bedingten Operator und bevorzugen die lange Form.\\nWoher kommt diese Vorstellung von \"nur einer Rückgabe\"? Gibt es einen historischen Grund, warum diese Konvention entstanden ist?\\n  \"Single Entry, Single Exit\" wurde geschrieben, als die meisten Programmierungen in Assembler, FORTRAN oder COBOL erfolgten.  Es ist weithin falsch interpretiert worden, weil moderne Sprachen die Praktiken, vor denen Dijkstra warnte, nicht unterstützen.\\n\"Single Entry\" bedeutete \"keine alternativen Einstiegspunkte für Funktionen schaffen\".  In Assembler ist es natürlich möglich, eine Funktion an jeder beliebigen Anweisung einzugeben.  FORTRAN unterstützte mehrere Eingänge zu Funktionen mit dem ```\\nENTRY\\n```\\n Anweisung:\\n```\\n      SUBROUTINE S(X, Y)\\n      R = SQRT(X*X + Y*Y)\\nC ALTERNATIVER EINTRAG, WENN R BEREITS BEKANNT IST\\n      EINTRAG S2(R)\\n      ...\\n      RETURN\\n      END\\n\\nC ANWENDUNG\\n      CALL S(3,4)\\nC ALTERNATIVE VERWENDUNG\\n      CALL S2(5)\\n\\n```\\n\\n\"Single Exit\" bedeutete, dass eine Funktion nur an eine Stelle zurückkehren sollte: die Anweisung, die unmittelbar auf den Aufruf folgt.  Es bedeutete nicht, dass eine Funktion nur von einer Stelle zurückkehren sollte.  Als die strukturierte Programmierung geschrieben wurde, war es üblich, dass eine Funktion einen Fehler anzeigt, indem sie an eine andere Stelle zurückkehrt.  FORTRAN unterstützte dies durch \"alternate return\":\\n```\\nC SUBROUTINE MIT ALTERNATIVER RÜCKKEHR.  DER '*' IST EIN PLATZHALTER FÜR DIE FEHLERRÜCKGABE\\n      SUBROUTINE QSOLVE(A, B, C, X1, X2, *)\\n      DISCR = B*B - 4*A*C\\nC KEINE LÖSUNGEN, RÜCKKEHR ZUR FEHLERBEHANDLUNGSSTELLE\\n      IF DISCR .LT. 0 RETURN 1\\n      SD = SQRT(DISCR)\\n      DENOM = 2*A\\n      X1 = (-B + SD) / NENNWERT\\n      X2 = (-B - SD) / NENNWERT\\n      RETURN\\n      END\\n\\nC VERWENDUNG DER ALTERNATIVEN RÜCKGABE\\n      AUFRUF QSOLVE(1, 0, 1, X1, X2, *99)\\nC LÖSUNG GEFUNDEN\\n      ...\\nC QSOLVE KEHRT HIER ZURÜCK, WENN KEINE LÖSUNGEN\\n99 PRINT 'KEINE LÖSUNGEN'\\n\\n```\\n\\nDiese beiden Techniken waren sehr fehleranfällig.  Die Verwendung von alternativen Eingaben ließ oft eine Variable uninitialisiert.  Die Verwendung von alternativen Rücksprüngen hatte alle Probleme einer GOTO-Anweisung, mit der zusätzlichen Komplikation, dass die Verzweigungsbedingung nicht neben der Verzweigung, sondern irgendwo im Unterprogramm lag.\\nVielen Dank an für das Auffinden des Originalpapiers. Siehe , Seite 28 (gedruckte Seitenzahl ist 24). Nicht auf Funktionen beschränkt.\\n  A menudo hablo con programadores que me dicen: \"No pongas varias sentencias return en el mismo método\". Cuando les pido que me digan las razones, todo lo que obtengo es \"La norma de codificación lo dice\" o \"Es confuso\". Cuando me muestran soluciones con una única sentencia return, el código me parece más feo. Por ejemplo:\\n```\\nif (condición)\\n   return 42;\\nelse\\n   devuelve 97;\\n\\n```\\n\\n\"¡Esto es feo, tienes que usar una variable local!\"\\n```\\nint resultado;\\nsi (condición)\\n   resultado = 42;\\nsi no\\n   resultado = 97\\ndevolver resultado;\\n\\n```\\n\\n¿Cómo hace este 50% de código hinchado que el programa sea más fácil de entender? Personalmente, lo encuentro más difícil, porque el espacio de estados acaba de aumentar en otra variable que podría haberse evitado fácilmente.\\nPor supuesto, normalmente escribiría:\\n```\\nreturn (condición) ? 42 : 97;\\n\\n```\\n\\nPero muchos programadores evitan el operador condicional y prefieren la forma larga.\\n¿De dónde viene esta noción de \"un solo retorno\"? ¿Existe alguna razón histórica que explique esta convención?\\n  \"Single Entry, Single Exit\" se escribió cuando la mayor parte de la programación se hacía en lenguaje ensamblador, FORTRAN o COBOL.  Se ha malinterpretado mucho, porque los lenguajes modernos no admiten las prácticas contra las que advertía Dijkstra.\\n\"Entrada única\" significaba \"no crear puntos de entrada alternativos para las funciones\".  En lenguaje ensamblador, por supuesto, es posible entrar en una función en cualquier instrucción.  FORTRAN permitía múltiples entradas a funciones con la instrucción ```\\nENTRY\\n```\\n con la sentencia\\n```\\n      SUBRUTINA S(X, Y)\\n      R = SQRT(X*X + Y*Y)\\nC ENTRADA ALTERNATIVA UTILIZADA CUANDO R YA SE CONOCE\\n      ENTRADA S2(R)\\n      ...\\n      RETURN\\n      FIN\\n\\nC USO\\n      CALL S(3,4)\\nC USO ALTERNATIVO\\n      CALL S2(5)\\n\\n```\\n\\n\"Single Exit\" significaba que una función sólo debía volver a un lugar: la sentencia inmediatamente posterior a la llamada.  No significaba que una función sólo debía retornar desde un lugar.  Cuando se escribió la Programación Estructurada, era práctica común que una función indicara un error retornando a un lugar alternativo.  FORTRAN soportaba esto a través del \"retorno alternativo\":\\n```\\nC SUBRUTINA CON RETORNO ALTERNATIVO.  EL '*' ES UN MARCADOR DE POSICIÓN PARA EL RETORNO DE ERROR\\n      SUBRUTINA QSOLVE(A, B, C, X1, X2, *)\\n      DISCR = B*B - 4*A*C\\nC NO HAY SOLUCIONES, VUELVE AL LUGAR DE TRATAMIENTO DE ERRORES\\n      IF DISCR .LT. 0 RETURN 1\\n      SD = SQRT(DISCR)\\n      DENOM = 2*A\\n      X1 = (-B + SD) / DENOM\\n      X2 = (-B - SD) / DENOM\\n      RETURN\\n      FIN\\n\\nC USO DE RETORNO ALTERNATIVO\\n      CALL QSOLVE(1, 0, 1, X1, X2, *99)\\nC SOLUCIÓN ENCONTRADA\\n      ...\\nC QSOLVE DEVUELVE AQUÍ SI NO HAY SOLUCIONES\\n99 PRINT 'NO HAY SOLUCIONES'\\n\\n```\\n\\nAmbas técnicas eran muy propensas a errores.  El uso de entradas alternativas a menudo dejaba alguna variable sin inicializar.  El uso de retornos alternativos tenía todos los problemas de una sentencia GOTO, con la complicación adicional de que la condición de bifurcación no estaba adyacente a la bifurcación, sino en algún lugar de la subrutina.\\nGracias a por encontrar el artículo original. Ver , página 28 (la página impresa es la 24). No se limita a las funciones.\\n  Je discute souvent avec des programmeurs qui me disent : \"Ne mettez pas plusieurs déclarations de retour dans la même méthode\". Lorsque je leur demande de m'expliquer pourquoi, tout ce que j'obtiens, c'est \"La norme de codage le dit\" ou \"C'est déroutant\". Lorsqu'ils me montrent des solutions avec une seule instruction de retour, le code me semble plus laid. Par exemple :\\n```\\nif (condition)\\n   return 42 ;\\nelse\\n   renvoie 97 ;\\n\\n```\\n\\n\"C'est moche, il faut utiliser une variable locale !\"\\n```\\nint result ;\\nif (condition)\\n   result = 42 ;\\nelse\\n   result = 97 ;\\nretour du résultat ;\\n\\n```\\n\\nEn quoi ces 50 % de code en trop rendent-ils le programme plus facile à comprendre ? Personnellement, je trouve que c'est plus difficile, parce que l'espace d'état vient d'augmenter d'une autre variable qui aurait pu facilement être évitée.\\nBien sûr, en temps normal, j'écrirais simplement :\\n```\\nreturn (condition) ? 42 : 97 ;\\n\\n```\\n\\nMais de nombreux programmeurs évitent l'opérateur conditionnel et préfèrent la forme longue.\\nD'où vient cette notion de \"un seul retour\" ? Y a-t-il une raison historique à cette convention ?\\n  \"Single Entry, Single Exit\" a été écrit à l'époque où la plupart des programmes étaient réalisés en langage d'assemblage, en FORTRAN ou en COBOL.  Il a été largement mal interprété, car les langages modernes ne prennent pas en charge les pratiques contre lesquelles Dijkstra mettait en garde.\\nL'expression \"entrée unique\" signifiait \"ne pas créer de points d'entrée alternatifs pour les fonctions\".  En langage assembleur, bien sûr, il est possible d'entrer dans une fonction à n'importe quelle instruction.  FORTRAN supporte les entrées multiples dans les fonctions avec la fonction ```\\nENTRY\\n```\\n avec l'instruction ``EnTRY ``'' :\\n```\\n      SOUS-PROGRAMME S(X, Y)\\n      R = SQRT(X*X + Y*Y)\\nC ENTRÉE ALTERNATIVE UTILISÉE LORSQUE R EST DÉJÀ CONNU\\n      ENTRÉE S2(R)\\n      ...\\n      RETURN\\n      FIN\\n\\nC USAGE\\n      APPEL S(3,4)\\nC UTILISATION ALTERNATIVE\\n      APPEL S2(5)\\n\\n```\\n\\nL'expression \"sortie unique\" signifiait qu'une fonction ne devait retourner qu'à un seul endroit : la déclaration suivant immédiatement l'appel.  Cela ne signifiait pas qu'une fonction ne devait retourner qu'à un seul endroit.  Lorsque la programmation structurée a été écrite, il était courant qu'une fonction indique une erreur en retournant à un autre endroit.  FORTRAN prenait en charge cette pratique par le biais d'un \"retour alternatif\" :\\n```\\nC SOUS-ROUTINE AVEC RETOUR ALTERNATIF.  LE '*' EST UN SUPPORT POUR LE RETOUR D'ERREUR\\n      SUBROUTINE QSOLVE(A, B, C, X1, X2, *)\\n      DISCR = B*B - 4*A*C\\nC PAS DE SOLUTION, RETOUR AU POINT DE TRAITEMENT DES ERREURS\\n      IF DISCR .LT. 0 RETOURNE 1\\n      SD = SQRT(DISCR)\\n      DENOM = 2*A\\n      X1 = (-B + SD) / DENOM\\n      X2 = (-B - SD) / DENOM\\n      RETURN\\n      FIN\\n\\nC UTILISATION DU RETOUR ALTERNATIF\\n      CALL QSOLVE(1, 0, 1, X1, X2, *99)\\nC SOLUTION TROUVÉE\\n      ...\\nC QSOLVE RETOURNE ICI SI AUCUNE SOLUTION N'EST TROUVÉE\\n99 PRINT 'NO SOLUTIONS'\\n\\n```\\n\\nCes deux techniques étaient très sujettes aux erreurs.  L'utilisation d'entrées alternatives laissait souvent une variable non initialisée.  L'utilisation de retours alternatifs présentait tous les problèmes d'une instruction GOTO, avec la complication supplémentaire que la condition de branchement n'était pas adjacente à la branche, mais quelque part dans le sous-programme.\\nMerci d'avoir trouvé l'article original. Voir , page 28 (le numéro de la page imprimée est 24). Ne se limite pas aux fonctions.\\n  Mi capita spesso di parlare con programmatori che dicono: \"Non inserire più dichiarazioni di ritorno nello stesso metodo\". Quando chiedo loro di spiegarmi il perché, tutto ciò che ottengo è: \"Lo dice lo standard di codifica\" o \"Crea confusione\". Quando mi mostrano soluzioni con una sola dichiarazione di ritorno, il codice mi sembra più brutto. Per esempio:\\n```\\nse (condizione)\\n   ritorno 42;\\naltrimenti\\n   restituisce 97;\\n\\n```\\n\\n\"Questo è brutto, devi usare una variabile locale!\".\\n```\\nint risultato;\\nse (condizione)\\n   risultato = 42;\\naltrimenti\\n   risultato = 97;\\nrestituire il risultato;\\n\\n```\\n\\nIn che modo questo 50% di codice gonfiato rende il programma più facile da capire? Personalmente, lo trovo più difficile, perché lo spazio di stato è appena aumentato di un'altra variabile che poteva essere facilmente evitata.\\nNaturalmente, normalmente scriverei semplicemente:\\n```\\nreturn (condition) ? 42 : 97;\\n\\n```\\n\\nMa molti programmatori evitano l'operatore condizionale e preferiscono la forma lunga.\\nDa dove viene questa nozione di \"un solo ritorno\"? C'è una ragione storica per cui è nata questa convenzione?\\n  \"Single Entry, Single Exit\" è stato scritto quando la maggior parte della programmazione era realizzata in linguaggio assembly, FORTRAN o COBOL.  È stato ampiamente frainteso, perché i linguaggi moderni non supportano le pratiche contro cui Dijkstra metteva in guardia.\\n\"Single Entry\" significava \"non creare punti di ingresso alternativi per le funzioni\".  Nel linguaggio assembly, ovviamente, è possibile accedere a una funzione in qualsiasi istruzione.  Il FORTRAN supportava l'ingresso multiplo alle funzioni con l'opzione ```\\nENTRATA\\n```\\n con l'istruzione `` ENTRY'':\\n```\\n      SUBROUTINE S(X, Y)\\n      R = SQRT(X*X + Y*Y)\\nC VOCE ALTERNATIVA UTILIZZATA QUANDO R È GIÀ NOTO\\n      VOCE S2(R)\\n      ...\\n      RITORNO\\n      FINE\\n\\nC USO\\n      CHIAMATA S(3,4)\\nUSO ALTERNATIVO DI C\\n      CHIAMARE S2(5)\\n\\n```\\n\\n\"Single Exit\" significava che una funzione doveva tornare in un solo posto: l'istruzione immediatamente successiva alla chiamata.  Non significava che una funzione dovesse tornare da un solo punto.  Quando fu scritta la programmazione strutturata, era pratica comune che una funzione indicasse un errore ritornando in un punto alternativo.  FORTRAN supportava questa possibilità tramite il \"ritorno alternativo\":\\n```\\nSUBROUTINE C CON RITORNO ALTERNATIVO.  IL SIMBOLO '*' È UN SEGNAPOSTO PER IL RITORNO ALL'ERRORE.\\n      SUBROUTINE QSOLVE(A, B, C, X1, X2, *)\\n      DISCR = B*B - 4*A*C\\nC NESSUNA SOLUZIONE, RITORNO ALLA POSIZIONE DI GESTIONE DEGLI ERRORI\\n      SE DISCR .LT. 0 RITORNO 1\\n      SD = SQRT(DISCR)\\n      DENOM = 2*A\\n      X1 = (-B + SD) / DENOM\\n      X2 = (-B - SD) / DENOM\\n      RITORNO\\n      FINE\\n\\nC USO DEL RITORNO ALTERNATIVO\\n      CHIAMATA QSOLVE(1, 0, 1, X1, X2, *99)\\nC SOLUZIONE TROVATA\\n      ...\\nC QSOLVE RITORNA QUI SE NON CI SONO SOLUZIONI\\n99 PRINT 'NESSUNA SOLUZIONE'\\n\\n```\\n\\nEntrambe queste tecniche erano altamente soggette a errori.  L'uso di inserimenti alternativi lasciava spesso qualche variabile non inizializzata.  L'uso di ritorni alternativi presentava tutti i problemi di una dichiarazione GOTO, con l'ulteriore complicazione che la condizione di diramazione non era adiacente alla diramazione, ma da qualche parte nella subroutine.\\nGrazie per aver trovato il documento originale. Vedere , pagina 28 (il numero di pagina stampato è 24). Non limitato alle funzioni.\\n\n", "2           2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  What software programming languages were used by the Soviet Union's space program? I was interested to discover that the software on the  circa 1988 was written in Prolog.  \\nDo you know what languages might have been used in earlier missions, especially the  of the early 1970s which were somewhat autonomous and could navigate obstacles?                                                 There's a book in Russian, German Noskin, First computers (literally board digital computing machines) for space applications (Герман Но<PERSON>кин, Первые БЦВМ космического применения), ISBN 978-5-91918-093-7. \\nThe author himself participated in many early projects (mostly in hardware) and according to him analog hardware was in favor for a long time, he mentions that space rendezvous tasks didn't use digital computers until the late 70's. Due to this policy many digital computers were really proofs of concept although used in other areas of soviet economics. The first computer according to him used on-board was the Argon-11S (Аргон-11С) on the unmanned missions to the Moon closer to Apollo-8 in time. Also <PERSON><PERSON> briefly says that the on-board computer Salut-4 was compatible with general-purpose computers ES used in Soviet economics so it was possible to develop software in PL-1 and Fortran. \\nThere are several mentions of Buran program languages on Russian websites. According to <PERSON>, an engineer from the program () three languages using Russian as a base were developed: PROL2 (ПРОЛ2) for onboard programs, <PERSON><PERSON> (Диполь) for earth tests, and Laks (Лакс) for modelling. All of them were intended for use not only by professional programmers but also engineers from other areas. \\nWhen the Buran program was closed they were merged into a new language  (Дракон, Russian word for \"Dragon\") that is claimed to be be a \"graphical\" language having 2-dimensional descriptions of the programs and using arbitrary well-known languages for code generation. This language was also intended for use by non-programmers. The language probably does not have and international community and isn't even well-known within Russia although heavily promoted by its author, Vladimir Parondjanov (the Russian  article is very long and was even deleted once for not following Wikipedia rules). Drakon was first used for programming for the  missions and has been used in other Russian space programs since.\\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Welche Software-Programmiersprachen wurden im Raumfahrtprogramm der Sowjetunion verwendet? Ich fand es interessant zu entdecken, dass die Software auf der circa 1988 in Prolog geschrieben wurde.  \\nWissen Sie, welche Sprachen bei früheren Missionen verwendet wurden, insbesondere bei den Missionen der frühen 1970er Jahre, die einigermaßen autonom waren und Hindernisse überwinden konnten?                                          Es gibt ein Buch in russischer Sprache, German Noskin, Erste Computer (wörtlich: Board Digital Computing Machines) für Weltraumanwendungen (Герман Носкин, Первые БЦВМ космического применения), ISBN 978-5-91918-093-7.\\nDer Autor war selbst an vielen frühen Projekten beteiligt (vor allem im Bereich der Hardware), und ihm zufolge wurde lange Zeit analoge Hardware bevorzugt; er erwähnt, dass bei Weltraum-Rendezvous-Aufgaben bis Ende der 70er Jahre keine Digitalcomputer verwendet wurden. Aufgrund dieser Politik waren viele Digitalcomputer wirklich Proofs of Concept, obwohl sie in anderen Bereichen der sowjetischen Wirtschaft eingesetzt wurden. Der erste Computer, der ihm zufolge an Bord eingesetzt wurde, war der Argon-11S (Аргон-11С) bei den unbemannten Missionen zum Mond, die zeitlich näher an Apollo-8 lagen. Noskin erwähnt auch kurz, dass der Bordcomputer Salut-4 mit den in der sowjetischen Wirtschaft verwendeten Allzweckcomputern ES kompatibel war, so dass es möglich war, Software in PL-1 und Fortran zu entwickeln.\\nAuf russischen Websites werden mehrere Buran-Programmsprachen erwähnt. Nach Angaben von Wladimir Parondjanow, einem Ingenieur des Programms () wurden drei Sprachen auf der Grundlage von Russisch entwickelt: PROL2 (ПРОЛ2) für Bordprogramme, Dipol (Диполь) für Erdtests und Laks (Лакс) für die Modellierung. Alle diese Programme waren nicht nur für professionelle Programmierer, sondern auch für Ingenieure aus anderen Bereichen bestimmt.\\nAls das Buran-Programm eingestellt wurde, wurden sie in einer neuen Sprache (Дракон, russisches Wort für \"Drache\") zusammengeführt, die angeblich eine \"grafische\" Sprache mit zweidimensionalen Programmbeschreibungen ist und beliebige bekannte Sprachen zur Codegenerierung verwendet. Diese Sprache sollte auch von Nicht-Programmierern verwendet werden. Die Sprache hat wahrscheinlich keine internationale Gemeinschaft und ist nicht einmal in Russland bekannt, obwohl sie von ihrem Autor, Vladimir Parondjanov, stark beworben wird (der russische Artikel ist sehr lang und wurde sogar einmal wegen Nichteinhaltung der Wikipedia-Regeln gelöscht). Drakon wurde zuerst für die Programmierung der Missionen verwendet und ist seitdem in anderen russischen Raumfahrtprogrammen eingesetzt worden.\\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ¿Qué lenguajes de programación utilizaba el programa espacial de la Unión Soviética? Me interesó descubrir que el software del circa 1988 estaba escrito en Prolog.  \\n¿Sabe qué lenguajes se utilizaron en misiones anteriores, sobre todo en las de principios de los años setenta, que eran algo autónomas y podían sortear obstáculos?                             Hay un libro en ruso, German Noskin, First computers (literalmente máquinas de computación digital de a bordo) for space applications (Герман Носкин, Первые БЦВМ космического применения), ISBN 978-5-91918-093-7.\\nEl propio autor participó en muchos de los primeros proyectos (la mayoría en hardware) y según él el hardware analógico estuvo a favor durante mucho tiempo, menciona que las tareas de encuentro espacial no utilizaron ordenadores digitales hasta finales de los 70. Debido a esta política, muchos ordenadores digitales fueron en realidad pruebas de concepto, aunque se utilizaron en otras áreas de la economía soviética. Según él, el primer ordenador utilizado a bordo fue el Argon-11S (Аргон-11С) en las misiones no tripuladas a la Luna más cercanas en el tiempo al Apolo-8. También Noskin dice brevemente que el ordenador de a bordo Salut-4 era compatible con los ordenadores de propósito general ES utilizados en la economía soviética por lo que era posible desarrollar software en PL-1 y Fortran.\\nHay varias menciones a lenguajes de programación Buran en sitios web rusos. Según Vladimir Parondjanov, ingeniero del programa () se desarrollaron tres lenguajes que utilizaban el ruso como base: PROL2 (ПРОЛ2) para programas de a bordo, Dipol (Диполь) para pruebas terrestres y Laks (Лакс) para modelado. Todos ellos estaban destinados a ser utilizados no sólo por programadores profesionales, sino también por ingenieros de otras áreas.\\nCuando se cerró el programa Buran, se fusionaron en un nuevo lenguaje (Дракон, palabra rusa que significa \"Dragón\") que, según se afirma, es un lenguaje \"gráfico\" con descripciones bidimensionales de los programas y que utiliza lenguajes conocidos arbitrarios para la generación de código. Este lenguaje también estaba pensado para ser utilizado por no programadores. El lenguaje probablemente no tiene una comunidad internacional y ni siquiera es conocido en Rusia, aunque su autor, Vladimir Parondjanov, lo promociona mucho (el artículo en ruso es muy largo e incluso fue borrado una vez por no seguir las normas de Wikipedia). Drakon se utilizó por primera vez para programar las misiones y desde entonces se ha empleado en otros programas espaciales rusos.\\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          Quels sont les langages de programmation utilisés par le programme spatial de l'Union soviétique ? J'ai découvert avec intérêt que le logiciel du circa 1988 était écrit en Prolog.  \\nSavez-vous quels langages ont pu être utilisés pour les missions antérieures, en particulier celles du début des années 1970, qui étaient quelque peu autonomes et pouvaient franchir des obstacles ?                                   Il existe un livre en russe, German Noskin, First computers (literally board digital computing machines) for space applications (Герман Носкин, Первые БЦВМ космического применения), ISBN 978-5-91918-093-7.\\nL'auteur a lui-même participé à de nombreux projets précoces (principalement dans le domaine du matériel) et, selon lui, le matériel analogique a été en faveur pendant longtemps. Il mentionne que les tâches de rendez-vous dans l'espace n'ont pas utilisé d'ordinateurs numériques jusqu'à la fin des années 70. En raison de cette politique, de nombreux ordinateurs numériques étaient en réalité des preuves de concept, bien qu'ils aient été utilisés dans d'autres domaines de l'économie soviétique. Selon lui, le premier ordinateur utilisé à bord a été l'Argon-11S (Аргон-11С) lors des missions non habitées sur la Lune, plus proches d'Apollo-8 dans le temps. Par ailleurs, Noskin indique brièvement que l'ordinateur de bord de Salut-4 était compatible avec les ordinateurs à usage général utilisés dans l'économie soviétique, de sorte qu'il était possible de développer des logiciels en PL-1 et en Fortran.\\nLes sites web russes mentionnent à plusieurs reprises les langages de programmation de Buran. Selon Vladimir Parondjanov, un ingénieur du programme (), trois langages utilisant le russe comme base ont été développés : PROL2 (ПРОЛ2) pour les programmes embarqués, Dipol (Диполь) pour les tests terrestres, et Laks (Лакс) pour la modélisation. Tous ces programmes étaient destinés à être utilisés non seulement par des programmeurs professionnels, mais aussi par des ingénieurs d'autres domaines.\\nLorsque le programme Buran a été fermé, ils ont été fusionnés dans un nouveau langage (Дракон, mot russe pour \"Dragon\") qui est censé être un langage \"graphique\" ayant des descriptions bidimensionnelles des programmes et utilisant des langages arbitraires bien connus pour la génération de code. Ce langage était également destiné à être utilisé par des non-programmeurs. Le langage n'a probablement pas de communauté internationale et n'est même pas connu en Russie, bien qu'il soit fortement promu par son auteur, Vladimir Parondjanov (l'article russe est très long et a même été supprimé une fois pour ne pas avoir respecté les règles de Wikipédia). Drakon a été utilisé pour la première fois pour la programmation des missions et a été utilisé dans d'autres programmes spatiaux russes depuis.\\n                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                Quali linguaggi di programmazione sono stati utilizzati dal programma spaziale dell'Unione Sovietica? Mi ha interessato scoprire che il software del 1988 circa era scritto in Prolog.  \\nSapete quali linguaggi potevano essere utilizzati nelle missioni precedenti, in particolare in quelle dei primi anni '70, che erano in qualche modo autonome e potevano superare gli ostacoli?                                                          C'è un libro in russo, German Noskin, First computers (letteralmente macchine di calcolo digitale a bordo) per applicazioni spaziali (Герман Носкин, Первые БЦВМ космического применения), ISBN 978-5-91918-093-7.\\nL'autore stesso ha partecipato a molti progetti iniziali (per lo più in ambito hardware) e secondo lui l'hardware analogico è stato in auge per molto tempo, menzionando il fatto che i compiti di rendez-vous spaziale non hanno utilizzato computer digitali fino alla fine degli anni '70. A causa di questa politica, molti computer digitali erano in realtà delle prove di concetto, anche se utilizzati in altri settori dell'economia sovietica. Secondo Noskin, il primo computer utilizzato a bordo fu l'Argon-11S (Аргон-11С) nelle missioni senza equipaggio verso la Luna, più vicine al tempo dell'Apollo-8. Inoltre, Noskin afferma brevemente che il computer di bordo Salut-4 era compatibile con i computer general-purpose ES utilizzati nell'economia sovietica, per cui era possibile sviluppare software in PL-1 e Fortran.\\nSui siti web russi si trovano diverse menzioni dei linguaggi di programma Buran. Secondo Vladimir Parondjanov, un ingegnere del programma () sono stati sviluppati tre linguaggi che utilizzano il russo come base: PROL2 (ПРОЛ2) per i programmi di bordo, Dipol (Диполь) per i test a terra e Laks (Лакс) per la modellazione. Tutti questi programmi erano destinati all'uso non solo di programmatori professionisti, ma anche di ingegneri di altri settori.\\nQuando il programma Buran è stato chiuso, sono stati fusi in un nuovo linguaggio (Дракон, parola russa per \"Drago\") che si afferma essere un linguaggio \"grafico\" con descrizioni bidimensionali dei programmi e che utilizza linguaggi arbitrari noti per la generazione del codice. Questo linguaggio è stato pensato per essere utilizzato anche da non programmatori. Il linguaggio probabilmente non ha una comunità internazionale e non è nemmeno conosciuto in Russia, sebbene sia stato fortemente promosso dal suo autore, Vladimir Parondjanov (l'articolo in russo è molto lungo ed è stato persino cancellato una volta per non aver rispettato le regole di Wikipedia). Drakon è stato utilizzato per la prima volta per la programmazione delle missioni e da allora è stato utilizzato in altri programmi spaziali russi.\\n\n", "\n", "Processing complete.\n"]}], "source": ["# -----------------------------------------------------------------------------\n", "# 1. Import necessary libraries\n", "# -----------------------------------------------------------------------------\n", "from datasets import load_dataset, Dataset, DatasetDict\n", "import re # For parsing the ID strings\n", "import pandas as pd # Optional: for displaying samples as DataFrames\n", "\n", "print(\"Libraries imported.\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 2. Configuration\n", "# -----------------------------------------------------------------------------\n", "dataset_name = \"lamarr-org/Lima-X\"\n", "# Target languages as specified in the request\n", "target_languages_upper = [\"EN\", \"DE\", \"ES\", \"FR\", \"IT\"]\n", "\n", "print(f\"Configuration set for dataset: {dataset_name}\")\n", "print(f\"Target languages: {', '.join(target_languages_upper)}\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 3. Initialize data aggregators\n", "# These dictionaries will hold the combined data, keyed by base_id_numeric.\n", "# One dictionary for each target split in the new dataset.\n", "# -----------------------------------------------------------------------------\n", "aggregated_data_for_new_train_split = {}\n", "aggregated_data_for_new_val_split = {}\n", "\n", "print(\"Data aggregators initialized.\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 4. Load data and process conversations\n", "# -----------------------------------------------------------------------------\n", "print(f\"\\nStarting processing for dataset: {dataset_name}\")\n", "\n", "for lang_code_upper in target_languages_upper:\n", "    print(f\"\\nProcessing language: {lang_code_upper}\")\n", "    try:\n", "        # Load the specific language subset (configuration)\n", "        # trust_remote_code=True is often needed for datasets with custom loading scripts.\n", "        lang_dataset = load_dataset(dataset_name, name=lang_code_upper, trust_remote_code=True)\n", "    except Exception as e:\n", "        print(f\"  Could not load subset {lang_code_upper}. Error: {e}\")\n", "        print(f\"  Skipping language {lang_code_upper}.\")\n", "        continue\n", "\n", "    # Iterate over available splits for this language configuration (e.g., 'train', 'val')\n", "    for original_split_name in lang_dataset.keys():\n", "        print(f\"  Processing original split: {original_split_name} for language {lang_code_upper}\")\n", "\n", "        for example_count, example in enumerate(lang_dataset[original_split_name]):\n", "            raw_id = example['id']\n", "            conversations = example['conversations']\n", "\n", "            # Parse the ID string (e.g., \"train_id_0_DE\", \"val_id_0_EN\")\n", "            # Regex captures: (original_split_prefix, numeric_id, language_suffix)\n", "            match = re.match(r'(\\w+)_id_(\\d+)_(\\w+)', raw_id)\n", "            if not match:\n", "                print(f\"    Skipping malformed ID: {raw_id} in {lang_code_upper}/{original_split_name}\")\n", "                continue\n", "\n", "            id_original_split_prefix, base_id_str, lang_from_id = match.groups()\n", "            base_id_numeric = int(base_id_str)\n", "\n", "            # Sanity check for language code from ID\n", "            if lang_from_id.upper() != lang_code_upper:\n", "                print(f\"    Warning: Mismatch detected for ID {raw_id}. Expected lang {lang_code_upper}, found {lang_from_id} in ID.\")\n", "                # We proceed using lang_code_upper for column naming as we are in its processing loop.\n", "\n", "            # Extract human and gpt values from conversations\n", "            human_value = None\n", "            gpt_value = None\n", "\n", "            if isinstance(conversations, list) and len(conversations) >= 2:\n", "                # Try to robustly find 'human' and 'gpt' turns\n", "                convo_map = {}\n", "                for turn in conversations:\n", "                    if isinstance(turn, dict) and 'from' in turn and 'value' in turn:\n", "                        # Take the first occurrence if multiple gpt/human turns exist (though not expected for Lima)\n", "                        if turn['from'] == 'human' and 'human' not in convo_map:\n", "                            convo_map['human'] = turn['value']\n", "                        elif turn['from'] == 'gpt' and 'gpt' not in convo_map:\n", "                            convo_map['gpt'] = turn['value']\n", "\n", "                human_value = convo_map.get('human')\n", "                gpt_value = convo_map.get('gpt')\n", "\n", "                if human_value is None or gpt_value is None:\n", "                    print(f\"    Skipping ID {raw_id} in {lang_code_upper}/{original_split_name}, 'human' or 'gpt' turn missing or malformed in conversations.\")\n", "                    continue\n", "            else:\n", "                print(f\"    Skipping ID {raw_id} in {lang_code_upper}/{original_split_name}, conversations format is not as expected.\")\n", "                continue\n", "\n", "            # Determine which aggregator to use based on the original split prefix\n", "            target_aggregator = None\n", "            normalized_original_split_prefix = id_original_split_prefix.lower()\n", "\n", "            if normalized_original_split_prefix == 'train':\n", "                target_aggregator = aggregated_data_for_new_train_split\n", "            elif normalized_original_split_prefix == 'val':\n", "                target_aggregator = aggregated_data_for_new_val_split\n", "            else:\n", "                print(f\"    Skipping ID {raw_id} due to unrecognized original split prefix: {id_original_split_prefix}. Data won't be added to new train or val splits.\")\n", "                continue\n", "\n", "            # Initialize the entry for this base_id_numeric if it's the first time we see it for this target split\n", "            if base_id_numeric not in target_aggregator:\n", "                target_aggregator[base_id_numeric] = {\n", "                    'id_numeric': base_id_numeric,\n", "                    # 'original_split_for_id': id_original_split_prefix # Optional: If you want to keep this information explicitly\n", "                }\n", "\n", "            # Add the input and output for the current language\n", "            lang_code_lower = lang_code_upper.lower()\n", "            target_aggregator[base_id_numeric][f'{lang_code_lower}_input'] = human_value\n", "            target_aggregator[base_id_numeric][f'{lang_code_lower}_output'] = gpt_value\n", "\n", "print(\"\\nFinished loading and initial processing of all specified languages and original splits.\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 5. Define final column names for the new dataset structure\n", "# -----------------------------------------------------------------------------\n", "final_column_names = ['id_numeric'] # Start with the common ID column\n", "# Add columns for each language's input and output\n", "for lang_code_upper_col in target_languages_upper:\n", "    lang_code_lower_col = lang_code_upper_col.lower()\n", "    final_column_names.append(f'{lang_code_lower_col}_input')\n", "    final_column_names.append(f'{lang_code_lower_col}_output')\n", "\n", "print(f\"\\nFinal column names for the new dataset structure: {', '.join(final_column_names)}\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 6. Prepare records for the new 'train' split\n", "# -----------------------------------------------------------------------------\n", "train_records_list = []\n", "# Sort by id_numeric for deterministic order of rows\n", "for base_id_numeric_key in sorted(aggregated_data_for_new_train_split.keys()):\n", "    record_data_dict = aggregated_data_for_new_train_split[base_id_numeric_key]\n", "    # Ensure all columns are present, filling with None if a language pair is missing for this ID\n", "    new_record_for_list = {col_name: record_data_dict.get(col_name) for col_name in final_column_names}\n", "    train_records_list.append(new_record_for_list)\n", "\n", "print(f\"\\nNumber of records prepared for the new 'train' split: {len(train_records_list)}\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 7. Prepare records for the new 'val' split\n", "# -----------------------------------------------------------------------------\n", "val_records_list = []\n", "# Sort by id_numeric for deterministic order of rows\n", "for base_id_numeric_key in sorted(aggregated_data_for_new_val_split.keys()):\n", "    record_data_dict = aggregated_data_for_new_val_split[base_id_numeric_key]\n", "    # Ensure all columns are present, filling with None if a language pair is missing for this ID\n", "    new_record_for_list = {col_name: record_data_dict.get(col_name) for col_name in final_column_names}\n", "    val_records_list.append(new_record_for_list)\n", "\n", "print(f\"Number of records prepared for the new 'val' split: {len(val_records_list)}\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 8. Create Hugging Face Dataset objects for train and val\n", "# -----------------------------------------------------------------------------\n", "# Only create dataset if records exist to avoid errors\n", "train_hf_dataset = Dataset.from_list(train_records_list) if train_records_list else Dataset.from_dict({'id_numeric': []}) # Create empty with schema if no data\n", "val_hf_dataset = Dataset.from_list(val_records_list) if val_records_list else Dataset.from_dict({'id_numeric': []})     # Create empty with schema if no data\n", "\n", "# If creating empty datasets, ensure they have the correct features (columns)\n", "# This is important if one split is empty but the other is not, for consistency in DatasetDict\n", "if not train_records_list and val_records_list: # train empty, val not\n", "    train_hf_dataset = Dataset.from_list([{col: None for col in final_column_names}]).select(range(0)) # Empty dataset with schema\n", "if not val_records_list and train_records_list: # val empty, train not\n", "    val_hf_dataset = Dataset.from_list([{col: None for col in final_column_names}]).select(range(0)) # Empty dataset with schema\n", "if not train_records_list and not val_records_list: # both empty\n", "    empty_schema_dict = {col: [] for col in final_column_names}\n", "    train_hf_dataset = Dataset.from_dict(empty_schema_dict)\n", "    val_hf_dataset = Dataset.from_dict(empty_schema_dict)\n", "\n", "print(\"\\nHugging Face Dataset objects created for 'train' and 'val' splits.\")\n", "\n", "# -----------------------------------------------------------------------------\n", "# 9. <PERSON><PERSON> the final DatasetDict\n", "# -----------------------------------------------------------------------------\n", "final_dataset_dict = DatasetDict({\n", "    'train': train_hf_dataset,\n", "    'val': val_hf_dataset\n", "})\n", "\n", "print(\"\\n--- Final Combined Hugging Face DatasetDict ---\")\n", "print(final_dataset_dict)\n", "\n", "# -----------------------------------------------------------------------------\n", "# 10. Print info and examples\n", "# -----------------------------------------------------------------------------\n", "if len(final_dataset_dict['train']) > 0:\n", "    print(\"\\n--- Example of the first record in the new 'train' split ---\")\n", "    # Using pandas Series for potentially better readability of a single complex record\n", "    example_train_record_pd = pd.Series(final_dataset_dict['train'][0])\n", "    print(example_train_record_pd)\n", "else:\n", "    print(\"\\nThe new 'train' split is empty.\")\n", "\n", "if len(final_dataset_dict['val']) > 0:\n", "    print(\"\\n--- Example of the first record in the new 'val' split ---\")\n", "    example_val_record_pd = pd.Series(final_dataset_dict['val'][0])\n", "    print(example_val_record_pd)\n", "else:\n", "    print(\"\\nThe new 'val' split is empty.\")\n", "\n", "# Optional: Display a sample of the data using Pandas for better readability\n", "if len(train_records_list) > 0:\n", "    print(\"\\n--- Sample of the new 'train' data (first 3 rows as DataFrame) ---\")\n", "    df_train_sample = pd.DataFrame(train_records_list[:3])\n", "    print(df_train_sample.to_string()) # .to_string() for better console output of wide dfs\n", "\n", "if len(val_records_list) > 0:\n", "    print(\"\\n--- Sample of the new 'val' data (first 3 rows as DataFrame) ---\")\n", "    df_val_sample = pd.DataFrame(val_records_list[:3])\n", "    print(df_val_sample.to_string()) # .to_string() for better console output of wide dfs\n", "\n", "print(\"\\nProcessing complete.\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0ff61f66ddce4025aebae2c00a8a5daf", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "330eaca518174711b6c6cf4517a46f4a", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/2 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "57456c0e45bc46bfb82d2d2e8d90eb8a", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "9f664152600c46ffa00c7027de7e0d14", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["CommitInfo(commit_url='https://huggingface.co/datasets/junkim100/Lima-X-Processed/commit/f17286c0e01e5619d721ebc67d1516960d0d6316', commit_message='Upload dataset', commit_description='', oid='f17286c0e01e5619d721ebc67d1516960d0d6316', pr_url=None, repo_url=RepoUrl('https://huggingface.co/datasets/junkim100/Lima-X-Processed', endpoint='https://huggingface.co', repo_type='dataset', repo_id='junkim100/Lima-X-Processed'), pr_revision=None, pr_num=None)"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# push to huggingface\n", "final_dataset_dict.push_to_hub(\"junkim100/Lima-X-Processed\")"]}], "metadata": {"kernelspec": {"display_name": "fuck", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 2}