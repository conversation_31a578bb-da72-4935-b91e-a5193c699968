# Cell 1: Import required libraries
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import requests
import json
from datasets import load_dataset
import numpy as np
from transformers import AutoTokenizer

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")


def plot_length_distribution(lengths, title="Text Length Distribution"):
    """
    Create visualizations for text length distribution
    """
    if lengths is None:
        print("No data to plot")
        return

    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(title, fontsize=16)

    # Histogram
    axes[0,0].hist(lengths, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_title('Length Distribution')
    axes[0,0].set_xlabel('Text Length')
    axes[0,0].set_ylabel('Frequency')

    # Box plot
    axes[0,1].boxplot(lengths)
    axes[0,1].set_title('Length Box Plot')
    axes[0,1].set_ylabel('Text Length')

    # Cumulative distribution
    sorted_lengths = np.sort(lengths)
    cumulative = np.arange(1, len(sorted_lengths) + 1) / len(sorted_lengths)
    axes[1,0].plot(sorted_lengths, cumulative, marker='o', markersize=2)
    axes[1,0].set_title('Cumulative Distribution')
    axes[1,0].set_xlabel('Text Length')
    axes[1,0].set_ylabel('Cumulative Probability')

    # Statistics summary
    stats_text = f"""
    Count: {len(lengths)}
    Min: {min(lengths)}
    Max: {max(lengths)}
    Mean: {np.mean(lengths):.2f}
    Median: {np.median(lengths):.2f}
    Std: {np.std(lengths):.2f}
    """
    axes[1,1].text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
    axes[1,1].set_title('Statistics Summary')
    axes[1,1].axis('off')

    plt.tight_layout()
    plt.show()

def analyze_dataset_direct(dataset_name, column_name, tokenizer, split='train'):
    """
    Analyze dataset column lengths by loading the dataset directly
    """
    try:
        # Load the dataset
        dataset = load_dataset(dataset_name)

        # There's an item in junnei/ko-limo that has an empty solution. Remove it.
        dataset['train'] = dataset['train'].filter(lambda x: len(x['solution']) > 0)
        dataset['train']

        # Remove data with length larger than 40960
        dataset['train'] = dataset['train'].filter(lambda x: len(x['solution']) < 40960)

        # Get the specified split
        data_split = dataset[split]

        # Check if column exists
        if column_name in data_split.column_names:
            # Extract the column data
            column_data = data_split[column_name]

            # Calculate lengths
            # lengths = [len(str(item)) for item in column_data]

            #calcluate token length
            lengths = [len(tokenizer(str(item))['input_ids']) for item in column_data]

            # Calculate statistics
            stats = {
                'count': len(lengths),
                'min_length': min(lengths),
                'max_length': max(lengths),
                'mean_length': np.mean(lengths),
                'median_length': np.median(lengths),
                'std_length': np.std(lengths)
            }

            plot_length_distribution(lengths, f"{dataset_name} {split} {column_name}")
            print("Dataset Statistics:")
            for key, value in stats.items():
                print(f"{key}: {value}")
            return lengths, stats
        else:
            print(f"Column '{column_name}' not found. Available columns: {data_split.column_names}")
            return None, None

    except Exception as e:
        print(f"Error loading dataset: {e}")
        return None, None

tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-3.1-8B")

lengths, stats = analyze_dataset_direct("GAIR/LIMO", "solution", tokenizer)
lengths, stats = analyze_dataset_direct("junnei/ko-limo", "solution", tokenizer)

from datasets import Dataset
from datasets import load_dataset

# I'm going to create a new dataset where question column from junnei/ko-limo is kept as question, question from GAIR/LIMO as translation, solution from GAIR/LIMO as solution, and answer from GAIR/LIMO as answer.
kolimo = load_dataset("junnei/ko-limo")
limo = load_dataset("GAIR/LIMO")

new_data = {
    'question': kolimo['train']['question'],
    'translation': limo['train']['question'],
    'solution': limo['train']['solution'],
    'answer': limo['train']['answer']
}

new_dataset = Dataset.from_dict(new_data)

# Remove data with length larger than 40960 and less than 1
new_dataset = new_dataset.filter(lambda x: len(x['question']) < 40960)
new_dataset = new_dataset.filter(lambda x: len(x['question']) > 0)
new_dataset = new_dataset.filter(lambda x: len(x['translation']) < 40960)
new_dataset = new_dataset.filter(lambda x: len(x['translation']) > 0)

# split train and test
new_dataset = new_dataset.train_test_split(test_size=0.2, seed=42)

new_dataset.push_to_hub("junkim100/limo_crosslingual_ko_en")

def max_token_length(dataset, tokenizer, columns):
    return max([len(tokenizer(' '.join([x[col] for col in columns]))['input_ids']) for x in dataset])

limo_crosslingual_ko_en = load_dataset("junkim100/limo_crosslingual_ko_en")

max_token_length(limo_crosslingual_ko_en['train'], tokenizer, ['question', 'translation', 'solution', 'answer'])

processed = load_dataset("junkim100/Lima-X-Processed")

max_token_length(processed['train'], tokenizer, ['en_input', 'en_output', 'de_input', 'de_output'])

# -----------------------------------------------------------------------------
# 1. Import necessary libraries
# -----------------------------------------------------------------------------
from datasets import load_dataset, Dataset, DatasetDict
import re # For parsing the ID strings
import pandas as pd # Optional: for displaying samples as DataFrames

print("Libraries imported.")

# -----------------------------------------------------------------------------
# 2. Configuration
# -----------------------------------------------------------------------------
dataset_name = "lamarr-org/Lima-X"
# Target languages as specified in the request
target_languages_upper = ["EN", "DE", "ES", "FR", "IT"]

print(f"Configuration set for dataset: {dataset_name}")
print(f"Target languages: {', '.join(target_languages_upper)}")

# -----------------------------------------------------------------------------
# 3. Initialize data aggregators
# These dictionaries will hold the combined data, keyed by base_id_numeric.
# One dictionary for each target split in the new dataset.
# -----------------------------------------------------------------------------
aggregated_data_for_new_train_split = {}
aggregated_data_for_new_val_split = {}

print("Data aggregators initialized.")

# -----------------------------------------------------------------------------
# 4. Load data and process conversations
# -----------------------------------------------------------------------------
print(f"\nStarting processing for dataset: {dataset_name}")

for lang_code_upper in target_languages_upper:
    print(f"\nProcessing language: {lang_code_upper}")
    try:
        # Load the specific language subset (configuration)
        # trust_remote_code=True is often needed for datasets with custom loading scripts.
        lang_dataset = load_dataset(dataset_name, name=lang_code_upper, trust_remote_code=True)
    except Exception as e:
        print(f"  Could not load subset {lang_code_upper}. Error: {e}")
        print(f"  Skipping language {lang_code_upper}.")
        continue

    # Iterate over available splits for this language configuration (e.g., 'train', 'val')
    for original_split_name in lang_dataset.keys():
        print(f"  Processing original split: {original_split_name} for language {lang_code_upper}")

        for example_count, example in enumerate(lang_dataset[original_split_name]):
            raw_id = example['id']
            conversations = example['conversations']

            # Parse the ID string (e.g., "train_id_0_DE", "val_id_0_EN")
            # Regex captures: (original_split_prefix, numeric_id, language_suffix)
            match = re.match(r'(\w+)_id_(\d+)_(\w+)', raw_id)
            if not match:
                print(f"    Skipping malformed ID: {raw_id} in {lang_code_upper}/{original_split_name}")
                continue

            id_original_split_prefix, base_id_str, lang_from_id = match.groups()
            base_id_numeric = int(base_id_str)

            # Sanity check for language code from ID
            if lang_from_id.upper() != lang_code_upper:
                print(f"    Warning: Mismatch detected for ID {raw_id}. Expected lang {lang_code_upper}, found {lang_from_id} in ID.")
                # We proceed using lang_code_upper for column naming as we are in its processing loop.

            # Extract human and gpt values from conversations
            human_value = None
            gpt_value = None

            if isinstance(conversations, list) and len(conversations) >= 2:
                # Try to robustly find 'human' and 'gpt' turns
                convo_map = {}
                for turn in conversations:
                    if isinstance(turn, dict) and 'from' in turn and 'value' in turn:
                        # Take the first occurrence if multiple gpt/human turns exist (though not expected for Lima)
                        if turn['from'] == 'human' and 'human' not in convo_map:
                            convo_map['human'] = turn['value']
                        elif turn['from'] == 'gpt' and 'gpt' not in convo_map:
                            convo_map['gpt'] = turn['value']

                human_value = convo_map.get('human')
                gpt_value = convo_map.get('gpt')

                if human_value is None or gpt_value is None:
                    print(f"    Skipping ID {raw_id} in {lang_code_upper}/{original_split_name}, 'human' or 'gpt' turn missing or malformed in conversations.")
                    continue
            else:
                print(f"    Skipping ID {raw_id} in {lang_code_upper}/{original_split_name}, conversations format is not as expected.")
                continue

            # Determine which aggregator to use based on the original split prefix
            target_aggregator = None
            normalized_original_split_prefix = id_original_split_prefix.lower()

            if normalized_original_split_prefix == 'train':
                target_aggregator = aggregated_data_for_new_train_split
            elif normalized_original_split_prefix == 'val':
                target_aggregator = aggregated_data_for_new_val_split
            else:
                print(f"    Skipping ID {raw_id} due to unrecognized original split prefix: {id_original_split_prefix}. Data won't be added to new train or val splits.")
                continue

            # Initialize the entry for this base_id_numeric if it's the first time we see it for this target split
            if base_id_numeric not in target_aggregator:
                target_aggregator[base_id_numeric] = {
                    'id_numeric': base_id_numeric,
                    # 'original_split_for_id': id_original_split_prefix # Optional: If you want to keep this information explicitly
                }

            # Add the input and output for the current language
            lang_code_lower = lang_code_upper.lower()
            target_aggregator[base_id_numeric][f'{lang_code_lower}_input'] = human_value
            target_aggregator[base_id_numeric][f'{lang_code_lower}_output'] = gpt_value

print("\nFinished loading and initial processing of all specified languages and original splits.")

# -----------------------------------------------------------------------------
# 5. Define final column names for the new dataset structure
# -----------------------------------------------------------------------------
final_column_names = ['id_numeric'] # Start with the common ID column
# Add columns for each language's input and output
for lang_code_upper_col in target_languages_upper:
    lang_code_lower_col = lang_code_upper_col.lower()
    final_column_names.append(f'{lang_code_lower_col}_input')
    final_column_names.append(f'{lang_code_lower_col}_output')

print(f"\nFinal column names for the new dataset structure: {', '.join(final_column_names)}")

# -----------------------------------------------------------------------------
# 6. Prepare records for the new 'train' split
# -----------------------------------------------------------------------------
train_records_list = []
# Sort by id_numeric for deterministic order of rows
for base_id_numeric_key in sorted(aggregated_data_for_new_train_split.keys()):
    record_data_dict = aggregated_data_for_new_train_split[base_id_numeric_key]
    # Ensure all columns are present, filling with None if a language pair is missing for this ID
    new_record_for_list = {col_name: record_data_dict.get(col_name) for col_name in final_column_names}
    train_records_list.append(new_record_for_list)

print(f"\nNumber of records prepared for the new 'train' split: {len(train_records_list)}")

# -----------------------------------------------------------------------------
# 7. Prepare records for the new 'val' split
# -----------------------------------------------------------------------------
val_records_list = []
# Sort by id_numeric for deterministic order of rows
for base_id_numeric_key in sorted(aggregated_data_for_new_val_split.keys()):
    record_data_dict = aggregated_data_for_new_val_split[base_id_numeric_key]
    # Ensure all columns are present, filling with None if a language pair is missing for this ID
    new_record_for_list = {col_name: record_data_dict.get(col_name) for col_name in final_column_names}
    val_records_list.append(new_record_for_list)

print(f"Number of records prepared for the new 'val' split: {len(val_records_list)}")

# -----------------------------------------------------------------------------
# 8. Create Hugging Face Dataset objects for train and val
# -----------------------------------------------------------------------------
# Only create dataset if records exist to avoid errors
train_hf_dataset = Dataset.from_list(train_records_list) if train_records_list else Dataset.from_dict({'id_numeric': []}) # Create empty with schema if no data
val_hf_dataset = Dataset.from_list(val_records_list) if val_records_list else Dataset.from_dict({'id_numeric': []})     # Create empty with schema if no data

# If creating empty datasets, ensure they have the correct features (columns)
# This is important if one split is empty but the other is not, for consistency in DatasetDict
if not train_records_list and val_records_list: # train empty, val not
    train_hf_dataset = Dataset.from_list([{col: None for col in final_column_names}]).select(range(0)) # Empty dataset with schema
if not val_records_list and train_records_list: # val empty, train not
    val_hf_dataset = Dataset.from_list([{col: None for col in final_column_names}]).select(range(0)) # Empty dataset with schema
if not train_records_list and not val_records_list: # both empty
    empty_schema_dict = {col: [] for col in final_column_names}
    train_hf_dataset = Dataset.from_dict(empty_schema_dict)
    val_hf_dataset = Dataset.from_dict(empty_schema_dict)

print("\nHugging Face Dataset objects created for 'train' and 'val' splits.")

# -----------------------------------------------------------------------------
# 9. Create the final DatasetDict
# -----------------------------------------------------------------------------
final_dataset_dict = DatasetDict({
    'train': train_hf_dataset,
    'val': val_hf_dataset
})

print("\n--- Final Combined Hugging Face DatasetDict ---")
print(final_dataset_dict)

# -----------------------------------------------------------------------------
# 10. Print info and examples
# -----------------------------------------------------------------------------
if len(final_dataset_dict['train']) > 0:
    print("\n--- Example of the first record in the new 'train' split ---")
    # Using pandas Series for potentially better readability of a single complex record
    example_train_record_pd = pd.Series(final_dataset_dict['train'][0])
    print(example_train_record_pd)
else:
    print("\nThe new 'train' split is empty.")

if len(final_dataset_dict['val']) > 0:
    print("\n--- Example of the first record in the new 'val' split ---")
    example_val_record_pd = pd.Series(final_dataset_dict['val'][0])
    print(example_val_record_pd)
else:
    print("\nThe new 'val' split is empty.")

# Optional: Display a sample of the data using Pandas for better readability
if len(train_records_list) > 0:
    print("\n--- Sample of the new 'train' data (first 3 rows as DataFrame) ---")
    df_train_sample = pd.DataFrame(train_records_list[:3])
    print(df_train_sample.to_string()) # .to_string() for better console output of wide dfs

if len(val_records_list) > 0:
    print("\n--- Sample of the new 'val' data (first 3 rows as DataFrame) ---")
    df_val_sample = pd.DataFrame(val_records_list[:3])
    print(df_val_sample.to_string()) # .to_string() for better console output of wide dfs

print("\nProcessing complete.")


# push to huggingface
final_dataset_dict.push_to_hub("junkim100/Lima-X-Processed")