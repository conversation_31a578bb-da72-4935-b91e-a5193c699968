#!/usr/bin/env python3
"""
Debug script to check what prompts are actually being generated
"""

import sys
sys.path.append('.')
from chat_cli import Chat<PERSON>ot

def debug_prompt_generation():
    """Debug the actual prompt generation"""
    
    print("🔍 Debugging Prompt Generation")
    print("=" * 50)
    
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    
    try:
        # Create chatbot instance (without loading the full model)
        print("🤖 Creating ChatBot instance...")
        
        # We'll manually create the components to avoid loading the full model
        from transformers import AutoTokenizer
        
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print(f"✅ Tokenizer loaded - Vocab size: {len(tokenizer)}")
        
        # Check translation tokens
        tokens_to_check = ["<in_translation>", "</in_translation>", "<out_translation>", "</out_translation>"]
        
        print(f"\n🔍 Checking translation tokens...")
        for token in tokens_to_check:
            if token in tokenizer.get_vocab():
                token_id = tokenizer.convert_tokens_to_ids(token)
                print(f"✅ {token:<20} - ID: {token_id}")
            else:
                print(f"❌ {token:<20} - NOT FOUND")
        
        # Test conversation formatting
        print(f"\n📝 Testing Conversation Formatting...")
        print("-" * 40)
        
        test_conversations = [
            [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Ciao"}
            ],
            [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Come si dice hello world in inglese?"}
            ]
        ]
        
        for i, conversation in enumerate(test_conversations, 1):
            print(f"\n{i}. Test conversation:")
            print(f"   User: '{conversation[-1]['content']}'")
            
            # Test with chat template
            try:
                formatted = tokenizer.apply_chat_template(
                    conversation,
                    tokenize=False,
                    add_generation_prompt=True
                )
                
                print(f"\n   📋 Generated prompt:")
                print("   " + "-" * 50)
                # Show last few lines
                lines = formatted.split('\n')
                for line in lines[-5:]:
                    print(f"   {line}")
                print("   " + "-" * 50)
                
                # Check for translation token
                if "<in_translation>" in formatted:
                    print("   ✅ Contains <in_translation> token")
                else:
                    print("   ❌ Missing <in_translation> token")
                    
            except Exception as e:
                print(f"   ❌ Error with chat template: {e}")
                
            # Test manual formatting (fallback)
            try:
                print(f"\n   📋 Manual formatting test:")
                manual_formatted = ""
                for message in conversation:
                    if message["role"] == "system":
                        manual_formatted += f"<|start_header_id|>system<|end_header_id|>\n\n{message['content']}<|eot_id|>"
                    elif message["role"] == "user":
                        manual_formatted += f"<|start_header_id|>user<|end_header_id|>\n\n{message['content']}<|eot_id|>"
                
                manual_formatted += "<|start_header_id|>assistant<|end_header_id|>\n\n<in_translation>"
                
                print("   " + "-" * 50)
                lines = manual_formatted.split('\n')
                for line in lines[-5:]:
                    print(f"   {line}")
                print("   " + "-" * 50)
                
                if "<in_translation>" in manual_formatted:
                    print("   ✅ Manual format contains <in_translation> token")
                else:
                    print("   ❌ Manual format missing <in_translation> token")
                    
            except Exception as e:
                print(f"   ❌ Error with manual formatting: {e}")
        
        # Test tokenization
        print(f"\n🔤 Testing Tokenization...")
        print("-" * 30)
        
        test_text = "<in_translation>Hello</in_translation>"
        try:
            tokens = tokenizer.tokenize(test_text)
            token_ids = tokenizer.convert_tokens_to_ids(tokens)
            
            print(f"Text: {test_text}")
            print(f"Tokens: {tokens}")
            print(f"Token IDs: {token_ids}")
            
            # Decode back
            decoded = tokenizer.decode(token_ids)
            print(f"Decoded: {decoded}")
            
        except Exception as e:
            print(f"❌ Tokenization error: {e}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_prompt_generation()
