# Simplified Dockerfile for ML training environment
FROM nvidia/cuda:12.1-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    git \
    wget \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic links
RUN ln -s /usr/bin/python3 /usr/bin/python

# Upgrade pip
RUN python -m pip install --upgrade pip

# Install PyTorch with CUDA support (using stable versions)
RUN pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Install core packages (may use slightly different versions if exact ones fail)
RUN pip install transformers>=4.50.0
RUN pip install numpy>=1.24.0
RUN pip install deepspeed>=0.14.0

# Install additional dependencies
RUN pip install \
    accelerate \
    datasets \
    peft \
    wandb \
    jupyter \
    tqdm

# Create working directory
WORKDIR /workspace

# Create user
RUN useradd -m -s /bin/bash mluser
RUN chown -R mluser:mluser /workspace
USER mluser

# Verify installation
RUN python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA: {torch.cuda.is_available()}')"

CMD ["/bin/bash"]
