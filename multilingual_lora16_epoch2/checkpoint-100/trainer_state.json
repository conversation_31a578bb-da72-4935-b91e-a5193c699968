{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 0.05901881224640354, "eval_steps": 100, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0005901881224640354, "grad_norm": 13.907805442810059, "learning_rate": 0.0, "loss": 5.7487, "step": 1}, {"epoch": 0.011803762449280709, "grad_norm": 2.254296064376831, "learning_rate": 5.833040977508817e-05, "loss": 3.8995, "step": 20}, {"epoch": 0.023607524898561418, "grad_norm": 0.9450002312660217, "learning_rate": 7.182679576172462e-05, "loss": 1.7948, "step": 40}, {"epoch": 0.03541128734784212, "grad_norm": 0.7378047108650208, "learning_rate": 7.972167545916544e-05, "loss": 1.4338, "step": 60}, {"epoch": 0.047215049797122835, "grad_norm": 0.7283475399017334, "learning_rate": 8.532318174836105e-05, "loss": 1.3365, "step": 80}, {"epoch": 0.05901881224640354, "grad_norm": 0.6210907101631165, "learning_rate": 8.966804757690343e-05, "loss": 1.3234, "step": 100}, {"epoch": 0.05901881224640354, "eval_loss": 1.49502432346344, "eval_runtime": 313.9198, "eval_samples_per_second": 21.598, "eval_steps_per_second": 0.675, "step": 100}], "logging_steps": 20, "max_steps": 3390, "num_input_tokens_seen": 0, "num_train_epochs": 2, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 11389679378432.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}