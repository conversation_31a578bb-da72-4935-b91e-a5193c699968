{"cells": [{"cell_type": "code", "execution_count": 7, "id": "f7616767", "metadata": {}, "outputs": [], "source": ["from datasets import load_dataset, Dataset, DatasetDict, concatenate_datasets\n", "import re\n", "import pandas as pd # Optional: for displaying samples, not strictly needed for core logic\n", "import random\n", "from huggingface_hub import HfApi, create_repo # For pushing to hub\n", "\n", "# -----------------------------------------------------------------------------\n", "# 0. Configuration\n", "# -----------------------------------------------------------------------------\n", "HF_TOKEN = \"*************************************\"\n", "HF_USERNAME = \"junkim100\"\n", "TARGET_REPO_NAME = \"multilingual_instruction_tuning_lima_bactrian\"\n", "TARGET_REPO_ID = f\"{HF_USERNAME}/{TARGET_REPO_NAME}\"\n", "\n", "random.seed(42) # For reproducible splits\n", "lima_language_codes = [\"en\", \"de\", \"es\", \"fr\", \"it\"]\n", "# bactrian_language_codes = [\"af\", \"ar\", \"az\", \"bn\", \"cs\", \"de\", \"en\", \"es\", \"et\", \"fa\", \"fi\", \"gl\", \"gu\", \"he\", \"hi\", \"hr\", \"id\", \"it\", \"ja\", \"ka\", \"kk\", \"km\", \"ko\", \"lt\", \"lv\", \"mk\", \"ml\", \"mn\", \"mr\", \"my\", \"ne\", \"nl\", \"pl\", \"ps\", \"pt\", \"ro\", \"ru\", \"si\", \"sl\", \"sv\", \"sw\", \"ta\", \"te\", \"th\", \"tl\", \"tr\", \"uk\", \"ur\", \"vi\", \"xh\", \"zh\"]\n", "# bactrian_language_codes = [\"en\", \"de\", \"es\", \"fr\", \"it\", \"zh\", \"ko\", \"ja\", \"pt\", \"ar\"]\n", "bactrian_language_codes = [\"en\", \"de\", \"es\", \"fr\", \"it\"]"]}, {"cell_type": "code", "execution_count": 2, "id": "8a35f506", "metadata": {}, "outputs": [], "source": ["# -----------------------------------------------------------------------------\n", "# Function to process Lima-X dataset\n", "# -----------------------------------------------------------------------------\n", "def process_lima_x(languages_to_process):\n", "    print(\"--- Starting Lima-X Processing ---\")\n", "    dataset_name = \"lamarr-org/Lima-X\"\n", "    aggregated_data_all = {}\n", "\n", "    print(f\"Processing Lima-X for languages: {', '.join(languages_to_process)}\")\n", "\n", "    for lang_code_upper in [lang.upper() for lang in languages_to_process]:\n", "        print(f\"  Processing Lima-X language: {lang_code_upper}\")\n", "        try:\n", "            # trust_remote_code is needed for Lima-X configs\n", "            lang_dataset_config = load_dataset(dataset_name, name=lang_code_upper, trust_remote_code=True)\n", "        except Exception as e:\n", "            print(f\"    Could not load Lima-X subset {lang_code_upper}. Error: {e}. Skipping.\")\n", "            continue\n", "\n", "        for original_split_name in lang_dataset_config.keys():\n", "            for example in lang_dataset_config[original_split_name]:\n", "                raw_id = example.get('id')\n", "                conversations = example.get('conversations')\n", "\n", "                if not raw_id or not conversations:\n", "                    # print(f\"    Skipping Lima example in {lang_code_upper}/{original_split_name} due to missing id or conversations.\")\n", "                    continue\n", "\n", "                match = re.match(r'(\\w+)_id_(\\d+)_(\\w+)', raw_id)\n", "                if not match:\n", "                    # print(f\"    Skipping malformed Lima-X ID: {raw_id}\")\n", "                    continue\n", "\n", "                _, base_id_str, _ = match.groups()\n", "                base_id_numeric = int(base_id_str)\n", "\n", "                human_value, gpt_value = None, None\n", "                if isinstance(conversations, list) and len(conversations) >= 2:\n", "                    temp_human, temp_gpt = None, None\n", "                    for turn in conversations:\n", "                        if isinstance(turn, dict) and 'from' in turn and 'value' in turn:\n", "                            if turn['from'] == 'human' and temp_human is None:\n", "                                temp_human = turn['value']\n", "                            elif turn['from'] == 'gpt' and temp_gpt is None:\n", "                                temp_gpt = turn['value']\n", "                    human_value, gpt_value = temp_human, temp_gpt\n", "\n", "                if human_value is None or gpt_value is None:\n", "                    # print(f\"    Skipping Lima-X ID {raw_id}, 'human' or 'gpt' turn missing/malformed.\")\n", "                    continue\n", "\n", "                if base_id_numeric not in aggregated_data_all:\n", "                    aggregated_data_all[base_id_numeric] = {'id_numeric': base_id_numeric}\n", "\n", "                lang_code_lower = lang_code_upper.lower()\n", "                aggregated_data_all[base_id_numeric][f'{lang_code_lower}_input'] = human_value\n", "                aggregated_data_all[base_id_numeric][f'{lang_code_lower}_output'] = gpt_value\n", "\n", "    final_lima_column_names = ['id_numeric']\n", "    for lang_code_lower_col in languages_to_process:\n", "        final_lima_column_names.append(f'{lang_code_lower_col}_input')\n", "        final_lima_column_names.append(f'{lang_code_lower_col}_output')\n", "\n", "    all_lima_records_list = []\n", "    for base_id_numeric_key in sorted(aggregated_data_all.keys()):\n", "        record_data_dict = aggregated_data_all[base_id_numeric_key]\n", "        new_record = {col_name: record_data_dict.get(col_name) for col_name in final_lima_column_names}\n", "        all_lima_records_list.append(new_record)\n", "\n", "    if not all_lima_records_list:\n", "        print(\"  No data processed for Lima-X. Returning empty DatasetDict.\")\n", "        empty_schema = {col: [] for col in final_lima_column_names}\n", "        return DatasetDict({\n", "            'train': Dataset.from_dict(empty_schema), 'val': Dataset.from_dict(empty_schema), 'test': Dataset.from_dict(empty_schema)\n", "        })\n", "\n", "    full_lima_dataset = Dataset.from_list(all_lima_records_list)\n", "\n", "    train_test_split_val = full_lima_dataset.train_test_split(test_size=0.2, seed=42)\n", "    lima_train = train_test_split_val['train']\n", "    temp_val_test = train_test_split_val['test']\n", "\n", "    val_test_split_final = temp_val_test.train_test_split(test_size=0.5, seed=42)\n", "    lima_val = val_test_split_final['train']\n", "    lima_test = val_test_split_final['test']\n", "\n", "    lima_final_dict = DatasetDict({'train': lima_train, 'val': lima_val, 'test': lima_test})\n", "    print(f\"  Lima-X processing complete. Splits: train={len(lima_train)}, val={len(lima_val)}, test={len(lima_test)}\")\n", "    print(\"--- Finished Lima-X Processing ---\")\n", "    return lima_final_dict\n", "\n", "# -----------------------------------------------------------------------------\n", "# Function to process Bactrian-X dataset (CORRECTED)\n", "# -----------------------------------------------------------------------------\n", "def process_bactrian_x(languages_to_process):\n", "    print(\"--- Starting Bactrian-X Processing ---\")\n", "\n", "    # Dictionary to aggregate data by original ID across languages\n", "    aggregated_data_by_id = {}\n", "\n", "    bactrian_subset_column_names = []\n", "    for lang_code in languages_to_process:\n", "        bactrian_subset_column_names.append(f\"{lang_code}_input\")\n", "        bactrian_subset_column_names.append(f\"{lang_code}_output\")\n", "    bactrian_id_column = 'id_original_bactrian'\n", "    full_bactrian_schema_columns = bactrian_subset_column_names + [bactrian_id_column]\n", "\n", "    print(f\"Processing Bactrian-X for languages: {', '.join(languages_to_process)}\")\n", "\n", "    for lang_code in languages_to_process:\n", "        print(f\"  Processing Bactrian-X language: {lang_code}\")\n", "\n", "        current_dataset_name = \"MBZUAI/Bactrian-X\" # Main English Bactrian\n", "\n", "        try:\n", "            # Bactrian datasets may or may not need trust_remote_code,\n", "            # but it's safer to include it if their loading scripts are custom.\n", "            # For standard Parquet/JSON datasets, it might not be strictly needed.\n", "            lang_specific_dataset_config = load_dataset(current_dataset_name, name=lang_code, trust_remote_code=True)\n", "        except Exception as e:\n", "            print(f\"    Could not load Bactrian-X for {lang_code} using {current_dataset_name}. Error: {e}. Skipping.\")\n", "            continue\n", "\n", "        # Bactrian-X datasets usually have a 'train' split or just one primary split.\n", "        # Iterate through available splits, though typically it's just one.\n", "        data_found_for_lang = False\n", "        for split_name in lang_specific_dataset_config.keys():\n", "            print(f\"    Loading from split: {split_name}\")\n", "            for example in lang_specific_dataset_config[split_name]:\n", "                instruction = example.get('instruction')\n", "                # 'input' field in Bactrian-X is context, not user input\n", "                context = example.get('input', '')\n", "                output = example.get('output')\n", "                original_id = example.get('id')\n", "\n", "                if instruction is None or output is None or original_id is None:\n", "                    # print(f\"    Skipping Bactrian-X example (missing instruction/output/id). Lang: {lang_code}, ID: {original_id}\")\n", "                    continue\n", "\n", "                full_instruction = f\"{context}\\n{instruction}\".strip() if context else instruction\n", "\n", "                # Initialize record for this ID if not exists\n", "                if original_id not in aggregated_data_by_id:\n", "                    aggregated_data_by_id[original_id] = {col: None for col in full_bactrian_schema_columns}\n", "                    aggregated_data_by_id[original_id][bactrian_id_column] = original_id\n", "\n", "                # Add data for this language\n", "                aggregated_data_by_id[original_id][f'{lang_code}_input'] = full_instruction\n", "                aggregated_data_by_id[original_id][f'{lang_code}_output'] = output\n", "                data_found_for_lang = True\n", "\n", "        if not data_found_for_lang:\n", "             print(f\"    No data records extracted for Bactrian-X language: {lang_code} from {current_dataset_name}\")\n", "\n", "    # Filter records to only include those that have data for ALL requested languages\n", "    print(f\"  Filtering records to include only those with data for all {len(languages_to_process)} languages...\")\n", "    complete_records = []\n", "    total_records = len(aggregated_data_by_id)\n", "\n", "    for record_id, record_data in aggregated_data_by_id.items():\n", "        # Check if this record has non-null data for all languages\n", "        has_all_languages = True\n", "        for lang_code in languages_to_process:\n", "            if (record_data.get(f'{lang_code}_input') is None or\n", "                record_data.get(f'{lang_code}_output') is None):\n", "                has_all_languages = False\n", "                break\n", "\n", "        if has_all_languages:\n", "            complete_records.append(record_data)\n", "\n", "    print(f\"  Found {len(complete_records)} complete records out of {total_records} total records\")\n", "    print(f\"  Filtered out {total_records - len(complete_records)} incomplete records\")\n", "\n", "    if not complete_records:\n", "        print(\"  No complete records found for Bactrian-X after filtering. Returning empty DatasetDict.\")\n", "        empty_schema = {col: [] for col in full_bactrian_schema_columns}\n", "        return DatasetDict({\n", "            'train': Dataset.from_dict(empty_schema), 'val': Dataset.from_dict(empty_schema), 'test': Dataset.from_dict(empty_schema)\n", "        })\n", "\n", "    full_bactrian_dataset = Dataset.from_list(complete_records)\n", "\n", "    train_test_split_val = full_bactrian_dataset.train_test_split(test_size=0.2, seed=42)\n", "    bactrian_train = train_test_split_val['train']\n", "    temp_val_test = train_test_split_val['test']\n", "\n", "    val_test_split_final = temp_val_test.train_test_split(test_size=0.5, seed=42)\n", "    bactrian_val = val_test_split_final['train']\n", "    bactrian_test = val_test_split_final['test']\n", "\n", "    bactrian_final_dict = DatasetDict({'train': bactrian_train, 'val': bactrian_val, 'test': bactrian_test})\n", "    print(f\"  Bactrian-X processing complete. Splits: train={len(bactrian_train)}, val={len(bactrian_val)}, test={len(bactrian_test)}\")\n", "    print(\"--- Finished Bactrian-X Processing ---\")\n", "    return bactrian_final_dict\n", "\n", "# -----------------------------------------------------------------------------\n", "# Function to create the combined dataset\n", "# -----------------------------------------------------------------------------\n", "def create_combined_dataset(lima_dict, bactrian_dict, languages_for_combined):\n", "    print(\"--- Starting Combined Dataset Creation ---\")\n", "    combined_dataset_dict = DatasetDict()\n", "\n", "    combined_feature_columns = []\n", "    for lang_code in languages_for_combined:\n", "        combined_feature_columns.append(f\"{lang_code}_input\")\n", "        combined_feature_columns.append(f\"{lang_code}_output\")\n", "\n", "    def refeature_for_combined(dataset_split):\n", "        new_examples = []\n", "        for example in dataset_split:\n", "            new_example = {col_name: example.get(col_name) for col_name in combined_feature_columns}\n", "            new_examples.append(new_example)\n", "\n", "        if not new_examples:\n", "             return Dataset.from_dict({col: [] for col in combined_feature_columns})\n", "        return Dataset.from_list(new_examples)\n", "\n", "    for split in ['train', 'val', 'test']:\n", "        print(f\"  Combining split: {split}\")\n", "        # Ensure the splits exist in the input dicts, provide empty dataset if not\n", "        empty_lima_split = Dataset.from_dict({col: [] for col in combined_feature_columns})\n", "        empty_bactrian_split = Dataset.from_dict({col: [] for col in combined_feature_columns})\n", "\n", "        lima_split_data = lima_dict.get(split, empty_lima_split)\n", "        bactrian_split_data = bactrian_dict.get(split, empty_bactrian_split)\n", "\n", "        processed_lima_for_concat = refeature_for_combined(lima_split_data)\n", "        processed_bactrian_for_concat = refeature_for_combined(bactrian_split_data)\n", "\n", "        datasets_to_concat = []\n", "        if len(processed_lima_for_concat) > 0:\n", "            datasets_to_concat.append(processed_lima_for_concat)\n", "        if len(processed_bactrian_for_concat) > 0:\n", "            datasets_to_concat.append(processed_bactrian_for_concat)\n", "\n", "        if not datasets_to_concat:\n", "            print(f\"    Both <PERSON> and Bact<PERSON> are empty or filtered out for split '{split}'. Combined split will be empty.\")\n", "            combined_dataset_dict[split] = Dataset.from_dict({col: [] for col in combined_feature_columns})\n", "        else:\n", "            combined_dataset_dict[split] = concatenate_datasets(datasets_to_concat)\n", "\n", "        print(f\"    Combined {split} split size: {len(combined_dataset_dict[split])}\")\n", "\n", "    print(\"--- Finished Combined Dataset Creation ---\")\n", "    return combined_dataset_dict"]}, {"cell_type": "code", "execution_count": 3, "id": "fc1562ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["--- Starting Lima-X Processing ---\n", "Processing Lima-X for languages: en, de, es, fr, it\n", "  Processing Lima-X language: EN\n", "  Processing Lima-X language: DE\n", "  Processing Lima-X language: ES\n", "  Processing Lima-X language: FR\n", "  Processing Lima-X language: IT\n", "  Lima-X processing complete. Splits: train=824, val=103, test=103\n", "--- Finished Lima-X Processing ---\n", "--- Starting Bactrian-X Processing ---\n", "Processing Bactrian-X for languages: en, de, es, fr, it\n", "  Processing Bactrian-X language: en\n", "    Loading from split: train\n", "  Processing Bactrian-X language: de\n", "    Loading from split: train\n", "  Processing Bactrian-X language: es\n", "    Loading from split: train\n", "  Processing Bactrian-X language: fr\n", "    Loading from split: train\n", "  Processing Bactrian-X language: it\n", "    Loading from split: train\n", "  Filtering records to include only those with data for all 5 languages...\n", "  Found 67017 complete records out of 67017 total records\n", "  Filtered out 0 incomplete records\n", "  Bactrian-X processing complete. Splits: train=53613, val=6702, test=6702\n", "--- Finished Bactrian-X Processing ---\n", "--- Starting Combined Dataset Creation ---\n", "  Combining split: train\n", "    Combined train split size: 54437\n", "  Combining split: val\n", "    Combined val split size: 6805\n", "  Combining split: test\n", "    Combined test split size: 6805\n", "--- Finished Combined Dataset Creation ---\n"]}], "source": ["# 1. Process Lima-X\n", "lima_processed_dict = process_lima_x(lima_language_codes)\n", "\n", "# 2. Process Bactrian-X\n", "bactrian_processed_dict = process_bactrian_x(bactrian_language_codes)\n", "\n", "# 3. Create the \"combined\" subset\n", "# These languages will be the columns in the combined dataset.\n", "languages_for_combined_subset = [\"de\", \"en\", \"es\", \"fr\", \"it\"]\n", "combined_final_dataset = create_combined_dataset(lima_processed_dict, bactrian_processed_dict, languages_for_combined_subset)"]}, {"cell_type": "code", "execution_count": 5, "id": "539edaab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Processed Datasets Summary ---\n", "\n", "Lima Subset Details:\n", "DatasetDict({\n", "    train: Dataset({\n", "        features: ['id_numeric', 'en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 824\n", "    })\n", "    val: Dataset({\n", "        features: ['id_numeric', 'en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 103\n", "    })\n", "    test: Dataset({\n", "        features: ['id_numeric', 'en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 103\n", "    })\n", "})\n", "Lima train example features: ['id_numeric', 'en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output']\n", "\n", "Bactrian Subset Details:\n", "DatasetDict({\n", "    train: Dataset({\n", "        features: ['en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output', 'id_original_bactrian'],\n", "        num_rows: 53613\n", "    })\n", "    val: Dataset({\n", "        features: ['en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output', 'id_original_bactrian'],\n", "        num_rows: 6702\n", "    })\n", "    test: Dataset({\n", "        features: ['en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output', 'id_original_bactrian'],\n", "        num_rows: 6702\n", "    })\n", "})\n", "Bactrian train example features: ['en_input', 'en_output', 'de_input', 'de_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output', 'id_original_bactrian']\n", "\n", "Combined Subset Details:\n", "DatasetDict({\n", "    train: Dataset({\n", "        features: ['de_input', 'de_output', 'en_input', 'en_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 54437\n", "    })\n", "    val: Dataset({\n", "        features: ['de_input', 'de_output', 'en_input', 'en_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 6805\n", "    })\n", "    test: Dataset({\n", "        features: ['de_input', 'de_output', 'en_input', 'en_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output'],\n", "        num_rows: 6805\n", "    })\n", "})\n", "Combined train example features: ['de_input', 'de_output', 'en_input', 'en_output', 'es_input', 'es_output', 'fr_input', 'fr_output', 'it_input', 'it_output']\n"]}], "source": ["# --- Displaying info about the processed datasets ---\n", "print(\"\\n--- Processed Datasets Summary ---\")\n", "print(\"\\nLima Subset Details:\")\n", "print(lima_processed_dict)\n", "if lima_processed_dict and 'train' in lima_processed_dict and len(lima_processed_dict['train']) > 0:\n", "    print(f\"Lima train example features: {list(lima_processed_dict['train'][0].keys())}\")\n", "\n", "print(\"\\nBactrian Subset Details:\")\n", "print(bactrian_processed_dict)\n", "if bactrian_processed_dict and 'train' in bactrian_processed_dict and len(bactrian_processed_dict['train']) > 0:\n", "    print(f\"Bactrian train example features: {list(bactrian_processed_dict['train'][0].keys())}\")\n", "\n", "print(\"\\nCombined Subset Details:\")\n", "print(combined_final_dataset)\n", "if combined_final_dataset and 'train' in combined_final_dataset and len(combined_final_dataset['train']) > 0:\n", "    print(f\"Combined train example features: {list(combined_final_dataset['train'][0].keys())}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "5e51acda", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- Pushing to Hugging Face Hub Repository: junkim100/multilingual_instruction_tuning_lima_bactrian ---\n", "Ensuring repository 'junkim100/multilingual_instruction_tuning_lima_bactrian' exists or creating it...\n", "Repository 'junkim100/multilingual_instruction_tuning_lima_bactrian' is ready.\n", "\n", "Pushing 'lima' configuration...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5b7233fe05bf4a568218129f1f0f4821", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ced2ac7657c0433f906aeb21537d7411", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "b5543e7344a04bc9b7924678359369d8", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1b2e5bf40c9845168696700edc1a071f", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "22002241dfc1424881ff1ccdab03265f", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "46206601187041e78febc92d99200d35", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["'lima' configuration pushed successfully.\n", "\n", "Pushing 'bactrian' configuration...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "169e08b7792c47268fc377fe52f0bfb7", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d4be8992ca3d4c6abb4195a151ab4e79", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/54 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "dd8e107813ed4ab7a15b2bc69120bc33", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "fcbd9969b05f4a829a39ba4b8ae6364e", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/7 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8466cf9c827640f19c949fd231d0fdcd", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2c2983cb251c4c8b8e4508ffbdcb6024", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/7 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["'bactrian' configuration pushed successfully.\n", "\n", "Pushing 'combined' configuration...\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "adaaea187c6142b9826e745447460255", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "412318f0c2cf469abd9f7d7f00aa0c04", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/55 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "830e964ede7f49c48e166a0d4a447cc2", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d746dd63a3b04802b12fbb0c810a11f7", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/7 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "90493923ee5a4044a8355138a3a55401", "version_major": 2, "version_minor": 0}, "text/plain": ["Uploading the dataset shards:   0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a9c77f2a96c8407abe47524695b05ed7", "version_major": 2, "version_minor": 0}, "text/plain": ["Creating parquet from Arrow format:   0%|          | 0/7 [00:00<?, ?ba/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "ffa1db1cd9db40dea463741d0e747f8e", "version_major": 2, "version_minor": 0}, "text/plain": ["README.md:   0%|          | 0.00/1.79k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["'combined' configuration pushed successfully.\n", "\n", "All configurations pushed to junkim100/multilingual_instruction_tuning_lima_bactrian (if not empty).\n", "You can load them using commands like: load_dataset('junkim100/multilingual_instruction_tuning_lima_bactrian', name='lima')\n"]}], "source": ["# 4. <PERSON>ush to Hugging Face Hub\n", "print(f\"\\n--- Pushing to Hugging Face Hub Repository: {TARGET_REPO_ID} ---\")\n", "try:\n", "    print(f\"Ensuring repository '{TARGET_REPO_ID}' exists or creating it...\")\n", "    create_repo(TARGET_REPO_ID, token=HF_TOKEN, exist_ok=True, repo_type=\"dataset\")\n", "    print(f\"Repository '{TARGET_REPO_ID}' is ready.\")\n", "\n", "    print(\"\\nPushing 'lima' configuration...\")\n", "    if lima_processed_dict and any(len(ds) > 0 for ds in lima_processed_dict.values()):\n", "        lima_processed_dict.push_to_hub(repo_id=TARGET_REPO_ID, config_name=\"lima\", token=HF_TOKEN)\n", "        print(\"'lima' configuration pushed successfully.\")\n", "    else:\n", "        print(\"'lima' configuration is empty or invalid, skipping push.\")\n", "\n", "    print(\"\\nPushing 'bactrian' configuration...\")\n", "    if bactrian_processed_dict and any(len(ds) > 0 for ds in bactrian_processed_dict.values()):\n", "        bactrian_processed_dict.push_to_hub(repo_id=TARGET_REPO_ID, config_name=\"bactrian\", token=HF_TOKEN)\n", "        print(\"'bactrian' configuration pushed successfully.\")\n", "    else:\n", "        print(\"'bactrian' configuration is empty or invalid, skipping push.\")\n", "\n", "    print(\"\\nPushing 'combined' configuration...\")\n", "    if combined_final_dataset and any(len(ds) > 0 for ds in combined_final_dataset.values()):\n", "        combined_final_dataset.push_to_hub(repo_id=TARGET_REPO_ID, config_name=\"combined\", token=HF_TOKEN)\n", "        print(\"'combined' configuration pushed successfully.\")\n", "    else:\n", "        print(\"'combined' configuration is empty or invalid, skipping push.\")\n", "\n", "    print(f\"\\nAll configurations pushed to {TARGET_REPO_ID} (if not empty).\")\n", "    print(f\"You can load them using commands like: load_dataset('{TARGET_REPO_ID}', name='lima')\")\n", "\n", "except Exception as e:\n", "    print(f\"An error occurred during push to Hub: {e}\")\n", "    print(\"Please ensure you are logged in (`huggingface-cli login`) and your token has write permissions.\")"]}], "metadata": {"kernelspec": {"display_name": "fuck", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 5}