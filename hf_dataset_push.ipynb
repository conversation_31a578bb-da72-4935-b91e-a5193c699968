from datasets import load_dataset, Dataset, DatasetDict, concatenate_datasets
import re
import pandas as pd # Optional: for displaying samples, not strictly needed for core logic
import random
from huggingface_hub import HfApi, create_repo # For pushing to hub

# -----------------------------------------------------------------------------
# 0. Configuration
# -----------------------------------------------------------------------------
HF_TOKEN = "*************************************"
HF_USERNAME = "junkim100"
TARGET_REPO_NAME = "multilingual_instruction_tuning_lima_bactrian"
TARGET_REPO_ID = f"{HF_USERNAME}/{TARGET_REPO_NAME}"

random.seed(42) # For reproducible splits
lima_language_codes = ["en", "de", "es", "fr", "it"]
# bactrian_language_codes = ["af", "ar", "az", "bn", "cs", "de", "en", "es", "et", "fa", "fi", "gl", "gu", "he", "hi", "hr", "id", "it", "ja", "ka", "kk", "km", "ko", "lt", "lv", "mk", "ml", "mn", "mr", "my", "ne", "nl", "pl", "ps", "pt", "ro", "ru", "si", "sl", "sv", "sw", "ta", "te", "th", "tl", "tr", "uk", "ur", "vi", "xh", "zh"]
# bactrian_language_codes = ["en", "de", "es", "fr", "it", "zh", "ko", "ja", "pt", "ar"]
bactrian_language_codes = ["en", "de", "es", "fr", "it"]

# -----------------------------------------------------------------------------
# Function to process Lima-X dataset
# -----------------------------------------------------------------------------
def process_lima_x(languages_to_process):
    print("--- Starting Lima-X Processing ---")
    dataset_name = "lamarr-org/Lima-X"
    aggregated_data_all = {}

    print(f"Processing Lima-X for languages: {', '.join(languages_to_process)}")

    for lang_code_upper in [lang.upper() for lang in languages_to_process]:
        print(f"  Processing Lima-X language: {lang_code_upper}")
        try:
            # trust_remote_code is needed for Lima-X configs
            lang_dataset_config = load_dataset(dataset_name, name=lang_code_upper, trust_remote_code=True)
        except Exception as e:
            print(f"    Could not load Lima-X subset {lang_code_upper}. Error: {e}. Skipping.")
            continue

        for original_split_name in lang_dataset_config.keys():
            for example in lang_dataset_config[original_split_name]:
                raw_id = example.get('id')
                conversations = example.get('conversations')

                if not raw_id or not conversations:
                    # print(f"    Skipping Lima example in {lang_code_upper}/{original_split_name} due to missing id or conversations.")
                    continue

                match = re.match(r'(\w+)_id_(\d+)_(\w+)', raw_id)
                if not match:
                    # print(f"    Skipping malformed Lima-X ID: {raw_id}")
                    continue

                _, base_id_str, _ = match.groups()
                base_id_numeric = int(base_id_str)

                human_value, gpt_value = None, None
                if isinstance(conversations, list) and len(conversations) >= 2:
                    temp_human, temp_gpt = None, None
                    for turn in conversations:
                        if isinstance(turn, dict) and 'from' in turn and 'value' in turn:
                            if turn['from'] == 'human' and temp_human is None:
                                temp_human = turn['value']
                            elif turn['from'] == 'gpt' and temp_gpt is None:
                                temp_gpt = turn['value']
                    human_value, gpt_value = temp_human, temp_gpt

                if human_value is None or gpt_value is None:
                    # print(f"    Skipping Lima-X ID {raw_id}, 'human' or 'gpt' turn missing/malformed.")
                    continue

                if base_id_numeric not in aggregated_data_all:
                    aggregated_data_all[base_id_numeric] = {'id_numeric': base_id_numeric}

                lang_code_lower = lang_code_upper.lower()
                aggregated_data_all[base_id_numeric][f'{lang_code_lower}_input'] = human_value
                aggregated_data_all[base_id_numeric][f'{lang_code_lower}_output'] = gpt_value

    final_lima_column_names = ['id_numeric']
    for lang_code_lower_col in languages_to_process:
        final_lima_column_names.append(f'{lang_code_lower_col}_input')
        final_lima_column_names.append(f'{lang_code_lower_col}_output')

    all_lima_records_list = []
    for base_id_numeric_key in sorted(aggregated_data_all.keys()):
        record_data_dict = aggregated_data_all[base_id_numeric_key]
        new_record = {col_name: record_data_dict.get(col_name) for col_name in final_lima_column_names}
        all_lima_records_list.append(new_record)

    if not all_lima_records_list:
        print("  No data processed for Lima-X. Returning empty DatasetDict.")
        empty_schema = {col: [] for col in final_lima_column_names}
        return DatasetDict({
            'train': Dataset.from_dict(empty_schema), 'val': Dataset.from_dict(empty_schema), 'test': Dataset.from_dict(empty_schema)
        })

    full_lima_dataset = Dataset.from_list(all_lima_records_list)

    train_test_split_val = full_lima_dataset.train_test_split(test_size=0.2, seed=42)
    lima_train = train_test_split_val['train']
    temp_val_test = train_test_split_val['test']

    val_test_split_final = temp_val_test.train_test_split(test_size=0.5, seed=42)
    lima_val = val_test_split_final['train']
    lima_test = val_test_split_final['test']

    lima_final_dict = DatasetDict({'train': lima_train, 'val': lima_val, 'test': lima_test})
    print(f"  Lima-X processing complete. Splits: train={len(lima_train)}, val={len(lima_val)}, test={len(lima_test)}")
    print("--- Finished Lima-X Processing ---")
    return lima_final_dict

# -----------------------------------------------------------------------------
# Function to process Bactrian-X dataset (CORRECTED)
# -----------------------------------------------------------------------------
def process_bactrian_x(languages_to_process):
    print("--- Starting Bactrian-X Processing ---")

    # Dictionary to aggregate data by original ID across languages
    aggregated_data_by_id = {}

    bactrian_subset_column_names = []
    for lang_code in languages_to_process:
        bactrian_subset_column_names.append(f"{lang_code}_input")
        bactrian_subset_column_names.append(f"{lang_code}_output")
    bactrian_id_column = 'id_original_bactrian'
    full_bactrian_schema_columns = bactrian_subset_column_names + [bactrian_id_column]

    print(f"Processing Bactrian-X for languages: {', '.join(languages_to_process)}")

    for lang_code in languages_to_process:
        print(f"  Processing Bactrian-X language: {lang_code}")

        current_dataset_name = "MBZUAI/Bactrian-X" # Main English Bactrian

        try:
            # Bactrian datasets may or may not need trust_remote_code,
            # but it's safer to include it if their loading scripts are custom.
            # For standard Parquet/JSON datasets, it might not be strictly needed.
            lang_specific_dataset_config = load_dataset(current_dataset_name, name=lang_code, trust_remote_code=True)
        except Exception as e:
            print(f"    Could not load Bactrian-X for {lang_code} using {current_dataset_name}. Error: {e}. Skipping.")
            continue

        # Bactrian-X datasets usually have a 'train' split or just one primary split.
        # Iterate through available splits, though typically it's just one.
        data_found_for_lang = False
        for split_name in lang_specific_dataset_config.keys():
            print(f"    Loading from split: {split_name}")
            for example in lang_specific_dataset_config[split_name]:
                instruction = example.get('instruction')
                # 'input' field in Bactrian-X is context, not user input
                context = example.get('input', '')
                output = example.get('output')
                original_id = example.get('id')

                if instruction is None or output is None or original_id is None:
                    # print(f"    Skipping Bactrian-X example (missing instruction/output/id). Lang: {lang_code}, ID: {original_id}")
                    continue

                full_instruction = f"{context}\n{instruction}".strip() if context else instruction

                # Initialize record for this ID if not exists
                if original_id not in aggregated_data_by_id:
                    aggregated_data_by_id[original_id] = {col: None for col in full_bactrian_schema_columns}
                    aggregated_data_by_id[original_id][bactrian_id_column] = original_id

                # Add data for this language
                aggregated_data_by_id[original_id][f'{lang_code}_input'] = full_instruction
                aggregated_data_by_id[original_id][f'{lang_code}_output'] = output
                data_found_for_lang = True

        if not data_found_for_lang:
             print(f"    No data records extracted for Bactrian-X language: {lang_code} from {current_dataset_name}")

    # Filter records to only include those that have data for ALL requested languages
    print(f"  Filtering records to include only those with data for all {len(languages_to_process)} languages...")
    complete_records = []
    total_records = len(aggregated_data_by_id)

    for record_id, record_data in aggregated_data_by_id.items():
        # Check if this record has non-null data for all languages
        has_all_languages = True
        for lang_code in languages_to_process:
            if (record_data.get(f'{lang_code}_input') is None or
                record_data.get(f'{lang_code}_output') is None):
                has_all_languages = False
                break

        if has_all_languages:
            complete_records.append(record_data)

    print(f"  Found {len(complete_records)} complete records out of {total_records} total records")
    print(f"  Filtered out {total_records - len(complete_records)} incomplete records")

    if not complete_records:
        print("  No complete records found for Bactrian-X after filtering. Returning empty DatasetDict.")
        empty_schema = {col: [] for col in full_bactrian_schema_columns}
        return DatasetDict({
            'train': Dataset.from_dict(empty_schema), 'val': Dataset.from_dict(empty_schema), 'test': Dataset.from_dict(empty_schema)
        })

    full_bactrian_dataset = Dataset.from_list(complete_records)

    train_test_split_val = full_bactrian_dataset.train_test_split(test_size=0.2, seed=42)
    bactrian_train = train_test_split_val['train']
    temp_val_test = train_test_split_val['test']

    val_test_split_final = temp_val_test.train_test_split(test_size=0.5, seed=42)
    bactrian_val = val_test_split_final['train']
    bactrian_test = val_test_split_final['test']

    bactrian_final_dict = DatasetDict({'train': bactrian_train, 'val': bactrian_val, 'test': bactrian_test})
    print(f"  Bactrian-X processing complete. Splits: train={len(bactrian_train)}, val={len(bactrian_val)}, test={len(bactrian_test)}")
    print("--- Finished Bactrian-X Processing ---")
    return bactrian_final_dict

# -----------------------------------------------------------------------------
# Function to create the combined dataset
# -----------------------------------------------------------------------------
def create_combined_dataset(lima_dict, bactrian_dict, languages_for_combined):
    print("--- Starting Combined Dataset Creation ---")
    combined_dataset_dict = DatasetDict()

    combined_feature_columns = []
    for lang_code in languages_for_combined:
        combined_feature_columns.append(f"{lang_code}_input")
        combined_feature_columns.append(f"{lang_code}_output")

    def refeature_for_combined(dataset_split):
        new_examples = []
        for example in dataset_split:
            new_example = {col_name: example.get(col_name) for col_name in combined_feature_columns}
            new_examples.append(new_example)

        if not new_examples:
             return Dataset.from_dict({col: [] for col in combined_feature_columns})
        return Dataset.from_list(new_examples)

    for split in ['train', 'val', 'test']:
        print(f"  Combining split: {split}")
        # Ensure the splits exist in the input dicts, provide empty dataset if not
        empty_lima_split = Dataset.from_dict({col: [] for col in combined_feature_columns})
        empty_bactrian_split = Dataset.from_dict({col: [] for col in combined_feature_columns})

        lima_split_data = lima_dict.get(split, empty_lima_split)
        bactrian_split_data = bactrian_dict.get(split, empty_bactrian_split)

        processed_lima_for_concat = refeature_for_combined(lima_split_data)
        processed_bactrian_for_concat = refeature_for_combined(bactrian_split_data)

        datasets_to_concat = []
        if len(processed_lima_for_concat) > 0:
            datasets_to_concat.append(processed_lima_for_concat)
        if len(processed_bactrian_for_concat) > 0:
            datasets_to_concat.append(processed_bactrian_for_concat)

        if not datasets_to_concat:
            print(f"    Both Lima and Bactrian are empty or filtered out for split '{split}'. Combined split will be empty.")
            combined_dataset_dict[split] = Dataset.from_dict({col: [] for col in combined_feature_columns})
        else:
            combined_dataset_dict[split] = concatenate_datasets(datasets_to_concat)

        print(f"    Combined {split} split size: {len(combined_dataset_dict[split])}")

    print("--- Finished Combined Dataset Creation ---")
    return combined_dataset_dict

# 1. Process Lima-X
lima_processed_dict = process_lima_x(lima_language_codes)

# 2. Process Bactrian-X
bactrian_processed_dict = process_bactrian_x(bactrian_language_codes)

# 3. Create the "combined" subset
# These languages will be the columns in the combined dataset.
languages_for_combined_subset = ["de", "en", "es", "fr", "it"]
combined_final_dataset = create_combined_dataset(lima_processed_dict, bactrian_processed_dict, languages_for_combined_subset)

# --- Displaying info about the processed datasets ---
print("\n--- Processed Datasets Summary ---")
print("\nLima Subset Details:")
print(lima_processed_dict)
if lima_processed_dict and 'train' in lima_processed_dict and len(lima_processed_dict['train']) > 0:
    print(f"Lima train example features: {list(lima_processed_dict['train'][0].keys())}")

print("\nBactrian Subset Details:")
print(bactrian_processed_dict)
if bactrian_processed_dict and 'train' in bactrian_processed_dict and len(bactrian_processed_dict['train']) > 0:
    print(f"Bactrian train example features: {list(bactrian_processed_dict['train'][0].keys())}")

print("\nCombined Subset Details:")
print(combined_final_dataset)
if combined_final_dataset and 'train' in combined_final_dataset and len(combined_final_dataset['train']) > 0:
    print(f"Combined train example features: {list(combined_final_dataset['train'][0].keys())}")

# 4. Push to Hugging Face Hub
print(f"\n--- Pushing to Hugging Face Hub Repository: {TARGET_REPO_ID} ---")
try:
    print(f"Ensuring repository '{TARGET_REPO_ID}' exists or creating it...")
    create_repo(TARGET_REPO_ID, token=HF_TOKEN, exist_ok=True, repo_type="dataset")
    print(f"Repository '{TARGET_REPO_ID}' is ready.")

    print("\nPushing 'lima' configuration...")
    if lima_processed_dict and any(len(ds) > 0 for ds in lima_processed_dict.values()):
        lima_processed_dict.push_to_hub(repo_id=TARGET_REPO_ID, config_name="lima", token=HF_TOKEN)
        print("'lima' configuration pushed successfully.")
    else:
        print("'lima' configuration is empty or invalid, skipping push.")

    print("\nPushing 'bactrian' configuration...")
    if bactrian_processed_dict and any(len(ds) > 0 for ds in bactrian_processed_dict.values()):
        bactrian_processed_dict.push_to_hub(repo_id=TARGET_REPO_ID, config_name="bactrian", token=HF_TOKEN)
        print("'bactrian' configuration pushed successfully.")
    else:
        print("'bactrian' configuration is empty or invalid, skipping push.")

    print("\nPushing 'combined' configuration...")
    if combined_final_dataset and any(len(ds) > 0 for ds in combined_final_dataset.values()):
        combined_final_dataset.push_to_hub(repo_id=TARGET_REPO_ID, config_name="combined", token=HF_TOKEN)
        print("'combined' configuration pushed successfully.")
    else:
        print("'combined' configuration is empty or invalid, skipping push.")

    print(f"\nAll configurations pushed to {TARGET_REPO_ID} (if not empty).")
    print(f"You can load them using commands like: load_dataset('{TARGET_REPO_ID}', name='lima')")

except Exception as e:
    print(f"An error occurred during push to Hub: {e}")
    print("Please ensure you are logged in (`huggingface-cli login`) and your token has write permissions.")