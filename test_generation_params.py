#!/usr/bin/env python3
"""
Test different generation parameters to get better responses
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

def test_generation_parameters():
    """Test different generation parameters"""
    
    print("🧪 Testing Generation Parameters")
    print("=" * 50)
    
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    base_model_name = "meta-llama/Meta-Llama-3.1-8B"
    
    print(f"📂 Loading model from: {model_path}")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(model_path)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    print(f"✅ Tokenizer loaded - Vocab size: {len(tokenizer)}")
    
    # Load base model
    print("🔄 Loading base model...")
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_name,
        torch_dtype=torch.bfloat16,
        device_map="auto",
        trust_remote_code=True
    )
    
    # Resize embeddings
    base_model.resize_token_embeddings(len(tokenizer))
    print(f"🔧 Resized embeddings to {len(tokenizer)} tokens")
    
    # Load LoRA adapter
    print("🔄 Loading LoRA adapter...")
    model = PeftModel.from_pretrained(base_model, model_path)
    print("✅ LoRA adapter loaded")
    
    # Test prompt
    test_conversation = [
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "Come si dice hello world in inglese?"}
    ]
    
    prompt = tokenizer.apply_chat_template(
        test_conversation,
        tokenize=False,
        add_generation_prompt=True
    )
    
    print(f"\n📝 Test prompt:")
    print("-" * 30)
    print(prompt)
    print("-" * 30)
    
    # Test different generation parameters
    generation_configs = [
        {
            "name": "Conservative",
            "params": {
                "max_new_tokens": 200,
                "temperature": 0.7,
                "do_sample": True,
                "top_p": 0.9,
                "repetition_penalty": 1.1,
            }
        },
        {
            "name": "More Creative",
            "params": {
                "max_new_tokens": 300,
                "temperature": 0.8,
                "do_sample": True,
                "top_p": 0.95,
                "repetition_penalty": 1.05,
            }
        },
        {
            "name": "Deterministic",
            "params": {
                "max_new_tokens": 250,
                "temperature": 0.1,
                "do_sample": True,
                "top_p": 0.9,
                "repetition_penalty": 1.1,
            }
        }
    ]
    
    # Tokenize input
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
    
    for i, config in enumerate(generation_configs, 1):
        print(f"\n{i}. Testing {config['name']} Parameters")
        print("-" * 40)
        print(f"Parameters: {config['params']}")
        
        try:
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    pad_token_id=tokenizer.eos_token_id,
                    eos_token_id=[
                        tokenizer.eos_token_id,
                        tokenizer.convert_tokens_to_ids("</in_translation>"),
                        tokenizer.convert_tokens_to_ids("</out_translation>")
                    ],
                    **config['params']
                )
            
            # Decode response
            input_length = inputs["input_ids"].shape[1]
            new_tokens = outputs[0][input_length:]
            response = tokenizer.decode(new_tokens, skip_special_tokens=False).strip()
            
            print(f"\n📤 Generated response:")
            print("=" * 30)
            print(response)
            print("=" * 30)
            
            # Analyze response
            if "<in_translation>" in response:
                print("✅ Contains <in_translation>")
            else:
                print("❌ Missing <in_translation>")
                
            if "</in_translation>" in response:
                print("✅ Contains </in_translation>")
            else:
                print("❌ Missing </in_translation>")
                
            if "<out_translation>" in response:
                print("✅ Contains <out_translation>")
            else:
                print("❌ Missing <out_translation>")
                
            if "</out_translation>" in response:
                print("✅ Contains </out_translation>")
            else:
                print("❌ Missing </out_translation>")
                
            # Count tokens
            response_tokens = tokenizer.tokenize(response)
            print(f"📊 Response length: {len(response_tokens)} tokens")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    test_generation_parameters()
