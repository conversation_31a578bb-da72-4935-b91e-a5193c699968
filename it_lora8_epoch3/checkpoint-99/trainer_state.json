{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.0, "eval_steps": 100, "global_step": 99, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.031007751937984496, "grad_norm": 7.2354302406311035, "learning_rate": 0.0, "loss": 4.3145, "step": 1}, {"epoch": 0.15503875968992248, "grad_norm": 7.822229385375977, "learning_rate": 0.0001, "loss": 4.2793, "step": 5}, {"epoch": 0.31007751937984496, "grad_norm": 4.465281963348389, "learning_rate": 0.0001, "loss": 3.3784, "step": 10}, {"epoch": 0.46511627906976744, "grad_norm": 2.054680347442627, "learning_rate": 0.0001, "loss": 2.2481, "step": 15}, {"epoch": 0.6201550387596899, "grad_norm": 1.26651132106781, "learning_rate": 0.0001, "loss": 1.8223, "step": 20}, {"epoch": 0.7751937984496124, "grad_norm": 0.8883180618286133, "learning_rate": 0.0001, "loss": 1.6703, "step": 25}, {"epoch": 0.9302325581395349, "grad_norm": 0.5542085766792297, "learning_rate": 0.0001, "loss": 1.5173, "step": 30}, {"epoch": 1.062015503875969, "grad_norm": 0.7313990592956543, "learning_rate": 0.0001, "loss": 1.4227, "step": 35}, {"epoch": 1.2170542635658914, "grad_norm": 0.4753330945968628, "learning_rate": 0.0001, "loss": 1.3725, "step": 40}, {"epoch": 1.372093023255814, "grad_norm": 0.5996339917182922, "learning_rate": 0.0001, "loss": 1.2863, "step": 45}, {"epoch": 1.5271317829457365, "grad_norm": 0.8013227581977844, "learning_rate": 0.0001, "loss": 1.2079, "step": 50}, {"epoch": 1.6821705426356588, "grad_norm": 0.29963281750679016, "learning_rate": 0.0001, "loss": 1.1915, "step": 55}, {"epoch": 1.8372093023255816, "grad_norm": 0.24620458483695984, "learning_rate": 0.0001, "loss": 1.1154, "step": 60}, {"epoch": 1.9922480620155039, "grad_norm": 0.3372691869735718, "learning_rate": 0.0001, "loss": 1.1474, "step": 65}, {"epoch": 2.124031007751938, "grad_norm": 0.2038220316171646, "learning_rate": 0.0001, "loss": 1.1357, "step": 70}, {"epoch": 2.2790697674418605, "grad_norm": 0.17976151406764984, "learning_rate": 0.0001, "loss": 1.1519, "step": 75}, {"epoch": 2.434108527131783, "grad_norm": 0.19190183281898499, "learning_rate": 0.0001, "loss": 1.1236, "step": 80}, {"epoch": 2.5891472868217056, "grad_norm": 0.18479135632514954, "learning_rate": 0.0001, "loss": 1.0835, "step": 85}, {"epoch": 2.744186046511628, "grad_norm": 0.1741180568933487, "learning_rate": 0.0001, "loss": 1.1203, "step": 90}, {"epoch": 2.89922480620155, "grad_norm": 0.17185117304325104, "learning_rate": 0.0001, "loss": 1.0642, "step": 95}], "logging_steps": 5, "max_steps": 99, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 136404118274048.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}