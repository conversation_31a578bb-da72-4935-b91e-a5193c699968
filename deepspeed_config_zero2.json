{"train_batch_size": "auto", "train_micro_batch_size_per_gpu": "auto", "gradient_accumulation_steps": "auto", "gradient_clipping": 0.5, "steps_per_print": 10, "wall_clock_breakdown": false, "bf16": {"enabled": true}, "zero_optimization": {"stage": 2, "allgather_partitions": true, "allgather_bucket_size": 500000000.0, "overlap_comm": true, "reduce_scatter": true, "reduce_bucket_size": 500000000.0, "contiguous_gradients": true, "cpu_offload": false}, "optimizer": {"type": "AdamW", "params": {"lr": "auto", "betas": "auto", "eps": "auto", "weight_decay": "auto"}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": "auto", "warmup_max_lr": "auto", "warmup_num_steps": "auto"}}, "activation_checkpointing": {"partition_activations": true, "cpu_checkpointing": false, "contiguous_memory_optimization": true, "number_checkpoints": 4, "synchronize_checkpoint_boundary": true, "profile": false}, "memory_optimization": {"deepspeed_activation_checkpointing": true}, "comms_logger": {"enabled": false}}