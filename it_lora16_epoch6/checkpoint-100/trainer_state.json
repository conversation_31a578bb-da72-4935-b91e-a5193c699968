{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 3.0310077519379846, "eval_steps": 100, "global_step": 100, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.031007751937984496, "grad_norm": 15.115035057067871, "learning_rate": 0.0, "loss": 4.3841, "step": 1}, {"epoch": 0.15503875968992248, "grad_norm": 10.538762092590332, "learning_rate": 6.989700043360187e-05, "loss": 4.2218, "step": 5}, {"epoch": 0.31007751937984496, "grad_norm": 3.7469191551208496, "learning_rate": 9.999999999999999e-05, "loss": 2.8634, "step": 10}, {"epoch": 0.46511627906976744, "grad_norm": 1.1965335607528687, "learning_rate": 0.0001, "loss": 1.8235, "step": 15}, {"epoch": 0.6201550387596899, "grad_norm": 1.070713758468628, "learning_rate": 0.0001, "loss": 1.5067, "step": 20}, {"epoch": 0.7751937984496124, "grad_norm": 0.8797351717948914, "learning_rate": 0.0001, "loss": 1.3786, "step": 25}, {"epoch": 0.9302325581395349, "grad_norm": 0.6276625990867615, "learning_rate": 0.0001, "loss": 1.2775, "step": 30}, {"epoch": 1.062015503875969, "grad_norm": 0.4060195982456207, "learning_rate": 0.0001, "loss": 1.216, "step": 35}, {"epoch": 1.2170542635658914, "grad_norm": 0.28143900632858276, "learning_rate": 0.0001, "loss": 1.2042, "step": 40}, {"epoch": 1.372093023255814, "grad_norm": 0.23164102435112, "learning_rate": 0.0001, "loss": 1.1549, "step": 45}, {"epoch": 1.5271317829457365, "grad_norm": 0.22547908127307892, "learning_rate": 0.0001, "loss": 1.1183, "step": 50}, {"epoch": 1.6821705426356588, "grad_norm": 0.18721862137317657, "learning_rate": 0.0001, "loss": 1.1296, "step": 55}, {"epoch": 1.8372093023255816, "grad_norm": 0.16096094250679016, "learning_rate": 0.0001, "loss": 1.0703, "step": 60}, {"epoch": 1.9922480620155039, "grad_norm": 0.2067575305700302, "learning_rate": 0.0001, "loss": 1.1074, "step": 65}, {"epoch": 2.124031007751938, "grad_norm": 0.18698015809059143, "learning_rate": 0.0001, "loss": 1.0911, "step": 70}, {"epoch": 2.2790697674418605, "grad_norm": 0.18081197142601013, "learning_rate": 0.0001, "loss": 1.1054, "step": 75}, {"epoch": 2.434108527131783, "grad_norm": 0.18589383363723755, "learning_rate": 0.0001, "loss": 1.0823, "step": 80}, {"epoch": 2.5891472868217056, "grad_norm": 0.18841643631458282, "learning_rate": 0.0001, "loss": 1.0436, "step": 85}, {"epoch": 2.744186046511628, "grad_norm": 0.1985272914171219, "learning_rate": 0.0001, "loss": 1.0815, "step": 90}, {"epoch": 2.89922480620155, "grad_norm": 0.1803186982870102, "learning_rate": 0.0001, "loss": 1.0284, "step": 95}, {"epoch": 3.0310077519379846, "grad_norm": 0.3525048792362213, "learning_rate": 0.0001, "loss": 1.0206, "step": 100}, {"epoch": 3.0310077519379846, "eval_loss": 1.1047457456588745, "eval_runtime": 4.401, "eval_samples_per_second": 11.815, "eval_steps_per_second": 0.454, "step": 100}], "logging_steps": 5, "max_steps": 198, "num_input_tokens_seen": 0, "num_train_epochs": 6, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 39145182199808.0, "train_batch_size": 1, "trial_name": null, "trial_params": null}