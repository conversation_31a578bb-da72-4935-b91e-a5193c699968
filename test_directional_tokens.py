#!/usr/bin/env python3
"""
Test script to demonstrate the new directional translation tokens
Note: Current model was trained with old tokens, so this shows the intended behavior
"""

from transformers import AutoTokenizer
import j<PERSON>

def test_directional_tokens():
    """Test the new directional translation token setup"""
    
    print("🎯 Testing Directional Translation Tokens")
    print("=" * 60)
    
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    
    print(f"📂 Loading tokenizer from: {model_path}")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print(f"✅ Tokenizer loaded - Vocab size: {len(tokenizer)}")
    except Exception as e:
        print(f"❌ Error loading tokenizer: {e}")
        return False
    
    # Test directional tokens
    directional_tokens = [
        "<en_translation>", "</en_translation>",
        "<it_translation>", "</it_translation>"
    ]
    
    print(f"\n🔍 Checking directional translation tokens...")
    print("-" * 40)
    
    all_tokens_found = True
    token_ids = {}
    
    for token in directional_tokens:
        if token in tokenizer.get_vocab():
            token_id = tokenizer.convert_tokens_to_ids(token)
            token_ids[token] = token_id
            print(f"✅ {token:<20} - ID: {token_id}")
        else:
            print(f"❌ {token:<20} - NOT FOUND")
            all_tokens_found = False
    
    if not all_tokens_found:
        print("\n⚠️  WARNING: Not all directional tokens found!")
        print("   This is expected since the current model was trained with old tokens.")
        print("   You need to retrain with the updated train.py to use directional tokens.")
        return False
    
    # Test chat template with directional tokens
    print(f"\n🧪 Testing Chat Template with Directional Tokens")
    print("-" * 50)
    
    test_conversations = [
        {
            "name": "Italian Translation Request",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Come si dice 'hello world' in inglese?"}
            ]
        },
        {
            "name": "Italian Math Question", 
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Quanto fa 2 + 2?"}
            ]
        }
    ]
    
    for i, test in enumerate(test_conversations, 1):
        print(f"\n{i}. {test['name']}")
        print(f"   Input: '{test['messages'][-1]['content']}'")
        
        try:
            formatted = tokenizer.apply_chat_template(
                test['messages'],
                tokenize=False,
                add_generation_prompt=True
            )
            
            # Check if it starts with <en_translation>
            if "<en_translation>" in formatted:
                print("   ✅ Starts with <en_translation> token")
                
                # Show the end of the formatted prompt
                lines = formatted.strip().split('\n')
                last_lines = lines[-2:] if len(lines) >= 2 else lines
                print("   📝 Prompt ending:")
                for line in last_lines:
                    print(f"      {line}")
            else:
                print("   ❌ Does not start with <en_translation> token")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return all_tokens_found

def show_expected_behavior():
    """Show what the expected behavior should be with directional tokens"""
    
    print("\n" + "=" * 60)
    print("🎯 Expected Behavior with Directional Tokens")
    print("=" * 60)
    
    print("\n📝 Example Conversation Flow:")
    print("-" * 30)
    
    print("👤 User (Italian): 'Come si dice hello world in inglese?'")
    print()
    print("🤖 Assistant (Expected Response):")
    print("   <en_translation>How do you say hello world in English?</en_translation>")
    print()
    print("   The phrase 'hello world' is already in English! It's a common")
    print("   greeting that means 'ciao mondo' in Italian.")
    print()
    print("   <it_translation>La frase 'hello world' è già in inglese! È un")
    print("   saluto comune che significa 'ciao mondo' in italiano.</it_translation>")
    
    print("\n🔄 Translation Flow:")
    print("   1. 🇮🇹 Italian input → 🇺🇸 English translation")
    print("   2. 🇺🇸 English reasoning/response")  
    print("   3. 🇺🇸 English response → 🇮🇹 Italian translation")
    
    print("\n✅ Benefits:")
    print("   • Clear language separation")
    print("   • No token ambiguity")
    print("   • Better training signal")
    print("   • Consistent behavior")

def show_retraining_instructions():
    """Show instructions for retraining with directional tokens"""
    
    print("\n" + "=" * 60)
    print("🚀 Retraining Instructions")
    print("=" * 60)
    
    print("\n📋 To use directional tokens, you need to retrain:")
    print()
    print("1. ✅ Updated train.py (already done)")
    print("   - New tokens: <en_translation>, </en_translation>, <it_translation>, </it_translation>")
    print("   - Updated response format")
    print()
    print("2. 🔄 Run retraining:")
    print("   deepspeed --num_gpus=4 train.py")
    print()
    print("3. 🧪 Test new model:")
    print("   python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-XX")
    print()
    print("4. ✅ Expected output format:")
    print("   <en_translation>English here</en_translation>")
    print("   English reasoning...")
    print("   <it_translation>Italian here</it_translation>")
    
    print("\n⏱️  Training Time: ~2-3 hours on 4 GPUs")
    print("💾 Disk Space: ~15GB for checkpoints")
    print("🔧 Memory: ~24GB per GPU with DeepSpeed ZeRO-3")

if __name__ == "__main__":
    print("🎯 Directional Translation Tokens Test Suite")
    print("=" * 60)
    
    # Test current setup
    tokens_found = test_directional_tokens()
    
    # Show expected behavior
    show_expected_behavior()
    
    # Show retraining instructions
    show_retraining_instructions()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    
    if tokens_found:
        print("🎉 SUCCESS: Directional tokens are configured!")
        print("✅ Ready to use the new directional translation system")
    else:
        print("⚠️  CONFIGURATION UPDATED: Directional tokens configured but not trained")
        print("🔄 Need to retrain model to use directional tokens")
        print("📝 Current model still uses old ambiguous tokens")
    
    print("\n🎯 The directional token approach is the CORRECT solution!")
    print("   It eliminates ambiguity and provides clear language separation.")
    print("   This will result in much better translation quality and consistency.")
