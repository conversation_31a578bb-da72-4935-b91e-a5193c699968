#!/usr/bin/env python3
"""
Test script to verify the retrained model with in_translation and out_translation tokens
"""

from transformers import AutoTokenizer
import j<PERSON>

def test_retrained_tokens():
    """Test the retrained model with new in_translation/out_translation tokens"""
    
    print("🎯 Testing Retrained Model with in_translation/out_translation Tokens")
    print("=" * 70)
    
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    
    print(f"📂 Loading tokenizer from: {model_path}")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        print(f"✅ Tokenizer loaded - Vocab size: {len(tokenizer)}")
    except Exception as e:
        print(f"❌ Error loading tokenizer: {e}")
        return False
    
    # Test the new tokens
    expected_tokens = [
        "<in_translation>", "</in_translation>",
        "<out_translation>", "</out_translation>"
    ]
    
    print(f"\n🔍 Checking new translation tokens...")
    print("-" * 40)
    
    all_tokens_found = True
    token_ids = {}
    
    for token in expected_tokens:
        if token in tokenizer.get_vocab():
            token_id = tokenizer.convert_tokens_to_ids(token)
            token_ids[token] = token_id
            print(f"✅ {token:<20} - ID: {token_id}")
        else:
            print(f"❌ {token:<20} - NOT FOUND")
            all_tokens_found = False
    
    if not all_tokens_found:
        print("\n❌ ERROR: Not all tokens found!")
        return False
    
    # Test chat template
    print(f"\n🧪 Testing Chat Template")
    print("-" * 30)
    
    test_conversations = [
        {
            "name": "Italian Translation Request",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Come si dice 'hello world' in inglese?"}
            ]
        },
        {
            "name": "Italian Math Question", 
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Quanto fa 2 + 2?"}
            ]
        },
        {
            "name": "Italian General Question",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Che tempo fa oggi?"}
            ]
        }
    ]
    
    for i, test in enumerate(test_conversations, 1):
        print(f"\n{i}. {test['name']}")
        print(f"   Input: '{test['messages'][-1]['content']}'")
        
        try:
            formatted = tokenizer.apply_chat_template(
                test['messages'],
                tokenize=False,
                add_generation_prompt=True
            )
            
            # Check if it starts with <in_translation>
            if "<in_translation>" in formatted:
                print("   ✅ Starts with <in_translation> token")
                
                # Show the end of the formatted prompt
                lines = formatted.strip().split('\n')
                last_lines = lines[-2:] if len(lines) >= 2 else lines
                print("   📝 Prompt ending:")
                for line in last_lines:
                    print(f"      {line}")
            else:
                print("   ❌ Does not start with <in_translation> token")
                print("   📝 Actual ending:")
                lines = formatted.strip().split('\n')
                last_lines = lines[-3:] if len(lines) >= 3 else lines
                for line in last_lines:
                    print(f"      {line}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    return all_tokens_found

def show_expected_format():
    """Show the expected response format"""
    
    print("\n" + "=" * 70)
    print("🎯 Expected Response Format")
    print("=" * 70)
    
    print("\n📝 Training Format:")
    print("response = f\"<in_translation>{translation_input}</in_translation>\\n\\n{output_content}\\n\\n<out_translation>{translation_output}</out_translation>\"")
    
    print("\n🔄 Expected Conversation Flow:")
    print("-" * 40)
    
    print("👤 User (Italian): 'Come si dice hello world in inglese?'")
    print()
    print("🤖 Assistant (Expected Response):")
    print("   <in_translation>How do you say hello world in English?</in_translation>")
    print()
    print("   The phrase 'hello world' is already in English! It's a common")
    print("   greeting that programmers use when learning a new language.")
    print()
    print("   <out_translation>La frase 'hello world' è già in inglese! È un")
    print("   saluto comune che i programmatori usano quando imparano un nuovo linguaggio.</out_translation>")
    
    print("\n🎯 Token Flow:")
    print("   1. 🇮🇹 Italian input → 🇺🇸 <in_translation>English translation</in_translation>")
    print("   2. 🇺🇸 English reasoning/response (untagged)")  
    print("   3. 🇺🇸 English response → 🇮🇹 <out_translation>Italian translation</out_translation>")
    
    print("\n✅ Benefits:")
    print("   • Clear input/output separation")
    print("   • No ambiguity about translation direction")
    print("   • Natural English flow in the middle")
    print("   • Consistent token usage")

def test_cli_integration():
    """Test CLI integration"""
    
    print("\n" + "=" * 70)
    print("🚀 CLI Integration Test")
    print("=" * 70)
    
    print("\n📋 To test the CLI with the retrained model:")
    print()
    print("1. 🔄 Start the CLI:")
    print("   python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66")
    print()
    print("2. 🧪 Test with Italian inputs:")
    print("   👤 'Come si dice hello world in inglese?'")
    print("   👤 'Quanto fa 2 + 2?'")
    print("   👤 'Che tempo fa oggi?'")
    print()
    print("3. ✅ Expected response format:")
    print("   🤖 <in_translation>English translation of your Italian input</in_translation>")
    print("   🤖 English reasoning and response content...")
    print("   🤖 <out_translation>Italian translation of the final response</out_translation>")
    print()
    print("4. 🔍 What to look for:")
    print("   • Response starts with <in_translation>")
    print("   • English content in the middle (untagged)")
    print("   • Response ends with <out_translation>")
    print("   • Clear language separation")

if __name__ == "__main__":
    print("🎯 Retrained Model Test Suite")
    print("=" * 70)
    
    # Test tokens and template
    tokens_ok = test_retrained_tokens()
    
    # Show expected format
    show_expected_format()
    
    # Show CLI test instructions
    test_cli_integration()
    
    print("\n" + "=" * 70)
    print("📋 SUMMARY")
    print("=" * 70)
    
    if tokens_ok:
        print("🎉 SUCCESS: Retrained model is properly configured!")
        print("✅ All in_translation/out_translation tokens found")
        print("✅ Chat template starts with <in_translation>")
        print("✅ Ready to test with CLI")
        print()
        print("🚀 Next step: Test with CLI to verify model behavior")
        print("   python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66")
    else:
        print("❌ ISSUES: Token configuration problems detected")
        print("🔧 Check tokenizer and model files")
    
    print("\n🎯 The retrained model should now use the correct token format!")
    print("   Input translation → English reasoning → Output translation")
