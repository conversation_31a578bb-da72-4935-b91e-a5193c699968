# ML Training Environment with CUDA 12.6
FROM nvidia/cuda:12.6.0-devel-ubuntu22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3.11-dev \
    python3.11-distutils \
    python3-pip \
    git \
    wget \
    curl \
    build-essential \
    cmake \
    ninja-build \
    libopenmpi-dev \
    libaio-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Create symbolic links for python
RUN ln -sf /usr/bin/python3.11 /usr/bin/python3 && \
    ln -sf /usr/bin/python3.11 /usr/bin/python

# Upgrade pip
RUN python -m pip install --upgrade pip setuptools wheel

# Install PyTorch 2.7.0 with CUDA 12.6 support
RUN pip install torch==2.7.0 torchvision torchaudio --index-url https://download.pytorch.org/whl/cu126

# Install exact package versions as requested
RUN pip install transformers==4.52.3
RUN pip install numpy==1.26.3

# Install DeepSpeed (may need compilation for CUDA 12.6)
RUN pip install deepspeed==0.16.9

# Install additional ML dependencies
RUN pip install \
    accelerate \
    datasets \
    tokenizers \
    peft \
    bitsandbytes \
    wandb \
    scipy \
    scikit-learn \
    matplotlib \
    seaborn \
    jupyter \
    ipykernel \
    tqdm \
    psutil

# Install development tools
RUN pip install \
    black \
    flake8 \
    pytest \
    ipdb

# Create working directory
WORKDIR /workspace

# Create non-root user
RUN useradd -m -s /bin/bash mluser && \
    chown -R mluser:mluser /workspace

# Switch to non-root user
USER mluser

# Set environment for user
ENV PATH=/home/<USER>/.local/bin:${PATH}

# Verify installations
RUN python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}')"
RUN python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
RUN python -c "import deepspeed; print(f'DeepSpeed: {deepspeed.__version__}')"
RUN python -c "import numpy; print(f'NumPy: {numpy.__version__}')"

# Default command
CMD ["/bin/bash"]
