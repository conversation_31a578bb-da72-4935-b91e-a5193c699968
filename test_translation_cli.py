#!/usr/bin/env python3
"""
Simple test script to verify translation functionality with the CLI
"""

import subprocess
import time
import os

def test_translation_cli():
    """Test the CLI with specific translation prompts"""
    
    print("🧪 Testing Translation CLI")
    print("=" * 50)
    
    # Test prompts that should trigger translation behavior
    test_prompts = [
        "Translate 'Hello world' to Italian",
        "Please translate: 'Good morning' to Italian",
        "Convert to Italian: 'Thank you very much'",
        "Italian translation of 'How are you?'",
    ]
    
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    system_prompt = "You are a translation assistant. Always use <translation> tags around your Italian translations."
    
    print(f"📂 Model: {model_path}")
    print(f"💭 System prompt: {system_prompt}")
    print("\n🔄 Testing translation prompts...")
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n{i}. Testing: '{prompt}'")
        print("-" * 40)
        
        # Create a simple test script that sends one prompt and exits
        test_script = f'''
import sys
sys.path.append('.')
from chat_cli import ChatBot

try:
    chatbot = ChatBot(
        model_path="{model_path}",
        system_prompt="{system_prompt}",
        use_4bit=False,
        max_length=2048
    )
    
    response = chatbot.generate_response("{prompt}")
    print("RESPONSE:", response)
    
except Exception as e:
    print("ERROR:", e)
'''
        
        # Write test script
        with open(f"temp_test_{i}.py", "w") as f:
            f.write(test_script)
        
        try:
            # Run test
            result = subprocess.run(
                ["python", f"temp_test_{i}.py"],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                output = result.stdout
                if "RESPONSE:" in output:
                    response = output.split("RESPONSE:", 1)[1].strip()
                    print(f"✅ Response: {response}")
                    
                    # Check if translation tags are used
                    if "<translation>" in response:
                        print("🎯 Uses translation tags!")
                    else:
                        print("⚠️  No translation tags found")
                else:
                    print("❌ No response found")
            else:
                print(f"❌ Error: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ Test timed out")
        except Exception as e:
            print(f"❌ Test failed: {e}")
        finally:
            # Clean up
            if os.path.exists(f"temp_test_{i}.py"):
                os.remove(f"temp_test_{i}.py")

def quick_manual_test():
    """Quick manual test instructions"""
    
    print("\n" + "=" * 60)
    print("🚀 Quick Manual Test Instructions")
    print("=" * 60)
    
    print("\n1. Run the CLI:")
    print('   python chat_cli.py ./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66 \\')
    print('     --system-prompt "You are a translation assistant. Use <translation> tags for translations."')
    
    print("\n2. Test these prompts:")
    prompts = [
        "Translate 'Hello' to Italian",
        "Italian for 'Good morning'",
        "Convert to Italian: 'Thank you'",
    ]
    
    for prompt in prompts:
        print(f"   👤 {prompt}")
    
    print("\n3. Expected responses should include:")
    print("   🤖 <translation>Ciao</translation>")
    print("   🤖 <translation>Buongiorno</translation>") 
    print("   🤖 <translation>Grazie</translation>")
    
    print("\n4. Commands to try:")
    print("   - 'history' - See conversation")
    print("   - 'reset' - Clear history")
    print("   - 'quit' - Exit")

if __name__ == "__main__":
    print("🤖 Translation CLI Testing Suite")
    print("=" * 60)
    
    # Check if model exists
    model_path = "./llama-reasoning-finetuned-9k-deepspeed/checkpoint-66"
    if not os.path.exists(model_path):
        print(f"❌ Model not found: {model_path}")
        print("Please ensure the model path is correct.")
        exit(1)
    
    print("✅ Model path exists")
    
    # Run automated tests
    print("\n🔬 Running automated tests...")
    test_translation_cli()
    
    # Show manual test instructions
    quick_manual_test()
    
    print("\n" + "=" * 60)
    print("🎉 Testing complete! Try the manual test for interactive verification.")
