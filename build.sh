#!/bin/bash

# Build script for translation training Docker environment
set -e

echo "🐳 Building Translation Training Docker Environment with CUDA 12.6"
echo "=================================================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if nvidia-docker is available
if ! docker run --rm --gpus all nvidia/cuda:12.6-base-ubuntu22.04 nvidia-smi > /dev/null 2>&1; then
    echo "❌ NVIDIA Docker runtime not available. Please install nvidia-docker2."
    exit 1
fi

echo "✅ Docker and NVIDIA runtime are available"

# Create necessary directories
echo "📁 Creating cache and checkpoint directories..."
mkdir -p cache/huggingface
mkdir -p cache/transformers  
mkdir -p cache/torch
mkdir -p checkpoints
mkdir -p logs

echo "🔨 Building Docker image..."
docker-compose build translation-trainer

echo "✅ Build completed successfully!"
echo ""
echo "🚀 To start the environment, run:"
echo "   ./run.sh"
echo ""
echo "📊 To start with <PERSON>py<PERSON> notebook:"
echo "   ./run.sh --jupyter"
echo ""
echo "📈 To start with TensorBoard:"
echo "   ./run.sh --tensorboard"
echo ""
echo "🔧 To start everything:"
echo "   ./run.sh --all"
