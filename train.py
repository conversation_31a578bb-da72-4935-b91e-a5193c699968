#    Copyright 2023 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
#
#    Licensed under the Apache License, Version 2.0 (the "License");
#    you may not use this file except in compliance with the License.
#    You may obtain a copy of the License at
#
#        http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS,
#    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#    See the License for the specific language governing permissions and
#    limitations under the License.

import os
import copy
import logging
from dataclasses import dataclass, field
from typing import Optional, Dict, Sequence, List
import numpy as np

from setproctitle import setproctitle
import torch
import transformers
from torch.utils.data import Dataset
from transformers import Trainer
from datasets import load_from_disk, concatenate_datasets, load_dataset


IGNORE_INDEX = -100
DEFAULT_PAD_TOKEN = "[PAD]"
DEFAULT_EOS_TOKEN = "</s>"
DEFAULT_BOS_TOKEN = "</s>"
DEFAULT_UNK_TOKEN = "</s>"


@dataclass
class ModelArguments:
    model_name_or_path: Optional[str] = field(default="meta-llama/Meta-Llama-3.1-8B")
    output_name: Optional[str] = field(default="multilingual_translation")


@dataclass
class DataArguments:
    data_name: Optional[str] = field(
        default="junkim100/multilingual_instruction_tuning_lima_bactrian"
    )
    max_length: int = field(
        default=4096,
        metadata={
            "help": "Maximum sequence length. Sequences will be right padded (and possibly truncated)."
        },
    )
    supported_languages: list = field(default_factory=lambda: ["de", "es", "fr", "it"])


@dataclass
class TrainingArguments(transformers.TrainingArguments):
    cache_dir: Optional[str] = field(default=None)
    optim: str = field(
        default="adamw_torch",
        metadata={"help": "adamw_torch, paged_adamw_32bit, adamw_bnb_8bit"},
    )
    report_to: str = field(default=None, metadata={"help": "wandb"})
    wb_name: str = field(
        default="multilingual-translation", metadata={"help": "wandb name"}
    )
    wb_project: str = field(
        default="llama-translation", metadata={"help": "wandb project"}
    )
    proctitle: str = field(
        default="multilingual-training", metadata={"help": "proctitle"}
    )

    bf16: bool = field(default=True, metadata={"help": "default: False"})
    tf32: bool = field(default=True, metadata={"help": "default: None"})
    # fsdp: str = field(default='full_shard auto_wrap')

    num_train_epochs: int = field(default=4)
    per_device_eval_batch_size: int = field(default=1)
    per_device_train_batch_size: int = field(default=1)
    gradient_accumulation_steps: int = field(default=16)

    logging_steps: int = field(default=10)
    # save_steps: int = field(default=100)
    # save_total_limit: int = field(default=2)

    learning_rate: float = field(default=1e-5)
    max_grad_norm: float = field(default=0.3)
    lr_scheduler_type: str = field(default="cosine")
    warmup_ratio: float = field(default=0.03)
    weight_decay: float = field(default=0.0)

    deepspeed: str = field(default="./deepspeed3.json")


def smart_tokenizer_and_embedding_resize(
    special_tokens_dict: Dict,
    tokenizer: transformers.PreTrainedTokenizer,
    model: transformers.PreTrainedModel,
):
    """Resize tokenizer and embedding.

    Note: This is the unoptimized version that may make your embedding size not be divisible by 64.
    """
    num_new_tokens = tokenizer.add_special_tokens(special_tokens_dict)
    model.resize_token_embeddings(len(tokenizer))

    if num_new_tokens > 0:
        input_embeddings = model.get_input_embeddings().weight.data
        output_embeddings = model.get_output_embeddings().weight.data

        input_embeddings_avg = input_embeddings[:-num_new_tokens].mean(
            dim=0, keepdim=True
        )
        output_embeddings_avg = output_embeddings[:-num_new_tokens].mean(
            dim=0, keepdim=True
        )

        input_embeddings[-num_new_tokens:] = input_embeddings_avg
        output_embeddings[-num_new_tokens:] = output_embeddings_avg


def make_supervised_multilingual(data, tokenizer, max_length, supported_languages):
    """Create supervised training data with multilingual translation tokens"""
    inputs_all, labels_all = [], []
    cnt = 0
    skipped = 0

    # Add translation tokens to tokenizer if not present
    special_tokens = [
        "<in_translation>",
        "</in_translation>",
        "<out_translation>",
        "</out_translation>",
    ]
    new_tokens = []
    for token in special_tokens:
        if token not in tokenizer.get_vocab():
            new_tokens.append(token)

    if new_tokens:
        tokenizer.add_tokens(new_tokens)
        logging.warning(f"Added {len(new_tokens)} new translation tokens: {new_tokens}")

    for d in data:
        # Try to find a supported language for this example
        found_language = None
        input_text = None
        output_text = None

        # Check each supported language
        for lang in supported_languages:
            input_column = f"{lang}_input"
            output_column = f"{lang}_output"

            if (
                input_column in d
                and output_column in d
                and d[input_column]
                and d[output_column]
            ):
                found_language = lang
                input_text = d[input_column]
                output_text = d[output_column]
                break

        # Skip if no supported language found
        if not found_language:
            skipped += 1
            continue

        # Create conversation format
        source = [{"role": "user", "content": input_text}]

        # Build response with translation tokens if English versions exist
        if "en_input" in d and "en_output" in d and d["en_input"] and d["en_output"]:
            translation_input = d["en_input"]
            english_output = d["en_output"]

            # Format response with bidirectional translation tokens
            response = f"<in_translation>{translation_input}</in_translation>\n\n{english_output}\n\n<out_translation>{output_text}</out_translation>"
        else:
            # Fallback to direct response if no English version
            response = output_text

        # Create full conversation
        labels_conversation = [
            {"role": "user", "content": input_text},
            {"role": "assistant", "content": response},
        ]

        # Tokenize
        source_tokens = tokenizer.apply_chat_template(source, return_tensors="pt")[0]
        labels_tokens = tokenizer.apply_chat_template(
            labels_conversation, return_tensors="pt"
        )[0]

        if len(labels_tokens) >= max_length:
            cnt += 1
            labels_tokens = labels_tokens[:max_length]

        inputs = copy.deepcopy(labels_tokens)
        labels = copy.deepcopy(labels_tokens)
        labels[: len(source_tokens)] = IGNORE_INDEX

        inputs_all.append(inputs)
        labels_all.append(labels)

    logging.warning(f"mean_token_length: {np.mean([len(item) for item in labels_all])}")
    logging.warning(f"min_token_length: {np.min([len(item) for item in labels_all])}")
    logging.warning(f"max_token_length: {np.max([len(item) for item in labels_all])}")
    logging.warning(f"revised by max len: {cnt} out of {len(labels_all)}")
    logging.warning(f"skipped (no supported language): {skipped}")
    return inputs_all, labels_all


class SupervisedDataset(Dataset):
    """Dataset for supervised fine-tuning with multilingual support."""

    def __init__(
        self, data_name, tokenizer, max_length: int, supported_languages: list
    ):
        super(SupervisedDataset, self).__init__()
        logging.warning("Loading multilingual data...")

        # Load from HuggingFace datasets
        if data_name.startswith("junkim100/"):
            dataset = load_dataset(data_name, "combined")
            data_all = dataset["train"]
        else:
            # Fallback to load_from_disk for local datasets
            data_all = load_from_disk(data_name)

        logging.warning("Data Loaded...")
        inputs_all, labels_all = make_supervised_multilingual(
            data=data_all,
            tokenizer=tokenizer,
            max_length=max_length,
            supported_languages=supported_languages,
        )
        logging.warning("Data Prepared...")
        self.input_ids = inputs_all
        self.labels = labels_all

    def __len__(self):
        return len(self.input_ids)

    def __getitem__(self, i) -> Dict[str, torch.Tensor]:
        return dict(input_ids=self.input_ids[i], labels=self.labels[i])


@dataclass
class DataCollatorForSupervisedDataset(object):
    """Collate examples for supervised fine-tuning."""

    tokenizer: transformers.PreTrainedTokenizer

    def __call__(self, instances: Sequence[Dict]) -> Dict[str, torch.Tensor]:
        input_ids, labels = tuple(
            [instance[key] for instance in instances] for key in ("input_ids", "labels")
        )
        input_ids = torch.nn.utils.rnn.pad_sequence(
            input_ids, batch_first=True, padding_value=self.tokenizer.pad_token_id
        )
        labels = torch.nn.utils.rnn.pad_sequence(
            labels, batch_first=True, padding_value=IGNORE_INDEX
        )
        return dict(
            input_ids=input_ids,
            labels=labels,
            attention_mask=input_ids.ne(self.tokenizer.pad_token_id),
        )


def make_supervised_data_module(
    tokenizer: transformers.PreTrainedTokenizer, data_args
) -> Dict:
    """Make dataset and collator for supervised fine-tuning."""
    train_dataset = SupervisedDataset(
        data_name=data_args.data_name,
        tokenizer=tokenizer,
        max_length=data_args.max_length,
        supported_languages=data_args.supported_languages,
    )
    data_collator = DataCollatorForSupervisedDataset(tokenizer=tokenizer)
    return dict(
        train_dataset=train_dataset, eval_dataset=None, data_collator=data_collator
    )


def train(model_args, data_args, training_args):
    setproctitle(training_args.proctitle)

    # Use the model name directly from HuggingFace
    model_name_or_path = model_args.model_name_or_path

    logging.warning(f"Loading model: {model_name_or_path}")
    model = transformers.AutoModelForCausalLM.from_pretrained(
        model_name_or_path, torch_dtype="auto", trust_remote_code=True
    )

    tokenizer = transformers.AutoTokenizer.from_pretrained(
        model_name_or_path,
        cache_dir=training_args.cache_dir,
        model_max_length=data_args.max_length,
        padding_side="right",
        use_fast=False,
        trust_remote_code=True,
    )

    # Set pad token if not present
    if tokenizer.pad_token is None:
        smart_tokenizer_and_embedding_resize(
            special_tokens_dict=dict(pad_token=DEFAULT_PAD_TOKEN),
            tokenizer=tokenizer,
            model=model,
        )

    # Prepare data module (this will add translation tokens)
    data_module = make_supervised_data_module(tokenizer=tokenizer, data_args=data_args)

    # Resize model embeddings to account for new translation tokens
    model.resize_token_embeddings(len(tokenizer))

    training_args.gradient_checkpointing = True
    trainer = Trainer(
        model=model, tokenizer=tokenizer, args=training_args, **data_module
    )
    trainer.train()
    trainer.save_state()
    trainer.save_model(training_args.output_dir)


if __name__ == "__main__":
    parser = transformers.HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments)
    )
    model_args, data_args, training_args = parser.parse_args_into_dataclasses()

    training_args.fsdp = []
    training_args.fsdp_min_num_params = 0
    training_args.fsdp_transformer_layer_cls_to_wrap = None

    logging.warning(f"training_args:\n{training_args}")
    training_args.output_dir = os.path.join(
        f"./multilingual_tuning",
        model_args.model_name_or_path.replace("/", "_"),
        model_args.output_name,
    )

    os.environ["WANDB_PROJECT"] = training_args.wb_project
    os.environ["WANDB_LOG_MODEL"] = training_args.wb_name

    train(model_args, data_args, training_args)
    torch.cuda.empty_cache()
