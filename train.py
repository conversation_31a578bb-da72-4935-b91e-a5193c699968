import os
import gc
import traceback
import torch
import torch.distributed as dist
import torch.multiprocessing as mp
import deepspeed
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    BitsAndBytesConfig,
)
from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training
from datasets import load_dataset
import wandb
from typing import Dict
import time
import json
from transformers import TrainerCallback


class EnhancedWandBCallback(TrainerCallback):
    """Custom WandB callback for enhanced metrics logging"""

    def __init__(self, rank=0):
        self.rank = rank
        self.step_times = []
        self.memory_usage = []
        self.sequence_completion_stats = {
            "complete_sequences": 0,
            "incomplete_sequences": 0,
            "chunks_with_endings": 0,
            "total_chunks_processed": 0,
        }

    def on_step_end(self, args, state, control, model=None, **kwargs):
        """Log detailed metrics at each step"""
        if self.rank == 0 and state.global_step % args.logging_steps == 0:
            # Get current GPU memory usage
            if torch.cuda.is_available():
                memory_info = get_gpu_memory_info(0)
                memory_used_gb = memory_info[
                    "allocated"
                ]  # Fixed: use "allocated" instead of "used"
                memory_percent = memory_info["usage_percent"]

                # Log memory metrics
                wandb.log(
                    {
                        "memory/gpu_memory_used_gb": memory_used_gb,
                        "memory/gpu_memory_percent": memory_percent,
                        "memory/gpu_memory_free_gb": memory_info["free"],
                        "step": state.global_step,
                    }
                )

                # Track memory usage over time
                self.memory_usage.append(memory_used_gb)

                # Log memory trend
                if len(self.memory_usage) > 1:
                    memory_trend = self.memory_usage[-1] - self.memory_usage[-2]
                    wandb.log(
                        {
                            "memory/memory_trend_gb": memory_trend,
                            "step": state.global_step,
                        }
                    )

    def on_log(self, args, state, control, logs=None, model=None, **kwargs):
        """Enhanced logging with additional metrics"""
        if self.rank == 0 and logs:
            enhanced_logs = logs.copy()

            # Add sequence chunking metrics
            enhanced_logs["config/chunk_size"] = 4096
            enhanced_logs["config/overlap_tokens"] = 512
            enhanced_logs["config/max_sequence_length"] = 9064

            # Add sequence completion tracking metrics
            if self.sequence_completion_stats["total_chunks_processed"] > 0:
                completion_rate = self.sequence_completion_stats[
                    "complete_sequences"
                ] / max(1, self.sequence_completion_stats["total_chunks_processed"])
                ending_preservation_rate = self.sequence_completion_stats[
                    "chunks_with_endings"
                ] / max(1, self.sequence_completion_stats["total_chunks_processed"])

                enhanced_logs["sequence_completion/completion_rate"] = completion_rate
                enhanced_logs["sequence_completion/ending_preservation_rate"] = (
                    ending_preservation_rate
                )
                enhanced_logs["sequence_completion/complete_sequences"] = (
                    self.sequence_completion_stats["complete_sequences"]
                )
                enhanced_logs["sequence_completion/incomplete_sequences"] = (
                    self.sequence_completion_stats["incomplete_sequences"]
                )
                enhanced_logs["sequence_completion/chunks_with_endings"] = (
                    self.sequence_completion_stats["chunks_with_endings"]
                )
                enhanced_logs["sequence_completion/total_chunks_processed"] = (
                    self.sequence_completion_stats["total_chunks_processed"]
                )

            # Add training efficiency metrics
            if "train_loss" in logs:
                enhanced_logs["efficiency/loss_per_step"] = logs["train_loss"]

            # Add gradient norm tracking
            if "grad_norm" in logs:
                enhanced_logs["training/gradient_norm"] = logs["grad_norm"]

            # Log to wandb
            wandb.log(enhanced_logs)


# Set environment variables for distributed training
os.environ["MASTER_ADDR"] = "localhost"
os.environ["MASTER_PORT"] = "12355"

# Memory optimization for 32k sequences
os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
os.environ["TOKENIZERS_PARALLELISM"] = "false"  # Prevent tokenizer warnings
os.environ["TORCH_NCCL_BLOCKING_WAIT"] = "1"  # Use new NCCL environment variable

# Handle CUDA_VISIBLE_DEVICES for DeepSpeed compatibility
if "CUDA_VISIBLE_DEVICES" in os.environ:
    visible_devices = os.environ["CUDA_VISIBLE_DEVICES"]
    print(f"🔧 CUDA_VISIBLE_DEVICES detected: {visible_devices}")
    print(f"   Note: DeepSpeed may ignore this. Use --include flag instead:")
    print(f"   deepspeed --include localhost:{visible_devices} train.py")


def get_gpu_memory_info(device_id: int) -> Dict[str, float]:
    """Get detailed GPU memory information for a specific device"""
    if torch.cuda.is_available() and device_id < torch.cuda.device_count():
        torch.cuda.set_device(device_id)
        total = torch.cuda.get_device_properties(device_id).total_memory / 1024**3  # GB
        allocated = torch.cuda.memory_allocated(device_id) / 1024**3  # GB
        cached = torch.cuda.memory_reserved(device_id) / 1024**3  # GB
        free = total - allocated
        usage_percent = (allocated / total) * 100

        return {
            "total": total,
            "allocated": allocated,
            "cached": cached,
            "free": free,
            "usage_percent": usage_percent,
        }
    return {"total": 0, "allocated": 0, "cached": 0, "free": 0, "usage_percent": 0}


def log_gpu_memory_usage(rank: int, world_size: int, stage: str = ""):
    """Log GPU memory usage across all devices"""
    if rank == 0:  # Only log from rank 0 to avoid spam
        print(f"\n=== GPU Memory Usage {stage} ===")
        for i in range(world_size):
            memory_info = get_gpu_memory_info(i)
            print(
                f"GPU {i}: {memory_info['allocated']:.2f}GB/{memory_info['total']:.2f}GB "
                f"({memory_info['usage_percent']:.1f}%) - Free: {memory_info['free']:.2f}GB"
            )
        print("=" * 50)


def setup(rank, world_size):
    """Fixed setup function with proper device assignment"""

    print(f"Process {rank}: Initializing distributed training...")

    # CRITICAL: Set device BEFORE any CUDA operations or process group init
    torch.cuda.set_device(rank)
    device = torch.device(f"cuda:{rank}")

    # Check if already initialized by DeepSpeed launcher
    if not dist.is_initialized():
        # Initialize process group AFTER setting device
        dist.init_process_group("nccl", rank=rank, world_size=world_size)
    else:
        print(f"Process {rank}: Distributed training already initialized by DeepSpeed")

    # Verify correct device assignment
    current_device = torch.cuda.current_device()
    print(f"✅ Process {rank}: Successfully assigned to GPU {current_device}")

    dist.barrier()

    if rank == 0:
        print(
            f"✅ Distributed training initialized successfully with {world_size} processes"
        )

    return device


def cleanup():
    """Clean up the process group with error handling"""
    try:
        if dist.is_initialized():
            dist.destroy_process_group()
    except Exception as e:
        print(f"Warning: Error during distributed cleanup: {e}")


def load_model_and_tokenizer(
    model_name="meta-llama/Meta-Llama-3.1-8B",
    use_4bit=False,  # Disable quantization for DeepSpeed compatibility
    target_device=None,
    rank=0,
    max_length=9064,
):
    """Load model and tokenizer optimized for DeepSpeed and long sequences"""

    print(f"Process {rank}: Loading model and tokenizer for max_length={max_length}")
    print(f"Process {rank}: DeepSpeed mode - quantization disabled for compatibility")

    # Note: DeepSpeed handles memory optimization, so we disable quantization
    # DeepSpeed's ZeRO stages provide better memory efficiency than quantization
    bnb_config = None

    # Load tokenizer with optimizations for long sequences
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        model_max_length=max_length,  # Set maximum length
        padding_side="right",
        truncation_side="right",
        use_fast=True,  # Use fast tokenizer for better performance
    )

    # Set padding token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Set up custom chat template with special tokens
    tokenizer = setup_custom_chat_template(tokenizer)

    print(f"Process {rank}: Tokenizer loaded with vocab size: {len(tokenizer)}")

    # Load model with DeepSpeed optimizations - CRITICAL: Keep on CPU for DeepSpeed
    print(f"Process {rank}: Loading model from {model_name}")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16,
        device_map=None,  # CRITICAL: Must be None for DeepSpeed
        trust_remote_code=True,
        low_cpu_mem_usage=True,  # Reduce CPU memory usage
        use_cache=False,  # Disable KV cache for training to save memory
    )

    # Resize token embeddings to account for new special tokens
    original_vocab_size = model.config.vocab_size
    new_vocab_size = len(tokenizer)
    if new_vocab_size != original_vocab_size:
        print(
            f"Process {rank}: Resizing token embeddings from {original_vocab_size} to {new_vocab_size}"
        )
        model.resize_token_embeddings(new_vocab_size)

    # CRITICAL: Keep model on CPU - DeepSpeed will handle device placement during initialization
    print(
        f"Process {rank}: Model loaded on CPU, DeepSpeed will handle device placement"
    )

    return model, tokenizer


def setup_lora(model, rank=0, max_length=9064):
    """Setup LoRA configuration optimized for DeepSpeed and long sequences"""

    print(f"Process {rank}: Setting up LoRA for max_length={max_length}")

    # Determine target modules based on model architecture
    model_type = model.config.model_type.lower()

    if "llama" in model_type:
        # Llama/Llama2/Llama3 target modules
        target_modules = [
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
        ]
    elif "gpt" in model_type or "dialogpt" in model_type:
        # GPT-style models
        target_modules = ["c_attn", "c_proj", "c_fc"]
    elif "mistral" in model_type:
        # Mistral models
        target_modules = [
            "q_proj",
            "k_proj",
            "v_proj",
            "o_proj",
            "gate_proj",
            "up_proj",
            "down_proj",
        ]
    else:
        # Default: try to find attention and MLP layers
        target_modules = []
        for name, module in model.named_modules():
            if any(
                keyword in name.lower()
                for keyword in ["attn", "attention", "proj", "fc", "linear"]
            ):
                module_name = name.split(".")[-1]
                if module_name not in target_modules:
                    target_modules.append(module_name)

        if not target_modules:
            # Fallback to common patterns
            target_modules = ["q_proj", "v_proj"]

    print(f"Process {rank}: Using LoRA target modules: {target_modules}")

    # LoRA configuration optimized for translation token learning - increased rank
    lora_config = LoraConfig(
        r=16,  # Increased from 4 to 16 for better translation token learning
        lora_alpha=32,  # Scaling factor (2x rank)
        target_modules=target_modules,
        lora_dropout=0.1,  # Slightly increased dropout for regularization
        bias="none",
        task_type="CAUSAL_LM",
        inference_mode=False,  # Ensure training mode
    )

    model = get_peft_model(model, lora_config)

    # Note: DeepSpeed will handle gradient checkpointing through its config
    # We don't enable it manually here to avoid conflicts
    print(
        f"Process {rank}: LoRA setup complete, DeepSpeed will handle gradient checkpointing"
    )

    if rank == 0:
        model.print_trainable_parameters()

    return model


def setup_custom_chat_template(tokenizer):
    """Set up custom chat template with translation tokens as content delimiters"""

    # Add special tokens for directional translation
    special_tokens = [
        "<in_translation>",
        "</in_translation>",  # English translations
        "<out_translation>",
        "</out_translation>",  # Target language translations
    ]
    tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})

    # Standard Llama chat template with <in_translation> token for generation
    custom_template = """
{%- for message in messages %}
    {%- if message['role'] == 'system' %}
        {{- '<|start_header_id|>system<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'user' %}
        {{- '<|start_header_id|>user<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- elif message['role'] == 'assistant' %}
        {{- '<|start_header_id|>assistant<|end_header_id|>\n\n' + message['content'] + '<|eot_id|>' }}
    {%- endif %}
{%- endfor %}
{%- if add_generation_prompt %}
    {{- '<|start_header_id|>assistant<|end_header_id|>\n\n<in_translation>' }}
{%- endif %}
""".strip()

    tokenizer.chat_template = custom_template
    return tokenizer


def format_multilingual_chat_template(examples, tokenizer, supported_languages):
    """Format multilingual data for reasoning models with translation tokens"""

    formatted_texts = []

    # Language detection mapping
    lang_columns = {
        "de": ("de_input", "de_output"),
        "es": ("es_input", "es_output"),
        "fr": ("fr_input", "fr_output"),
        "it": ("it_input", "it_output"),
    }

    for i in range(len(examples["en_input"])):
        # Skip if English fields are missing
        if not examples["en_input"][i] or not examples["en_output"][i]:
            continue

        # Find which target language has data for this example
        target_language = None
        user_input = None
        translation_output = None

        for lang in supported_languages:
            input_col, output_col = lang_columns[lang]
            if (
                examples.get(input_col, [None])[i]
                and examples.get(output_col, [None])[i]
            ):
                target_language = lang
                user_input = examples[input_col][i]
                translation_output = examples[output_col][i]
                break

        # Skip if no target language data found
        if not target_language or not user_input or not translation_output:
            continue

        # Get English content
        translation_input = examples["en_input"][
            i
        ]  # English translation of target language input
        output_content = examples["en_output"][i]  # English reasoning/response

        # Create conversation with target language input
        conversation = [{"role": "user", "content": user_input}]

        # Apply chat template to get the prompt
        formatted_chat = tokenizer.apply_chat_template(
            conversation, tokenize=False, add_generation_prompt=True
        )

        # Create the response with bidirectional translation tokens
        response = f"<in_translation>{translation_input}</in_translation>\n\n{output_content}\n\n<out_translation>{translation_output}</out_translation>"

        # Combine formatted chat + response + eos
        full_text = formatted_chat + response + tokenizer.eos_token
        formatted_texts.append(full_text)

    return {"text": formatted_texts}


def prepare_dataset(
    dataset_name, tokenizer, max_length=9064, rank=0, supported_languages=None
):
    """Prepare and tokenize multilingual dataset optimized for extremely long sequences"""

    if supported_languages is None:
        supported_languages = ["de", "es", "fr", "it"]

    print(
        f"Process {rank}: Preparing multilingual dataset {dataset_name} with max_length={max_length}"
    )
    print(f"Process {rank}: Supported languages: {supported_languages}")

    # Load dataset with 'combined' subset
    dataset = load_dataset(dataset_name, "combined")

    print(
        f"Process {rank}: Dataset loaded - Train: {len(dataset['train'])}, Val: {len(dataset['val'])}, Test: {len(dataset['test'])}"
    )

    # Format with chat template and filter out invalid samples
    def format_and_filter(examples):
        formatted = format_multilingual_chat_template(
            examples, tokenizer, supported_languages
        )

        # Filter out empty texts (from skipped samples)
        filtered_texts = [text for text in formatted["text"] if text.strip()]

        return {"text": filtered_texts}

    dataset = dataset.map(
        format_and_filter,
        batched=True,
        remove_columns=dataset["train"].column_names,
        desc="Formatting dataset",
    )

    # Enhanced tokenization function with sequence completion validation
    def tokenize_function(examples):
        # First, validate sequences before tokenization
        valid_texts = []
        truncated_count = 0
        incomplete_count = 0

        for text in examples["text"]:
            # Check if text contains complete response format
            has_complete_format = all(
                token in text
                for token in [
                    "<in_translation>",
                    "</in_translation>",
                    "<out_translation>",
                    "</out_translation>",
                ]
            )

            if not has_complete_format:
                incomplete_count += 1
                print(f"⚠️  Skipping incomplete sequence (missing translation tokens)")
                continue

            # Pre-tokenize to check length
            temp_tokens = tokenizer(text, add_special_tokens=False)["input_ids"]

            if len(temp_tokens) > max_length:
                truncated_count += 1
                # Smart truncation: preserve the ending with </out_translation>
                out_translation_end = "</out_translation>"
                eos_token = tokenizer.eos_token

                # Find the position of </out_translation> in the text
                end_marker_pos = text.rfind(out_translation_end)
                if end_marker_pos != -1:
                    # Calculate how much we need to truncate from the beginning
                    # to preserve the ending including </out_translation> + eos_token
                    ending_part = text[end_marker_pos:]
                    ending_tokens = tokenizer(
                        ending_part + eos_token, add_special_tokens=False
                    )["input_ids"]

                    # Reserve space for ending tokens
                    max_start_tokens = (
                        max_length - len(ending_tokens) - 50
                    )  # 50 token buffer

                    if max_start_tokens > 0:
                        # Tokenize the start part and truncate
                        start_part = text[:end_marker_pos]
                        start_tokens = tokenizer(start_part, add_special_tokens=False)[
                            "input_ids"
                        ]

                        if len(start_tokens) > max_start_tokens:
                            # Truncate start tokens and decode back to text
                            truncated_start_tokens = start_tokens[:max_start_tokens]
                            truncated_start_text = tokenizer.decode(
                                truncated_start_tokens, skip_special_tokens=True
                            )

                            # Reconstruct the text with preserved ending
                            text = truncated_start_text + ending_part
                            print(
                                f"🔧 Smart truncation applied: preserved </out_translation> ending"
                            )
                        else:
                            print(
                                f"✅ Sequence fits after smart truncation calculation"
                            )
                    else:
                        print(
                            f"❌ Sequence too long even with smart truncation, skipping"
                        )
                        continue
                else:
                    print(
                        f"❌ No </out_translation> found for smart truncation, skipping"
                    )
                    continue

            valid_texts.append(text)

        if rank == 0 and (truncated_count > 0 or incomplete_count > 0):
            print(f"📊 Tokenization validation:")
            print(f"   Valid sequences: {len(valid_texts)}")
            print(f"   Truncated (smart): {truncated_count}")
            print(f"   Incomplete (skipped): {incomplete_count}")

        # Tokenize valid sequences
        tokenized = tokenizer(
            valid_texts,
            truncation=True,  # Final safety truncation
            padding=False,  # Don't pad here - will pad in data collator
            max_length=max_length,
            return_tensors=None,
            add_special_tokens=False,  # Already in chat template
            return_attention_mask=True,
            return_length=True,
        )

        # Create labels (copy of input_ids for causal LM)
        tokenized["labels"] = [input_ids.copy() for input_ids in tokenized["input_ids"]]

        return tokenized

    # Apply tokenization with progress tracking
    dataset = dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=["text"],
        desc="Tokenizing dataset",
        num_proc=1,  # Single process to avoid memory issues
    )

    # Filter sequences and log statistics
    def filter_length(example):
        length = len(example["input_ids"])
        return length <= max_length and length > 50  # Minimum length filter

    original_train_size = len(dataset["train"])
    original_val_size = len(dataset["val"])

    dataset = dataset.filter(filter_length)

    filtered_train_size = len(dataset["train"])
    filtered_val_size = len(dataset["val"])

    if rank == 0:
        print(f"Dataset filtering results:")
        print(
            f"  Train: {original_train_size} -> {filtered_train_size} "
            f"({filtered_train_size/original_train_size*100:.1f}% retained)"
        )
        print(
            f"  Val: {original_val_size} -> {filtered_val_size} "
            f"({filtered_val_size/original_val_size*100:.1f}% retained)"
        )

        # Enhanced sequence length and completion statistics
        train_lengths = [len(example["input_ids"]) for example in dataset["train"]]
        if train_lengths:
            print(
                f"  Sequence lengths - Min: {min(train_lengths)}, Max: {max(train_lengths)}, "
                f"Mean: {sum(train_lengths)/len(train_lengths):.1f}"
            )

            # Validate sequence completion in final dataset
            complete_sequences = 0
            incomplete_sequences = 0

            for i, example in enumerate(dataset["train"]):
                if i >= 10:  # Check first 10 sequences for validation
                    break

                text = tokenizer.decode(example["input_ids"], skip_special_tokens=False)
                has_complete_format = all(
                    token in text
                    for token in [
                        "<in_translation>",
                        "</in_translation>",
                        "<out_translation>",
                        "</out_translation>",
                    ]
                )

                if has_complete_format:
                    complete_sequences += 1
                    # Check if ending is preserved
                    if text.endswith(tokenizer.eos_token):
                        ending_check = (
                            text[: -len(tokenizer.eos_token)]
                            if tokenizer.eos_token
                            else text
                        )
                        if (
                            "</out_translation>" in ending_check[-50:]
                        ):  # Check last 50 chars before EOS
                            print(
                                f"  ✅ Sample {i}: Complete format with preserved ending"
                            )
                        else:
                            print(
                                f"  ⚠️  Sample {i}: Complete format but ending may be truncated"
                            )
                    else:
                        print(f"  ⚠️  Sample {i}: Complete format but no EOS token")
                else:
                    incomplete_sequences += 1
                    print(
                        f"  ❌ Sample {i}: Incomplete format - missing translation tokens"
                    )

            print(
                f"  📊 Validation sample (first 10): {complete_sequences}/10 complete sequences"
            )
            if incomplete_sequences > 0:
                print(
                    f"  ⚠️  Found {incomplete_sequences} incomplete sequences in validation sample!"
                )

    return dataset


class ChunkedSequenceDataCollator:
    """Advanced data collator with sequence chunking for 9k+ token sequences"""

    def __init__(
        self,
        tokenizer,
        max_length=9064,
        chunk_size=4096,
        overlap=512,
        device=None,
        wandb_callback=None,
    ):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.chunk_size = chunk_size  # Size of each chunk
        self.overlap = overlap  # Overlap between chunks for context continuity
        self.device = device
        self.chunk_cycle_index = 0  # Track which chunk to use for cycling
        self.sequence_completion_tracking = (
            {}
        )  # Track which sequences have been fully processed
        self.wandb_callback = wandb_callback  # Reference to WandB callback for metrics

        print(f"🔧 ChunkedSequenceDataCollator initialized:")
        print(f"   Max sequence length: {max_length}")
        print(f"   Chunk size: {chunk_size}")
        print(f"   Overlap: {overlap}")
        print(
            f"   Effective chunks per 9k sequence: {self._calculate_chunks(max_length)}"
        )
        print(f"   🎯 FIXED: Will cycle through ALL chunks to ensure complete training")

    def _calculate_chunks(self, seq_length):
        """Calculate number of chunks needed for a sequence"""
        if seq_length <= self.chunk_size:
            return 1

        effective_chunk_size = self.chunk_size - self.overlap
        remaining_length = seq_length - self.chunk_size
        additional_chunks = (
            remaining_length + effective_chunk_size - 1
        ) // effective_chunk_size
        return 1 + additional_chunks

    def _create_chunks(self, input_ids, labels):
        """Split a long sequence into overlapping chunks"""
        seq_length = len(input_ids)

        if seq_length <= self.chunk_size:
            # No chunking needed
            return [(input_ids, labels)]

        chunks = []
        start = 0

        while start < seq_length:
            end = min(start + self.chunk_size, seq_length)

            chunk_input_ids = input_ids[start:end]
            chunk_labels = labels[start:end]

            # Pad chunk if it's shorter than chunk_size
            if len(chunk_input_ids) < self.chunk_size:
                padding_length = self.chunk_size - len(chunk_input_ids)
                chunk_input_ids.extend([self.tokenizer.pad_token_id] * padding_length)
                chunk_labels.extend([-100] * padding_length)

            chunks.append((chunk_input_ids, chunk_labels))

            # Move start position with overlap consideration
            if end >= seq_length:
                break
            start = end - self.overlap

        return chunks

    def _validate_sequence_completion(self, input_ids, labels=None):
        """Validate that the sequence contains complete response format"""
        # Convert to text to check for translation tokens
        text = self.tokenizer.decode(input_ids, skip_special_tokens=False)

        # Check for required tokens
        has_in_translation = "<in_translation>" in text
        has_in_translation_end = "</in_translation>" in text
        has_out_translation = "<out_translation>" in text
        has_out_translation_end = "</out_translation>" in text

        is_complete = all(
            [
                has_in_translation,
                has_in_translation_end,
                has_out_translation,
                has_out_translation_end,
            ]
        )

        return is_complete, {
            "has_in_translation": has_in_translation,
            "has_in_translation_end": has_in_translation_end,
            "has_out_translation": has_out_translation,
            "has_out_translation_end": has_out_translation_end,
            "sequence_length": len(input_ids),
            "text_preview": text[-200:] if len(text) > 200 else text,
        }

    def __call__(self, features):
        """Process features with COMPLETE sequence chunking strategy"""
        # CRITICAL FIX: Cycle through ALL chunks to ensure complete training

        if len(features) > 1:
            # If multiple features, only take the first one to avoid memory issues
            features = [features[0]]

        feature = features[0]
        input_ids = feature["input_ids"]
        labels = feature["labels"]

        # Validate sequence completion before chunking
        is_complete, completion_info = self._validate_sequence_completion(
            input_ids, labels
        )

        # Update WandB callback statistics
        if self.wandb_callback:
            if is_complete:
                self.wandb_callback.sequence_completion_stats["complete_sequences"] += 1
            else:
                self.wandb_callback.sequence_completion_stats[
                    "incomplete_sequences"
                ] += 1
            self.wandb_callback.sequence_completion_stats["total_chunks_processed"] += 1

        if not is_complete:
            print(f"⚠️  WARNING: Incomplete sequence detected!")
            print(f"   Length: {completion_info['sequence_length']}")
            print(
                f"   Missing tokens: {[k for k, v in completion_info.items() if k.startswith('has_') and not v]}"
            )
            print(f"   Text ending: ...{completion_info['text_preview']}")

        # Create chunks for this sequence
        chunks = self._create_chunks(input_ids, labels)

        # CRITICAL FIX: Cycle through chunks instead of always using first
        total_chunks = len(chunks)
        if total_chunks > 1:
            # Use modulo to cycle through all chunks across training steps
            chunk_index = self.chunk_cycle_index % total_chunks
            self.chunk_cycle_index += 1

            # Check if we're processing the final chunk (contains ending)
            if chunk_index == total_chunks - 1:
                chunk_text = self.tokenizer.decode(
                    chunks[chunk_index][0], skip_special_tokens=False
                )
                has_ending = "</out_translation>" in chunk_text

                # Update WandB callback statistics for ending preservation
                if self.wandb_callback and has_ending:
                    self.wandb_callback.sequence_completion_stats[
                        "chunks_with_endings"
                    ] += 1

            # Log chunk cycling for monitoring
            if self.chunk_cycle_index % 100 == 0:  # Log every 100 steps
                print(
                    f"🔄 Chunk cycling: Step {self.chunk_cycle_index}, using chunk {chunk_index}/{total_chunks-1}"
                )

                if chunk_index == total_chunks - 1:
                    chunk_text = self.tokenizer.decode(
                        chunks[chunk_index][0], skip_special_tokens=False
                    )
                    has_ending = "</out_translation>" in chunk_text
                    print(
                        f"   📝 Final chunk contains </out_translation>: {'✅' if has_ending else '❌'}"
                    )
        else:
            chunk_index = 0

        chunk_input_ids, chunk_labels = chunks[chunk_index]

        # Convert to tensors
        input_ids = torch.tensor([chunk_input_ids])  # Batch size 1
        labels = torch.tensor([chunk_labels])

        # Create attention mask
        attention_mask = (input_ids != self.tokenizer.pad_token_id).long()

        batch = {
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels,
        }

        # Move to device if specified
        if self.device is not None:
            batch = {
                k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                for k, v in batch.items()
            }

        return batch


def train_model(
    rank,
    world_size,
    dataset_name,
    model_name="meta-llama/Meta-Llama-3.1-8B",
    supported_languages=None,
):
    """Main training function with DeepSpeed integration for multilingual translation"""

    # Initialize supported languages
    if supported_languages is None:
        supported_languages = [
            "de",
            "es",
            "fr",
            "it",
        ]  # German, Spanish, French, Italian

    max_length = 9064  # Exact max token length required

    try:
        # Set aggressive memory optimization environment variables for 9k sequences
        os.environ["PYTORCH_CUDA_ALLOC_CONF"] = "expandable_segments:True"
        os.environ["CUDA_LAUNCH_BLOCKING"] = "0"

        # Setup distributed training
        device = setup(rank, world_size)

        print(
            f"Process {rank}: Starting DeepSpeed training on {device} with max_length={max_length} (9k sequences)"
        )

        # Log initial GPU memory
        log_gpu_memory_usage(rank, world_size, "Before Model Loading")

        # Initialize wandb (only on rank 0)
        if rank == 0:
            wandb.init(
                project="llama-translation",
                name=f"lima-bactrian-lora16-epoch2",
                tags=[
                    "lora",
                    "deepspeed",
                    "multi-gpu",
                    "9k-sequences",
                    "lora16",
                    "epoch2",
                    "multilingual",
                    "translation-tokens",
                ],
                config={
                    "model_name": model_name,
                    "dataset_name": dataset_name,
                    "world_size": world_size,
                    "max_length": max_length,
                    "chunk_size": 4096,
                    "overlap_tokens": 512,
                    "chunks_per_9k_sequence": 3,
                    "method": "LoRA + DeepSpeed ZeRO-3 + Sequence Chunking",
                    "lora_rank": 16,  # Updated from 8 to 16
                    "lora_alpha": 32,  # Updated from 8 to 32
                    "lora_dropout": 0.1,  # Updated from 0.05 to 0.1
                    "trainable_params": "42M",  # Estimated for rank 16
                    "trainable_percent": "0.52%",  # Estimated for rank 16
                    "deepspeed_stage": 3,
                    "per_device_batch_size": 1,
                    "gradient_accumulation_steps": 8,
                    "effective_batch_size": 32,
                    "learning_rate": 1e-4,
                    "supported_languages": supported_languages,
                    "reasoning_tokens": True,
                    "epochs": 2,  # Updated epoch count
                    "chat_template_fixed": True,  # Added to track template fix
                    "multilingual": True,  # Added multilingual flag
                },
            )

        # Load model and tokenizer for DeepSpeed
        model, tokenizer = load_model_and_tokenizer(
            model_name=model_name,
            use_4bit=False,  # Disabled for DeepSpeed compatibility
            target_device=None,  # DeepSpeed handles device placement
            rank=rank,
            max_length=max_length,
        )

        # Synchronization barrier after model loading
        dist.barrier()
        print(f"Process {rank}: Passed model loading barrier")

        # Log GPU memory after model loading
        log_gpu_memory_usage(rank, world_size, "After Model Loading")

        # Setup LoRA with DeepSpeed optimizations
        model = setup_lora(model, rank, max_length)

        print(f"Process {rank}: LoRA model prepared for DeepSpeed initialization")

        # Synchronization barrier after LoRA setup
        dist.barrier()
        print(f"Process {rank}: Passed LoRA setup barrier")

        # Prepare dataset with long sequence support
        dataset = prepare_dataset(
            dataset_name, tokenizer, max_length, rank, supported_languages
        )

        # Calculate and log training steps information
        if rank == 0:
            train_dataset_size = len(dataset["train"])
            effective_batch_size = (
                1
                * 8
                * world_size  # per_device_batch_size * gradient_accumulation * world_size
            )
            base_steps_per_epoch = train_dataset_size // effective_batch_size
            total_base_steps = base_steps_per_epoch * 2  # 2 epochs

            # Estimate chunking multiplier (sequences > 4096 tokens get chunked)
            try:
                # Sample first 100 examples to estimate average length
                sample_size = min(100, len(dataset["train"]))
                sample_lengths = []
                for i in range(sample_size):
                    example = dataset["train"][i]
                    if isinstance(example, dict) and "input_ids" in example:
                        sample_lengths.append(len(example["input_ids"]))

                if sample_lengths:
                    avg_length = sum(sample_lengths) / len(sample_lengths)
                    chunking_multiplier = max(1, avg_length / 4096)
                else:
                    # Fallback if we can't determine lengths
                    avg_length = 6000  # Reasonable estimate for long sequences
                    chunking_multiplier = 1.5
            except Exception as e:
                print(f"   ⚠️  Could not estimate sequence lengths: {e}")
                avg_length = 6000  # Fallback estimate
                chunking_multiplier = 1.5

            estimated_total_steps = int(total_base_steps * chunking_multiplier)

            print(f"\n📊 Training Steps Calculation:")
            print(f"   Dataset size: {train_dataset_size:,}")
            print(f"   Epochs: 2")
            print(f"   Effective batch size: {effective_batch_size}")
            print(f"   Base steps per epoch: {base_steps_per_epoch:,}")
            print(f"   Base total steps: {total_base_steps:,}")
            print(f"   Average sequence length: {avg_length:.0f} tokens")
            print(f"   Chunking multiplier: {chunking_multiplier:.1f}x")
            print(f"   Estimated total steps: {estimated_total_steps:,}")
            print(f"\n🔄 Checkpoint & Evaluation Schedule:")
            print(f"   Save every: 100 steps")
            print(f"   Evaluate every: 100 steps")
            print(f"   Log every: 20 steps")
            print(f"   Expected checkpoints: ~{estimated_total_steps // 100}")
            print(f"   Expected evaluations: ~{estimated_total_steps // 100}")
            print(f"   Keep last: 10 checkpoints")
            print(
                f"   Save frequency: every {100/estimated_total_steps*100:.1f}% of training"
            )
            print(
                f"   Log frequency: every {20/estimated_total_steps*100:.1f}% of training"
            )

        # Synchronization barrier after dataset preparation
        dist.barrier()
        print(f"Process {rank}: Dataset prepared and synchronized")

        # Enhanced training arguments optimized for DeepSpeed and long sequences
        training_args = TrainingArguments(
            output_dir="./multilingual_lora16_epoch2",
            num_train_epochs=2,  # Updated for multilingual training
            per_device_train_batch_size=1,  # Must be 1 for 9k sequences
            gradient_accumulation_steps=8,  # Reduced due to chunking increasing effective batch size
            warmup_ratio=0.05,  # Reduced warmup for stability
            learning_rate=1e-4,  # Reduced learning rate for long sequences
            weight_decay=0.01,
            max_grad_norm=0.5,  # Gradient clipping for stability
            # Precision settings
            fp16=False,
            bf16=True,
            tf32=True,  # Enable TF32 for better performance
            # Memory optimizations - let DeepSpeed handle these
            gradient_checkpointing=False,  # DeepSpeed handles this
            dataloader_num_workers=0,  # Avoid multiprocessing issues
            dataloader_pin_memory=False,  # Reduce memory pressure
            remove_unused_columns=False,
            # DeepSpeed integration - use ZeRO Stage 3 for better memory efficiency
            deepspeed="deepspeed_config_zero3.json",  # Use ZeRO Stage 3 for 9k sequences
            local_rank=rank,
            # Logging and monitoring
            report_to="wandb" if rank == 0 else "none",
            run_name=(f"lima-bactrian-lora16-epoch2" if rank == 0 else None),
            logging_steps=20,  # Optimized for ~3,500 steps (logs every 0.6%)
            logging_first_step=True,
            # Checkpoint saving - optimized for 3,500 step training
            save_strategy="steps",
            save_steps=100,  # Save every 2.8% of training (~35 checkpoints)
            save_total_limit=10,  # Keep more checkpoints since you have disk space
            # Evaluation - balanced frequency for good monitoring
            eval_strategy="steps",
            eval_steps=100,  # Evaluate every 2.8% of training (~35 evaluations)
            eval_accumulation_steps=1,  # Reduce eval memory usage
            # Performance optimizations
            group_by_length=False,  # Disable for consistent memory usage
            length_column_name="length",
            # Memory management
            skip_memory_metrics=False,  # Monitor memory usage
        )

        # Initialize enhanced WandB callback first
        wandb_callback = EnhancedWandBCallback(rank=rank)

        # Use chunked data collator for 9k sequences with WandB callback integration
        data_collator = ChunkedSequenceDataCollator(
            tokenizer=tokenizer,
            max_length=max_length,
            chunk_size=4096,  # Process 9k sequences in 4k chunks
            overlap=512,  # 512 token overlap for context continuity
            device=None,  # DeepSpeed will handle device placement
            wandb_callback=(
                wandb_callback if rank == 0 else None
            ),  # Only pass callback to rank 0
        )

        # Log GPU memory before trainer initialization
        log_gpu_memory_usage(rank, world_size, "Before Trainer Init")

        # Initialize trainer with DeepSpeed integration and enhanced logging
        trainer = Trainer(
            model=model,
            args=training_args,
            train_dataset=dataset["train"],
            eval_dataset=dataset["val"],
            data_collator=data_collator,
            tokenizer=tokenizer,
            callbacks=[wandb_callback] if rank == 0 else [],
        )

        # Final synchronization before training
        dist.barrier()

        # Log GPU memory after DeepSpeed initialization
        log_gpu_memory_usage(rank, world_size, "After DeepSpeed Init")

        # Start training
        if rank == 0:
            print(
                f"🚀 Starting DeepSpeed training with sequence chunking on {world_size} GPUs..."
            )
            print(f"   - Max sequence length: {max_length} (chunked into 4k pieces)")
            print(f"   - Chunk size: 4096 tokens with 512 token overlap")
            print(f"   - DeepSpeed ZeRO Stage: 3 (Maximum Memory Efficiency)")
            print(
                f"   - Batch size per device: {training_args.per_device_train_batch_size}"
            )
            print(
                f"   - Gradient accumulation steps: {training_args.gradient_accumulation_steps}"
            )
            print(
                f"   - Base effective batch size: {training_args.per_device_train_batch_size * training_args.gradient_accumulation_steps * world_size}"
            )
            print(f"   - Note: Actual batch size will be ~3x higher due to chunking")

        # Train with memory monitoring
        start_time = time.time()
        trainer.train()
        training_time = time.time() - start_time

        # Log final GPU memory usage
        log_gpu_memory_usage(rank, world_size, "After Training Complete")

        # Save final model (only on rank 0)
        if rank == 0:
            print(f"✅ DeepSpeed training completed in {training_time/3600:.2f} hours!")
            trainer.save_model()
            print("Model saved successfully!")

            # Log comprehensive final metrics
            final_memory_info = get_gpu_memory_info(0)
            wandb.log(
                {
                    "final/training_time_hours": training_time / 3600,
                    "final/training_time_minutes": training_time / 60,
                    "final/memory_usage_percent": final_memory_info["usage_percent"],
                    "final/memory_used_gb": final_memory_info[
                        "allocated"
                    ],  # Fixed: use "allocated"
                    "final/memory_free_gb": final_memory_info["free"],
                    "final/deepspeed_stage": 3,
                    "final/sequence_length": max_length,
                    "final/chunk_size": 4096,
                    "final/lora_rank": 16,
                    "final/trainable_params": 41943040,  # Updated for rank 16
                    "final/world_size": world_size,
                    "final/effective_batch_size": 32,
                }
            )

            # Log final sequence completion statistics
            if (
                wandb_callback
                and wandb_callback.sequence_completion_stats["total_chunks_processed"]
                > 0
            ):
                final_completion_rate = (
                    wandb_callback.sequence_completion_stats["complete_sequences"]
                    / wandb_callback.sequence_completion_stats["total_chunks_processed"]
                )
                final_ending_rate = (
                    wandb_callback.sequence_completion_stats["chunks_with_endings"]
                    / wandb_callback.sequence_completion_stats["total_chunks_processed"]
                )

                wandb.log(
                    {
                        "final/sequence_completion_rate": final_completion_rate,
                        "final/ending_preservation_rate": final_ending_rate,
                        "final/total_complete_sequences": wandb_callback.sequence_completion_stats[
                            "complete_sequences"
                        ],
                        "final/total_chunks_with_endings": wandb_callback.sequence_completion_stats[
                            "chunks_with_endings"
                        ],
                        "final/total_chunks_processed": wandb_callback.sequence_completion_stats[
                            "total_chunks_processed"
                        ],
                    }
                )

            # Log training summary
            print(f"📊 Training Summary:")
            print(f"   ✅ Training completed successfully!")
            print(f"   ⏱️  Total time: {training_time/3600:.2f} hours")
            print(
                f"   💾 Final memory usage: {final_memory_info['usage_percent']:.1f}%"
            )
            print(f"   🔧 Sequence chunking: 9064 → 4096 tokens")

            # Log sequence completion statistics
            if (
                wandb_callback
                and wandb_callback.sequence_completion_stats["total_chunks_processed"]
                > 0
            ):
                print(f"   📝 Sequence completion rate: {final_completion_rate:.2%}")
                print(f"   🎯 Ending preservation rate: {final_ending_rate:.2%}")
                print(
                    f"   📊 Total chunks processed: {wandb_callback.sequence_completion_stats['total_chunks_processed']}"
                )

            print(f"   📈 WandB run: https://wandb.ai/junkim/llama-translation")

            wandb.finish()

    except Exception as e:
        print(f"❌ Process {rank}: Error during training: {e}")
        traceback.print_exc()
        raise e
    finally:
        try:
            cleanup()
        except Exception as cleanup_error:
            print(f"Process {rank}: Error during cleanup: {cleanup_error}")


def launch_training():
    """Launch DeepSpeed training across multiple GPUs using DeepSpeed launcher"""

    print("🚀 Please use DeepSpeed launcher to run this script:")
    print("=" * 70)
    print("Command: deepspeed --num_gpus=4 train.py")
    print("=" * 70)
    print()
    print("This script is designed to work with DeepSpeed launcher only.")
    print("Using mp.spawn with DeepSpeed causes device placement conflicts.")
    print()
    print("If you want to run without DeepSpeed launcher, please:")
    print("1. Remove DeepSpeed configuration from TrainingArguments")
    print("2. Use standard PyTorch DDP instead")
    print()

    # Validate CUDA availability
    if not torch.cuda.is_available():
        raise RuntimeError("CUDA is not available. This script requires GPU support.")

    # Get number of available GPUs
    world_size = torch.cuda.device_count()
    print(f"📍 Available GPUs: {world_size}")

    # Validate GPU memory
    print(f"\n🔍 GPU Memory Validation:")
    total_memory = 0
    for i in range(world_size):
        if torch.cuda.is_available() and i < torch.cuda.device_count():
            props = torch.cuda.get_device_properties(i)
            memory_gb = props.total_memory / 1024**3
            total_memory += memory_gb
            print(f"   GPU {i}: {props.name} - {memory_gb:.1f}GB")

    print(f"   Total GPU Memory: {total_memory:.1f}GB")

    # Estimate memory requirements for 9k sequences
    estimated_memory_per_gpu = 20  # GB estimate for 9k sequences with LoRA
    if total_memory / world_size < estimated_memory_per_gpu:
        print(
            f"⚠️  Warning: Each GPU has {total_memory/world_size:.1f}GB, but ~{estimated_memory_per_gpu}GB recommended for 9k sequences"
        )
    else:
        print(
            f"✅ Memory validation passed: {total_memory/world_size:.1f}GB per GPU >= {estimated_memory_per_gpu}GB required"
        )

    print(f"\n📊 Training Configuration:")
    print(f"   Dataset: junkim100/multilingual_instruction_tuning_lima_bactrian")
    print(f"   Model: meta-llama/Meta-Llama-3.1-8B")
    print(f"   Languages: German, Spanish, French, Italian")
    print(f"   Max Sequence Length: 9,064 tokens (with chunking)")
    print(f"   Chunking Strategy: 4096 tokens per chunk, 512 token overlap")
    print(f"   Optimization: LoRA + DeepSpeed ZeRO Stage 3 + Sequence Chunking")

    print(f"\n🚀 To start training, run:")
    print(f"   deepspeed --num_gpus={world_size} train.py")
    print(f"   # Or to use specific GPUs (e.g., 1,2,3,5):")
    print(f"   deepspeed --include localhost:1,2,3,5 train.py")

    return


# Launch training
if __name__ == "__main__":
    # Check if launched by DeepSpeed launcher
    import sys

    if "--local_rank" in sys.argv or "LOCAL_RANK" in os.environ:
        # Launched by DeepSpeed launcher - run training directly
        print("🚀 Detected DeepSpeed launcher - running training directly")

        # Get local rank from command line or environment
        local_rank = None
        for i, arg in enumerate(sys.argv):
            if arg == "--local_rank" and i + 1 < len(sys.argv):
                local_rank = int(sys.argv[i + 1])
                break

        if local_rank is None:
            local_rank = int(os.environ.get("LOCAL_RANK", 0))

        world_size = int(os.environ.get("WORLD_SIZE", 4))

        # Run training directly
        train_model(
            rank=local_rank,
            world_size=world_size,
            dataset_name="junkim100/multilingual_instruction_tuning_lima_bactrian",
            model_name="meta-llama/Meta-Llama-3.1-8B",
            supported_languages=["de", "es", "fr", "it"],
        )
    else:
        # Not launched by DeepSpeed - use mp.spawn
        launch_training()
