#!/usr/bin/env python3
"""
Validation script to test sequence completion fixes in the training pipeline
"""

import torch
from transformers import AutoTokenizer
from datasets import load_dataset
import sys
import os

# Add current directory to path
sys.path.append(".")
from train import (
    setup_custom_chat_template, 
    format_multilingual_chat_template,
    ChunkedSequenceDataCollator
)

def test_sequence_completion_validation():
    """Test the sequence completion validation and chunking fixes"""
    
    print("🧪 Testing Sequence Completion Validation")
    print("=" * 60)
    
    # Load tokenizer and setup
    model_name = "meta-llama/Meta-Llama-3.1-8B"
    max_length = 9064
    
    print(f"📂 Loading tokenizer: {model_name}")
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        model_max_length=max_length,
        padding_side="right",
        truncation_side="right",
        use_fast=True,
    )
    
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Setup custom chat template
    tokenizer = setup_custom_chat_template(tokenizer)
    print(f"✅ Tokenizer setup complete with vocab size: {len(tokenizer)}")
    
    # Test translation tokens
    translation_tokens = [
        "<in_translation>", "</in_translation>", 
        "<out_translation>", "</out_translation>"
    ]
    
    print(f"\n🔍 Checking translation tokens:")
    for token in translation_tokens:
        if token in tokenizer.get_vocab():
            token_id = tokenizer.convert_tokens_to_ids(token)
            print(f"   ✅ {token} -> ID: {token_id}")
        else:
            print(f"   ❌ {token} -> NOT FOUND")
    
    # Load a small sample of the dataset
    print(f"\n📊 Loading dataset sample...")
    try:
        dataset = load_dataset("junkim100/multilingual_instruction_tuning_lima_bactrian", "combined")
        print(f"✅ Dataset loaded successfully")
        
        # Take first few examples for testing
        sample_size = 3
        sample_data = {
            key: dataset["train"][key][:sample_size] 
            for key in dataset["train"].column_names
        }
        
        print(f"📝 Testing with {sample_size} samples...")
        
        # Format with chat template
        formatted = format_multilingual_chat_template(
            sample_data, tokenizer, ["de", "es", "fr", "it"]
        )
        
        print(f"✅ Formatted {len(formatted['text'])} sequences")
        
        # Test each formatted sequence
        for i, text in enumerate(formatted["text"]):
            print(f"\n--- Sample {i+1} ---")
            
            # Check for complete format
            has_complete_format = all(token in text for token in translation_tokens)
            print(f"Complete format: {'✅' if has_complete_format else '❌'}")
            
            # Check sequence length
            tokens = tokenizer(text, add_special_tokens=False)["input_ids"]
            print(f"Token length: {len(tokens)}")
            
            # Check ending
            if text.endswith(tokenizer.eos_token):
                ending_check = text[:-len(tokenizer.eos_token)] if tokenizer.eos_token else text
                has_proper_ending = "</out_translation>" in ending_check[-100:]
                print(f"Proper ending: {'✅' if has_proper_ending else '❌'}")
            else:
                print(f"EOS token: ❌")
            
            # Show text preview
            print(f"Text preview (last 200 chars): ...{text[-200:]}")
            
            # Test chunking if sequence is long
            if len(tokens) > 4096:
                print(f"\n🔧 Testing chunking for long sequence...")
                
                # Create data collator
                data_collator = ChunkedSequenceDataCollator(
                    tokenizer=tokenizer,
                    max_length=max_length,
                    chunk_size=4096,
                    overlap=512
                )
                
                # Test validation
                is_complete, completion_info = data_collator._validate_sequence_completion(tokens)
                print(f"Validation result: {'✅' if is_complete else '❌'}")
                
                if not is_complete:
                    missing = [k for k, v in completion_info.items() if k.startswith('has_') and not v]
                    print(f"Missing tokens: {missing}")
                
                # Test chunking
                labels = tokens.copy()  # Simple copy for testing
                chunks = data_collator._create_chunks(tokens, labels)
                print(f"Number of chunks: {len(chunks)}")
                
                # Check if final chunk contains ending
                if chunks:
                    final_chunk_text = tokenizer.decode(chunks[-1][0], skip_special_tokens=False)
                    has_ending_in_final = "</out_translation>" in final_chunk_text
                    print(f"Final chunk has ending: {'✅' if has_ending_in_final else '❌'}")
                    
                    if has_ending_in_final:
                        print(f"Final chunk preview: ...{final_chunk_text[-100:]}")
        
        print(f"\n✅ Sequence completion validation test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_smart_truncation():
    """Test the smart truncation functionality"""
    
    print(f"\n🧪 Testing Smart Truncation")
    print("=" * 60)
    
    # Create a very long text that would exceed max_length
    long_text = (
        "<|start_header_id|>user<|end_header_id|>\n\n"
        "This is a very long input that will exceed the maximum token length. " * 200 +
        "<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n<in_translation>"
        "This is the English translation of the input. " * 50 +
        "</in_translation>\n\n"
        "This is the middle content with reasoning and explanation. " * 100 +
        "\n\n<out_translation>"
        "This is the final translation output that must be preserved."
        "</out_translation><|end_of_text|>"
    )
    
    # Load tokenizer
    model_name = "meta-llama/Meta-Llama-3.1-8B"
    tokenizer = AutoTokenizer.from_pretrained(model_name, use_fast=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Setup custom chat template
    tokenizer = setup_custom_chat_template(tokenizer)
    
    # Test tokenization
    tokens = tokenizer(long_text, add_special_tokens=False)["input_ids"]
    print(f"Original length: {len(tokens)} tokens")
    
    max_length = 9064
    if len(tokens) > max_length:
        print(f"⚠️  Sequence exceeds max_length ({max_length}), testing smart truncation...")
        
        # Test smart truncation logic
        out_translation_end = "</out_translation>"
        eos_token = tokenizer.eos_token
        
        end_marker_pos = long_text.rfind(out_translation_end)
        if end_marker_pos != -1:
            print(f"✅ Found </out_translation> at position {end_marker_pos}")
            
            # Calculate preserved ending
            ending_part = long_text[end_marker_pos:]
            ending_tokens = tokenizer(ending_part + eos_token, add_special_tokens=False)["input_ids"]
            
            max_start_tokens = max_length - len(ending_tokens) - 50  # 50 token buffer
            print(f"📊 Ending tokens: {len(ending_tokens)}, Max start tokens: {max_start_tokens}")
            
            if max_start_tokens > 0:
                start_part = long_text[:end_marker_pos]
                start_tokens = tokenizer(start_part, add_special_tokens=False)["input_ids"]
                
                if len(start_tokens) > max_start_tokens:
                    truncated_start_tokens = start_tokens[:max_start_tokens]
                    truncated_start_text = tokenizer.decode(truncated_start_tokens, skip_special_tokens=True)
                    
                    # Reconstruct
                    reconstructed_text = truncated_start_text + ending_part
                    final_tokens = tokenizer(reconstructed_text, add_special_tokens=False)["input_ids"]
                    
                    print(f"✅ Smart truncation successful!")
                    print(f"   Final length: {len(final_tokens)} tokens")
                    print(f"   Preserved ending: {'✅' if '</out_translation>' in reconstructed_text else '❌'}")
                    print(f"   Text ending: ...{reconstructed_text[-100:]}")
                    
                    return True
    
    print(f"❌ Smart truncation test failed or not needed")
    return False

def main():
    """Main test function"""
    print("🤖 Sequence Completion Validation Test Suite")
    print("=" * 80)
    
    test1_result = test_sequence_completion_validation()
    test2_result = test_smart_truncation()
    
    print(f"\n" + "=" * 80)
    print("📋 TEST RESULTS")
    print("=" * 80)
    print(f"Sequence Completion Validation: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Smart Truncation Test: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 All tests passed! The sequence completion fixes should work correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Please review the implementation.")

if __name__ == "__main__":
    main()
